<template>
  <div class="subscription-management">
    <div class="page-header box qk-radius">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">套餐管理</h1>
          <p class="page-description">管理您的订阅套餐，查看使用情况和账单信息</p>
        </div>
        <div class="header-status">
          <div class="status-badge" :class="`status--${currentPlan.status}`">
            <i :class="statusIcon"></i>
            {{ statusText }}
          </div>
        </div>
      </div>
    </div>

    <!-- 当前套餐概览 -->
    <div class="current-plan-section box qk-radius">
      <div class="section-header">
        <h2>当前套餐</h2>
        <div class="plan-actions">
          <button 
            v-if="canUpgrade"
            @click="showUpgradeModal = true"
            class="btn btn-primary"
          >
            <i class="ri-arrow-up-line"></i>
            升级套餐
          </button>
          <button 
            v-if="canDowngrade"
            @click="showDowngradeModal = true"
            class="btn btn-outline"
          >
            <i class="ri-arrow-down-line"></i>
            降级套餐
          </button>
          <button 
            @click="showRenewalModal = true"
            class="btn btn-outline"
          >
            <i class="ri-refresh-line"></i>
            续费
          </button>
        </div>
      </div>

      <div class="plan-overview">
        <div class="plan-info">
          <div class="plan-details">
            <div class="plan-name">{{ currentPlan.name }}</div>
            <div class="plan-type" :class="`type--${currentPlan.type}`">
              {{ planTypeText }}
            </div>
            <div class="plan-duration">
              <i class="ri-calendar-line"></i>
              {{ formatDate(currentPlan.startDate) }} - 
              {{ currentPlan.endDate ? formatDate(currentPlan.endDate) : '永久' }}
            </div>
            <div class="auto-renew" :class="{ active: currentPlan.autoRenew }">
              <i :class="currentPlan.autoRenew ? 'ri-refresh-line' : 'ri-pause-line'"></i>
              {{ currentPlan.autoRenew ? '自动续费已开启' : '自动续费已关闭' }}
            </div>
          </div>

          <div class="plan-pricing">
            <div class="current-price">
              <div class="price-amount">￥{{ currentPlan.pricing.monthly }}/月</div>
              <div class="price-annual">年付: ￥{{ currentPlan.pricing.annual }}/年</div>
            </div>
          </div>
        </div>

        <!-- 使用量进度条 -->
        <div class="usage-overview">
          <div class="usage-item">
            <div class="usage-header">
              <span>API调用量</span>
              <span class="usage-text">
                {{ formatNumber(currentUsage.requests) }} / {{ formatNumber(currentPlan.limits.monthlyRequests) }}
              </span>
            </div>
            <div class="usage-bar">
              <div 
                class="usage-fill" 
                :style="{ width: `${usagePercentage.requests}%` }"
                :class="{ 
                  'warning': usagePercentage.requests > 80, 
                  'danger': usagePercentage.requests > 95 
                }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 可用套餐 -->
    <div class="available-plans-section box qk-radius">
      <div class="section-header">
        <h2>可选套餐</h2>
        <div class="billing-toggle">
          <label class="toggle-item" :class="{ active: billingCycle === 'monthly' }">
            <input 
              v-model="billingCycle" 
              type="radio" 
              value="monthly"
              hidden
            >
            月付
          </label>
          <label class="toggle-item" :class="{ active: billingCycle === 'annual' }">
            <input 
              v-model="billingCycle" 
              type="radio" 
              value="annual"
              hidden
            >
            年付
            <span class="discount-badge">省20%</span>
          </label>
        </div>
      </div>

      <!-- 使用新的VIP卡片设计 -->
      <div class="vip-list">
        <div 
          v-for="plan in mockPlans"
          :key="plan.id"
          class="vip-item"
          :class="{ 
            'current': plan.id === currentPlan.id,
            'recommended': plan.type === 'pro'
          }"
        >
          <!-- 限时倒计时（仅对推荐套餐显示） -->
          <div v-if="plan.type === 'pro'" class="timedown-box">
            <div class="time-countdown-box">
              <span class="time">4</span>
              <span class="time">01</span>
              <span class="time">15</span>
              <span class="time ms">531</span>
            </div>
          </div>

          <h2>{{ plan.name }}</h2>
          <div class="subtitle" style="margin-top: 8px; color: var(--color-text-secondary);">
            {{ plan.description }}
          </div>

          <div class="vip-price">
            <span class="unit">￥</span>
            <span class="num">{{ plan.pricing.monthly.toFixed(2) }}</span>
            <span>元</span>
            <div style="display: inline-block; color: var(--color-text-secondary);">
              /{{ billingCycle === 'monthly' ? '1个月起' : '1年起' }}
            </div>
            <div v-if="plan.discount" class="badge gradient">
              限时 {{ plan.discount }}折
            </div>
          </div>

          <div v-if="plan.originalPrice && plan.pricing.monthly < plan.originalPrice" class="original-price">
            <span class="num">￥{{ plan.originalPrice.toFixed(2) }} /1个月起</span>
          </div>

          <div class="pay-button" style="padding: 0px; margin-top: 16px;">
            <button 
              class="modern-btn" 
              @click="selectPlan(plan, isPlanUpgrade(plan) ? 'upgrade' : isPlanDowngrade(plan) ? 'downgrade' : 'change')"
              :disabled="plan.id === currentPlan.id"
            >
              <div class="btn-icon" :class="{ disabled: plan.id === currentPlan.id }">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 1024 1024"
                  height="20px"
                  width="20px"
                >
                  <path
                    d="M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64z"
                    :fill="plan.id === currentPlan.id ? '#9ca3af' : '#ffffff'"
                  ></path>
                  <path
                    d="m237.248 512 265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312L237.248 512z"
                    :fill="plan.id === currentPlan.id ? '#9ca3af' : '#ffffff'"
                  ></path>
                </svg>
              </div>
              <p class="btn-text">
                {{ plan.id === currentPlan.id ? '当前套餐' : '立即开通' }}
              </p>
            </button>
          </div>

          <div class="rights">
            <ul class="rights-list" style="margin: 0px 10%;">
              <li>
                <span class="icon bg-text">
                  <i class="ri-check-line"></i>
                </span>
                <span>API调用量 <b>{{ formatNumber(plan.limits.monthlyRequests) }}</b> 次/月</span>
              </li>
              <li v-for="feature in plan.features" :key="feature">
                <span class="icon bg-text">
                  <i class="ri-check-line"></i>
                </span>
                <span v-html="feature"></span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 套餐变更确认模态框 -->
    <div v-if="showPlanChangeModal" class="modal-overlay" @click.self="showPlanChangeModal = false">
      <div class="modal-content">
        <div class="modal-header">
          <h3>确认{{ changeAction }}套餐</h3>
          <button @click="showPlanChangeModal = false" class="modal-close">
            <i class="ri-close-line"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="plan-comparison">
            <div class="comparison-item">
              <div class="label">当前套餐</div>
              <div class="value">{{ currentPlan.name }}</div>
            </div>
            <div class="comparison-arrow">
              <i class="ri-arrow-right-line"></i>
            </div>
            <div class="comparison-item">
              <div class="label">目标套餐</div>
              <div class="value">{{ selectedPlan?.name }}</div>
            </div>
          </div>

          <div class="change-details">
            <div class="detail-item">
              <span>计费周期:</span>
              <span>{{ billingCycle === 'monthly' ? '月付' : '年付' }}</span>
            </div>
            <div class="detail-item">
              <span>费用变化:</span>
              <span class="price-change" :class="{ 
                'increase': priceChange > 0,
                'decrease': priceChange < 0
              }">
                {{ priceChange > 0 ? '+' : '' }}￥{{ Math.abs(priceChange) }}
              </span>
            </div>
            <div class="detail-item">
              <span>生效时间:</span>
              <span>立即生效</span>
            </div>
          </div>

          <div class="billing-cycle-selector">
            <label>
              <input 
                v-model="billingCycle"
                type="radio" 
                value="monthly"
              >
              <span class="radio-custom"></span>
              月付 - ￥{{ selectedPlan?.pricing.monthly }}/月
            </label>
            <label>
              <input 
                v-model="billingCycle"
                type="radio" 
                value="annual"
              >
              <span class="radio-custom"></span>
              年付 - ￥{{ selectedPlan?.pricing.annual }}/年
              <span class="discount">节省 ￥{{ Math.round(selectedPlan?.pricing.monthly * 12 - selectedPlan?.pricing.annual) }}</span>
            </label>
          </div>
        </div>
        
        <div class="modal-footer">
          <button 
            @click="showPlanChangeModal = false" 
            class="btn btn-outline"
          >
            取消
          </button>
          <button 
            @click="confirmPlanChange" 
            class="btn btn-primary"
            :disabled="changingPlan"
          >
            <i v-if="changingPlan" class="ri-loader-line spinning"></i>
            <i v-else class="ri-check-line"></i>
            {{ changingPlan ? '处理中...' : '确认' + changeAction }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'
import { useDashboardStore } from '@/stores/dashboard'
import type { SubscriptionPlan, PlanType, PlanStatus } from '@/types/dashboard'

const billingCycle = ref<'monthly' | 'annual'>('monthly')
const showPlanChangeModal = ref(false)
const showUpgradeModal = ref(false)
const showDowngradeModal = ref(false)
const showRenewalModal = ref(false)
const changingPlan = ref(false)

const selectedPlan = ref<any>(null)
const changeAction = ref<'升级' | '降级' | '切换'>('升级')

// 模拟套餐数据
const mockPlans = ref([
  {
    id: '1',
    name: '赞助会员',
    type: 'basic',
    description: '适用于个人网站爱好者',
    pricing: { monthly: 25.00, annual: 250.00 },
    limits: { monthlyRequests: 10000 },
    features: [
      '查看所有隐藏内容 <b>100</b> 次/日',
      '下载所有资源 <b>10</b> 次/日',
      '观看所有视频 <b>20</b> 次/日',
      '签到额外奖励 <b>1-5</b> 经验/日',
      '免广告'
    ]
  },
  {
    id: '2',
    name: '高级会员',
    type: 'pro',
    description: '适用于专业团队合作网站',
    pricing: { monthly: 28.00, annual: 280.00 },
    originalPrice: 35.00,
    discount: 7.9,
    limits: { monthlyRequests: 50000 },
    features: [
      '查看所有隐藏内容 <b>50</b> 次/日',
      '下载所有资源 <b>30</b> 次/日',
      '观看所有视频 <b>不限</b> 次/日',
      '签到额外奖励 <b>5-10</b> 积分/日',
      '签到额外奖励 <b>15</b> 经验/日',
      '免广告'
    ]
  },
  {
    id: '3',
    name: '专业会员',
    type: 'enterprise',
    description: '适合企业网站',
    pricing: { monthly: 50.00, annual: 500.00 },
    limits: { monthlyRequests: 100000 },
    features: [
      '查看所有隐藏内容 <b>不限</b> 次/日',
      '下载所有资源 <b>40</b> 次/日',
      '观看所有视频 <b>不限</b> 次/日',
      '签到额外奖励 <b>30</b> 积分/日',
      '签到额外奖励 <b>40</b> 经验/日',
      '免广告'
    ]
  }
])

const currentPlan = computed(() => ({
  id: '1',
  name: '赞助会员',
  type: 'basic' as PlanType,
  status: 'active' as PlanStatus,
  features: [],
  limits: { monthlyRequests: 10000 },
  pricing: { monthly: 25.00, annual: 250.00 },
  startDate: new Date(),
  endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
  autoRenew: false
}))

const availablePlans = computed(() => mockPlans.value)

const currentUsage = computed(() => ({
  requests: 2500
}))

const usagePercentage = computed(() => ({
  requests: Math.min(100, (currentUsage.value.requests / currentPlan.value.limits.monthlyRequests) * 100)
}))

const statusIcon = computed(() => {
  const icons = {
    active: 'ri-check-line',
    inactive: 'ri-pause-line',
    expired: 'ri-time-line',
    cancelled: 'ri-close-line'
  }
  return icons[currentPlan.value.status] || 'ri-question-line'
})

const statusText = computed(() => {
  const texts = {
    active: '活跃中',
    inactive: '未激活',
    expired: '已过期',
    cancelled: '已取消'
  }
  return texts[currentPlan.value.status] || currentPlan.value.status
})

const planTypeText = computed(() => {
  const texts = {
    free: '免费版',
    basic: '基础版',
    pro: '专业版',
    enterprise: '企业版'
  }
  return texts[currentPlan.value.type] || currentPlan.value.type
})

const canUpgrade = computed(() => 
  currentPlan.value.type !== 'enterprise' && currentPlan.value.status === 'active'
)

const canDowngrade = computed(() => 
  currentPlan.value.type !== 'free' && currentPlan.value.status === 'active'
)

const priceChange = computed(() => {
  if (!selectedPlan.value) return 0
  const currentPrice = billingCycle.value === 'monthly' 
    ? currentPlan.value.pricing.monthly 
    : currentPlan.value.pricing.annual / 12
  const newPrice = billingCycle.value === 'monthly'
    ? selectedPlan.value.pricing.monthly
    : selectedPlan.value.pricing.annual / 12
  
  return newPrice - currentPrice
})

const planOrder: Record<string, number> = { free: 0, basic: 1, pro: 2, enterprise: 3 }

const isPlanUpgrade = (plan: any): boolean => {
  return planOrder[plan.type] > planOrder[currentPlan.value?.type || 'free']
}

const isPlanDowngrade = (plan: any): boolean => {
  return planOrder[plan.type] < planOrder[currentPlan.value?.type || 'free'] && plan.type !== 'free'
}

const selectPlan = (plan: any, action: 'upgrade' | 'downgrade' | 'change') => {
  selectedPlan.value = plan
  changeAction.value = action === 'upgrade' ? '升级' : action === 'downgrade' ? '降级' : '切换'
  showPlanChangeModal.value = true
}

const confirmPlanChange = async () => {
  if (!selectedPlan.value) return

  changingPlan.value = true
  try {
    // 模拟套餐变更
    console.log('Changing plan to:', selectedPlan.value.name)
    await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟网络请求
    showPlanChangeModal.value = false
    selectedPlan.value = null
    alert('套餐变更成功！')
  } catch (error) {
    console.error('Failed to change plan:', error)
    alert('套餐变更失败，请重试。')
  } finally {
    changingPlan.value = false
  }
}

const downloadInvoice = async (url: string) => {
  window.open(url, '_blank')
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
  return num.toString()
}

const formatDate = (date: Date): string => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

onMounted(async () => {
  console.log('Subscription view initialized with mock data')
})
</script>

<style scoped>
.subscription-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.page-header {
  padding: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.page-description {
  color: var(--color-text-secondary);
  margin: 0;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.status-badge.status--active {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.status-badge.status--inactive {
  background: rgba(156, 163, 175, 0.1);
  color: #9ca3af;
}

.status-badge.status--expired,
.status-badge.status--cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.current-plan-section,
.available-plans-section {
  padding: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.plan-actions {
  display: flex;
  gap: 12px;
}

.btn {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  border: 1px solid transparent;
  border-radius: var(--radius);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  gap: 8px;
}

.btn-primary {
  background: var(--theme-color);
  color: white;
}

.btn-primary:hover {
  background: color-mix(in srgb, var(--theme-color) 90%, black);
}

.btn-outline {
  background: white;
  border-color: var(--border-color-base);
  color: var(--color-text-primary);
}

.btn-outline:hover {
  border-color: var(--theme-color);
  color: var(--theme-color);
}

.btn-current {
  background: var(--bg-muted-color);
  color: var(--color-text-secondary);
  cursor: not-allowed;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.plan-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.plan-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.plan-name {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.plan-type {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 12px;
}

.plan-type.type--free {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.plan-type.type--basic {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.plan-type.type--pro {
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.plan-type.type--enterprise {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.plan-duration,
.auto-renew {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
}

.auto-renew.active {
  color: #22c55e;
}

.current-price {
  text-align: right;
}

.price-amount {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.price-annual {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.usage-overview {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.usage-item {
  background: var(--bg-muted-color);
  padding: 16px;
  border-radius: var(--radius);
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.usage-bar {
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: var(--theme-color);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.usage-fill.warning {
  background: #f59e0b;
}

.usage-fill.danger {
  background: #ef4444;
}

.usage-note {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: 8px;
}

.billing-toggle {
  display: flex;
  background: var(--bg-muted-color);
  border-radius: 8px;
  padding: 4px;
}

.toggle-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 600;
  font-size: 14px;
}

.toggle-item.active {
  background: white;
  color: var(--theme-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.discount-badge {
  background: #22c55e;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 4px;
}

/* VIP 卡片样式 */
.vip-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.vip-item {
  position: relative;
  background: white;
  border: 2px solid var(--border-color-base);
  border-radius: var(--radius);
  padding: 32px 24px;
  text-align: center;
  transition: all 0.3s ease;
  overflow: hidden;
}

.vip-item:hover {
  border-color: var(--theme-color);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.vip-item.current {
  border-color: #22c55e;
  background: rgba(34, 197, 94, 0.02);
}

.vip-item.recommended {
  border-color: var(--theme-color);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
}

.vip-item.recommended::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--theme-color), #a855f7);
}

.timedown-box {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
}

.time-countdown-box {
  display: flex;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  color: #ef4444;
}

.time {
  background: #ef4444;
  color: white;
  padding: 2px 4px;
  border-radius: 4px;
  min-width: 20px;
}

.time.ms {
  background: rgba(239, 68, 68, 0.7);
}

.vip-item h2 {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 16px 0 8px 0;
}

.subtitle {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 24px;
}

.vip-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin: 24px 0;
  position: relative;
  flex-wrap: wrap;
}

.vip-price .unit {
  font-size: 18px;
  color: var(--color-text-secondary);
  font-weight: 600;
}

.vip-price .num {
  font-size: 42px;
  font-weight: 700;
  color: var(--theme-color);
  line-height: 1;
}

.vip-price > span:last-of-type {
  font-size: 18px;
  color: var(--color-text-secondary);
  font-weight: 600;
}

.badge.gradient {
  position: absolute;
  top: -8px;
  right: -20px;
  background: linear-gradient(135deg, #ff6b6b, #ffd93d);
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.original-price {
  text-align: center;
  margin-top: -8px;
  margin-bottom: 16px;
}

.original-price .num {
  font-size: 14px;
  color: var(--color-text-secondary);
  text-decoration: line-through;
}

.pay-button {
  margin: 24px 0;
  display: flex;
  justify-content: center;
}

.modern-btn {
  background: white;
  text-align: center;
  width: 200px;
  border-radius: 16px;
  height: 56px;
  position: relative;
  color: black;
  font-size: 16px;
  font-weight: 600;
  border: 2px solid var(--border-color-base);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.modern-btn:hover:not(:disabled) {
  border-color: var(--theme-color);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.modern-btn:disabled {
  background: #f3f4f6;
  border-color: #e5e7eb;
  cursor: not-allowed;
  color: #9ca3af;
}

.btn-icon {
  background: var(--theme-color);
  border-radius: 12px;
  height: 48px;
  width: 25%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 4px;
  top: 4px;
  z-index: 10;
  transition: width 0.5s ease;
  box-sizing: border-box;
}

.btn-icon svg {
  display: block;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.btn-icon.disabled {
  background: #d1d5db;
}

.modern-btn:hover:not(:disabled) .btn-icon {
  width: calc(100% - 8px);
}

.btn-text {
  transform: translateX(8px);
  margin: 0;
  transition: all 0.3s ease;
  line-height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
}

.modern-btn:hover:not(:disabled) .btn-text {
  color: white;
  transform: translateX(0);
}

.rights {
  text-align: left;
}

.rights-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.rights-list li {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  font-size: 14px;
  color: var(--color-text-regular);
  line-height: 1.5;
}

.rights-list .icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--theme-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

.rights-list b {
  color: var(--theme-color);
  font-weight: 700;
}

/* 隐藏原有的 plans-grid，使用新的 vip-list 样式 */
.plans-grid {
  display: none;
}

.plan-card {
  position: relative;
  background: white;
  border: 2px solid var(--border-color-base);
  border-radius: var(--radius);
  padding: 24px;
  transition: all 0.2s;
}

.plan-card:hover {
  border-color: var(--theme-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.plan-card.current {
  border-color: #22c55e;
  background: rgba(34, 197, 94, 0.02);
}

.plan-card.recommended {
  border-color: var(--theme-color);
  background: var(--bg-text-color);
}

.recommended-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--theme-color);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.plan-header {
  margin-bottom: 24px;
  text-align: center;
}

.plan-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 16px;
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin-bottom: 8px;
}

.currency {
  font-size: 16px;
  color: var(--color-text-secondary);
}

.amount {
  font-size: 36px;
  font-weight: 700;
  color: var(--color-text-primary);
}

.period {
  font-size: 16px;
  color: var(--color-text-secondary);
}

.annual-note {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.plan-features {
  margin-bottom: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  font-size: 14px;
  color: var(--color-text-regular);
}

.feature-item i {
  color: #22c55e;
  font-size: 16px;
  flex-shrink: 0;
}

.plan-actions {
  margin-top: auto;
}

.plan-actions .btn {
  width: 100%;
  justify-content: center;
}

.btn-link {
  background: none;
  border: none;
  color: var(--theme-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.btn-link:hover {
  text-decoration: underline;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: var(--radius);
  width: 90vw;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color-base);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.modal-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
}

.modal-close:hover {
  color: var(--color-text-primary);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-color-base);
}

.plan-comparison {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--bg-muted-color);
  border-radius: var(--radius);
}

.comparison-item {
  flex: 1;
}

.comparison-item .label {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 4px;
}

.comparison-item .value {
  font-weight: 600;
  color: var(--color-text-primary);
}

.comparison-arrow {
  color: var(--theme-color);
  font-size: 20px;
}

.change-details {
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  font-size: 14px;
}

.detail-item span:first-child {
  color: var(--color-text-secondary);
}

.detail-item span:last-child {
  font-weight: 600;
  color: var(--color-text-primary);
}

.price-change.increase {
  color: #ef4444;
}

.price-change.decrease {
  color: #22c55e;
}

.billing-cycle-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.billing-cycle-selector label {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s;
}

.billing-cycle-selector label:hover {
  border-color: var(--theme-color);
}

.billing-cycle-selector input {
  display: none;
}

.radio-custom {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color-base);
  border-radius: 50%;
  position: relative;
  flex-shrink: 0;
}

.billing-cycle-selector input:checked + .radio-custom {
  border-color: var(--theme-color);
}

.billing-cycle-selector input:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--theme-color);
  border-radius: 50%;
}

.discount {
  font-size: 12px;
  color: #22c55e;
  margin-left: 8px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .plan-info {
    flex-direction: column;
    gap: 16px;
  }
  
  .current-price {
    text-align: left;
  }
  
  .plan-actions {
    flex-wrap: wrap;
  }
  
  .vip-list {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .vip-item {
    padding: 24px 20px;
  }
  
  .vip-item h2 {
    font-size: 20px;
  }
  
  .vip-price .num {
    font-size: 36px;
  }
  
  .modern-btn {
    width: 180px;
    height: 50px;
    font-size: 14px;
  }
  
  .btn-icon {
    height: 42px;
  }
  
  .btn-text {
    line-height: 50px;
  }
  
  .timedown-box {
    position: static;
    margin-bottom: 16px;
    display: inline-block;
  }
  
  .plan-comparison {
    flex-direction: column;
    text-align: center;
  }
  
  .comparison-arrow {
    transform: rotate(90deg);
  }
}
</style>