<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">管理后台诊断</h1>
    
    <div class="space-y-4">
      <div class="bg-white p-4 rounded shadow">
        <h2 class="font-semibold mb-2">认证状态</h2>
        <ul class="space-y-1 text-sm">
          <li>已初始化: {{ adminAuthStore.isInitialized }}</li>
          <li>已认证: {{ adminAuthStore.isAuthenticated }}</li>
          <li>用户名: {{ adminAuthStore.adminUser?.username || '未登录' }}</li>
          <li>角色: {{ adminAuthStore.adminUser?.role || '无' }}</li>
        </ul>
      </div>
      
      <div class="bg-white p-4 rounded shadow">
        <h2 class="font-semibold mb-2">测试组件加载</h2>
        <div class="space-y-2">
          <button @click="testDynamicImport" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            测试动态导入
          </button>
          <div v-if="importStatus" class="mt-2 text-sm">
            状态: {{ importStatus }}
          </div>
        </div>
      </div>
      
      <div class="bg-white p-4 rounded shadow">
        <h2 class="font-semibold mb-2">操作</h2>
        <div class="space-x-2">
          <button @click="clearCache" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
            清除缓存
          </button>
          <button @click="manualLogin" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
            手动登录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAdminAuthStore } from '@/stores/adminAuth'

const adminAuthStore = useAdminAuthStore()
const importStatus = ref('')

const testDynamicImport = async () => {
  importStatus.value = '正在测试...'
  try {
    const module = await import('@/views/admin/DashboardView.vue')
    importStatus.value = '✅ 动态导入成功'
    console.log('Imported module:', module)
  } catch (error) {
    importStatus.value = `❌ 动态导入失败: ${error}`
    console.error('Import error:', error)
  }
}

const clearCache = () => {
  localStorage.clear()
  sessionStorage.clear()
  location.reload()
}

const manualLogin = async () => {
  try {
    await adminAuthStore.login({
      username: 'superadmin',
      password: 'admin123'
    })
    alert('登录成功！')
  } catch (error) {
    alert('登录失败: ' + error)
  }
}
</script>