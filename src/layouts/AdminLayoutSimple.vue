<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-indigo-600 rounded flex items-center justify-center">
              <span class="text-white font-bold">X</span>
            </div>
            <h1 class="ml-3 text-xl font-semibold text-gray-900">
              管理后台
            </h1>
          </div>
          
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">
              {{ adminUser?.username || '管理员' }}
            </span>
            <button
              @click="handleLogout"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <div class="flex">
      <!-- Sidebar -->
      <aside class="w-64 bg-white shadow-md h-[calc(100vh-4rem)]">
        <nav class="mt-5 px-2">
          <router-link
            v-for="item in menuItems"
            :key="item.path"
            :to="item.path"
            class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
          >
            <div class="mr-3 flex-shrink-0 h-6 w-6">
              <!-- 使用简单的文字图标 -->
              <span class="text-lg">{{ item.icon }}</span>
            </div>
            {{ item.name }}
          </router-link>
        </nav>
      </aside>

      <!-- Page Content -->
      <main class="flex-1 overflow-y-auto">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAdminAuthStore } from '@/stores/adminAuth'

const router = useRouter()
const adminAuthStore = useAdminAuthStore()

const adminUser = computed(() => adminAuthStore.adminUser)

const menuItems = computed(() => {
  const items = [
    { path: '/admin', name: '仪表盘', icon: '📊' },
    { path: '/admin/users', name: '用户管理', icon: '👥' },
    { path: '/admin/api-keys', name: 'API密钥', icon: '🔑' },
    { path: '/admin/usage', name: '使用统计', icon: '📈' },
    { path: '/admin/subscriptions', name: '订阅管理', icon: '💳' },
  ]
  
  // 只有超级管理员可以看到系统配置和财务报表
  if (adminAuthStore.isSuperAdmin) {
    items.push(
      { path: '/admin/settings', name: '系统配置', icon: '⚙️' },
      { path: '/admin/finance', name: '财务报表', icon: '📑' }
    )
  }
  
  return items
})

const handleLogout = () => {
  adminAuthStore.logout()
  router.push('/admin/login')
}
</script>

<style scoped>
.router-link-active {
  @apply bg-gray-100 text-gray-900;
}
</style>