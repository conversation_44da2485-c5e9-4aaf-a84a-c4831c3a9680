<template>
  <div class="api-key-card" :class="{ 'api-key-card--compact': compact }">
    <div class="api-key-header">
      <div class="api-key-info">
        <div class="api-key-name">{{ apiKey.name }}</div>
        <div class="api-key-status" :class="`status--${apiKey.status}`">
          <i :class="statusIcon"></i>
          {{ statusText }}
        </div>
      </div>
      <div class="api-key-actions">
        <button 
          v-if="!compact"
          @click="copyKey"
          class="action-btn action-btn--copy"
          :disabled="copying"
        >
          <i :class="copying ? 'ri-check-line' : 'ri-file-copy-line'"></i>
        </button>
        <button 
          v-if="!compact"
          @click="toggleKey"
          class="action-btn"
          :class="apiKey.status === 'active' ? 'action-btn--disable' : 'action-btn--enable'"
        >
          <i :class="apiKey.status === 'active' ? 'ri-eye-off-line' : 'ri-eye-line'"></i>
        </button>
        <button 
          v-if="!compact"
          @click="deleteKey"
          class="action-btn action-btn--delete"
        >
          <i class="ri-delete-bin-line"></i>
        </button>
      </div>
    </div>
    
    <div v-if="!compact" class="api-key-content">
      <div class="api-key-field">
        <label>API密钥</label>
        <div class="key-display">
          <code class="api-key-value">{{ maskedKey }}</code>
          <button @click="toggleKeyVisibility" class="visibility-btn">
            <i :class="keyVisible ? 'ri-eye-off-line' : 'ri-eye-line'"></i>
          </button>
        </div>
      </div>
      
      <div class="api-key-meta">
        <div class="meta-item">
          <span class="meta-label">权限:</span>
          <div class="permissions">
            <span 
              v-for="permission in apiKey.permissions"
              :key="permission"
              class="permission-badge"
              :class="`permission--${permission}`"
            >
              {{ permissionText(permission) }}
            </span>
          </div>
        </div>
        
        <div class="meta-item">
          <span class="meta-label">创建时间:</span>
          <span class="meta-value">{{ formatDate(apiKey.createdAt) }}</span>
        </div>
        
        <div v-if="apiKey.lastUsed" class="meta-item">
          <span class="meta-label">最后使用:</span>
          <span class="meta-value">{{ formatDate(apiKey.lastUsed) }}</span>
        </div>
        
        <div class="meta-item">
          <span class="meta-label">使用次数:</span>
          <span class="meta-value">{{ formatNumber(apiKey.usageCount) }}</span>
        </div>
        
        <div class="meta-item">
          <span class="meta-label">限制:</span>
          <span class="meta-value">{{ apiKey.rateLimit }}/分钟</span>
        </div>
      </div>
    </div>
    
    <div v-else class="api-key-compact">
      <div class="compact-key">
        <code>{{ compactKey }}</code>
      </div>
      <div class="compact-meta">
        <span class="usage-count">{{ formatNumber(apiKey.usageCount) }}次调用</span>
        <span class="created-date">{{ formatRelativeDate(apiKey.createdAt) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { ApiKey, ApiKeyStatus, ApiKeyPermission } from '@/types/dashboard'

interface Props {
  apiKey: ApiKey
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  compact: false
})

const emit = defineEmits<{
  toggle: [id: string]
  delete: [id: string]
  copy: [key: string]
}>()

const keyVisible = ref(false)
const copying = ref(false)

const statusIcon = computed(() => {
  const icons = {
    active: 'ri-check-line',
    inactive: 'ri-pause-line',
    expired: 'ri-time-line'
  }
  return icons[props.apiKey.status] || 'ri-question-line'
})

const statusText = computed(() => {
  const texts = {
    active: '活跃',
    inactive: '已禁用',
    expired: '已过期'
  }
  return texts[props.apiKey.status] || props.apiKey.status
})

const maskedKey = computed(() => {
  if (keyVisible.value) return props.apiKey.key
  const key = props.apiKey.key
  return `${key.substring(0, 8)}${'*'.repeat(24)}${key.substring(key.length - 8)}`
})

const compactKey = computed(() => {
  const key = props.apiKey.key
  return `${key.substring(0, 8)}...${key.substring(key.length - 4)}`
})

const toggleKeyVisibility = () => {
  keyVisible.value = !keyVisible.value
}

const copyKey = async () => {
  try {
    copying.value = true
    await navigator.clipboard.writeText(props.apiKey.key)
    emit('copy', props.apiKey.key)
    
    setTimeout(() => {
      copying.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy key:', error)
    copying.value = false
  }
}

const toggleKey = () => {
  emit('toggle', props.apiKey.id)
}

const deleteKey = () => {
  if (confirm('确定要删除这个API密钥吗？此操作不可撤销。')) {
    emit('delete', props.apiKey.id)
  }
}

const permissionText = (permission: ApiKeyPermission): string => {
  const texts = {
    read: '读取',
    write: '写入',
    delete: '删除',
    admin: '管理员'
  }
  return texts[permission] || permission
}

const formatDate = (date: Date): string => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatRelativeDate = (date: Date): string => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天创建'
  if (days === 1) return '昨天创建'
  if (days < 7) return `${days}天前创建`
  if (days < 30) return `${Math.floor(days / 7)}周前创建`
  return `${Math.floor(days / 30)}个月前创建`
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
  return num.toString()
}
</script>

<style scoped>
.api-key-card {
  background: white;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  padding: 16px;
  transition: all 0.2s;
}

.api-key-card:hover {
  border-color: var(--theme-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.api-key-card--compact {
  padding: 12px;
}

.api-key-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.api-key-card--compact .api-key-header {
  margin-bottom: 8px;
}

.api-key-name {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.api-key-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  gap: 4px;
}

.status--active {
  color: #22c55e;
}

.status--inactive {
  color: #9ca3af;
}

.status--expired {
  color: #ef4444;
}

.api-key-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color-base);
  background: white;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--color-text-regular);
}

.action-btn:hover {
  border-color: var(--theme-color);
  color: var(--theme-color);
}

.action-btn--copy:hover {
  border-color: #22c55e;
  color: #22c55e;
}

.action-btn--delete:hover {
  border-color: #ef4444;
  color: #ef4444;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.api-key-field {
  margin-bottom: 16px;
}

.api-key-field label {
  display: block;
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
}

.key-display {
  display: flex;
  align-items: center;
  background: var(--bg-muted-color);
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  padding: 8px 12px;
}

.api-key-value {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: var(--color-text-primary);
  background: none;
  border: none;
}

.visibility-btn {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 4px;
  margin-left: 8px;
}

.visibility-btn:hover {
  color: var(--theme-color);
}

.api-key-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.meta-label {
  color: var(--color-text-secondary);
  min-width: 80px;
}

.meta-value {
  color: var(--color-text-primary);
}

.permissions {
  display: flex;
  gap: 6px;
}

.permission-badge {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.permission--read {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.permission--write {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.permission--delete {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.permission--admin {
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.api-key-compact {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.compact-key code {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: var(--color-text-primary);
  background: var(--bg-muted-color);
  padding: 4px 8px;
  border-radius: var(--radius);
}

.compact-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--color-text-secondary);
}
</style>