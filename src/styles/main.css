@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  html {
    font-family: 'Basier circle', system-ui, sans-serif;
  }
  
  body {
    @apply antialiased;
  }
}

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply px-6 py-3 border-2 border-blue-600 text-blue-600 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-lg p-6;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* 自定义工具类 */
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .bg-gradient-brand {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .shadow-brand {
    box-shadow: 0 10px 25px -3px rgba(102, 126, 234, 0.1), 0 4px 6px -2px rgba(102, 126, 234, 0.05);
  }
  
  /* 确保网格布局在首次加载时正确显示 */
  .stats-grid {
    display: grid !important;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1.5rem;
  }
  
  @media (min-width: 640px) {
    .stats-grid-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
    .stats-grid-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
  }
  
  @media (min-width: 1280px) {
    .stats-grid-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
    }
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.6s ease-out;
}

.animate-bounce-custom {
  animation: bounce 2s infinite;
}

/* 响应式设计增强 */
@media (max-width: 640px) {
  .container-custom {
    @apply px-4;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .card {
    @apply bg-gray-800 text-white;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
}

/* 可访问性增强 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply border-2 border-blue-800;
  }
  
  .btn-secondary {
    @apply border-2 border-blue-800;
  }
}
