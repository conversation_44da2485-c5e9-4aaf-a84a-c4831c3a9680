import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { dashboardApi } from '@/api'
import type { DashboardOverview, UsageChartData } from '@/types/dashboard'

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const overview = ref<DashboardOverview | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // 计算属性
  const hasData = computed(() => overview.value !== null)
  const usagePercentage = computed(() => {
    if (!overview.value) return 0
    return (overview.value.usage.monthlyUsage / 100000) * 100 // 假设月限额为10万
  })

  const billingTrend = computed(() => {
    if (!overview.value) return 0
    const current = overview.value.billing.currentMonthCost
    const last = overview.value.billing.lastMonthCost
    if (last === 0) return 0
    return ((current - last) / last) * 100
  })

  // 操作方法
  const fetchOverview = async () => {
    if (isLoading.value) return

    isLoading.value = true
    error.value = null

    try {
      const data = await dashboardApi.getOverview()
      overview.value = data
      lastUpdated.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取数据失败'
      console.error('Failed to fetch dashboard overview:', err)
    } finally {
      isLoading.value = false
    }
  }

  const refreshData = async () => {
    await fetchOverview()
  }

  // 清除数据
  const clearData = () => {
    overview.value = null
    error.value = null
    lastUpdated.value = null
  }

  // 更新特定字段
  const updateUsageStats = (stats: Partial<DashboardOverview['usage']>) => {
    if (overview.value) {
      overview.value.usage = { ...overview.value.usage, ...stats }
    }
  }

  const updateBillingInfo = (billing: Partial<DashboardOverview['billing']>) => {
    if (overview.value) {
      overview.value.billing = { ...overview.value.billing, ...billing }
    }
  }

  // 获取图表数据
  const getUsageChartData = async (period: number): Promise<UsageChartData> => {
    try {
      return await dashboardApi.getUsageChartData(period)
    } catch (err) {
      console.error('Failed to fetch chart data:', err)
      return []
    }
  }

  // 下载使用报告
  const downloadUsageReport = async (): Promise<void> => {
    try {
      await dashboardApi.downloadUsageReport()
    } catch (err) {
      console.error('Failed to download report:', err)
      throw err
    }
  }

  return {
    // 状态
    overview,
    isLoading,
    error,
    lastUpdated,
    
    // 计算属性
    hasData,
    usagePercentage,
    billingTrend,
    
    // 方法
    fetchOverview,
    refreshData,
    clearData,
    updateUsageStats,
    updateBillingInfo,
    getUsageChartData,
    downloadUsageReport
  }
})