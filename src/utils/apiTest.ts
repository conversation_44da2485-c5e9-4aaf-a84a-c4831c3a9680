// API 错误处理测试工具
import { apiClient } from '@/api/client'

// 测试网络错误处理
export const testNetworkError = async () => {
  try {
    await apiClient.get('/test/network-error')
  } catch (error) {
    console.log('Network error handled correctly:', error)
  }
}

// 测试认证错误处理
export const testAuthError = async () => {
  try {
    await apiClient.get('/test/auth-error')  
  } catch (error) {
    console.log('Auth error handled correctly:', error)
  }
}

// 测试一般错误处理
export const testGeneralError = async () => {
  try {
    await apiClient.get('/test/general-error')
  } catch (error) {
    console.log('General error handled correctly:', error)
  }
}