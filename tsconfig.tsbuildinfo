{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vite/types/customevent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importglob.d.ts", "./node_modules/vite/types/importmeta.d.ts", "./node_modules/vite/client.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/dist/vue.d.mts", "./env.d.ts", "./node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/vue-demi/lib/index.d.ts", "./node_modules/pinia/dist/pinia.d.ts", "./node_modules/vue-router/dist/vue-router.d.ts", "./src/stores/auth.ts", "./src/stores/adminauth.ts", "./src/router/guards/adminauth.ts", "./src/router/index.ts", "./src/main.ts", "./src/composables/usetoast.ts", "./src/utils/errorhandler.ts", "./src/api/client.ts", "./src/types/dashboard.ts", "./src/api/apikeys.ts", "./src/api/dashboard.ts", "./src/api/usage.ts", "./src/api/subscription.ts", "./src/api/index.ts", "./src/composables/useinteractions.ts", "./src/composables/usemenu.ts", "./src/composables/usesmoothscroll.ts", "./src/constants/index.ts", "./src/stores/apikeys.ts", "./src/stores/dashboard.ts", "./src/stores/subscription.ts", "./src/stores/usage.ts", "./src/types/index.ts", "./src/utils/apitest.ts", "./src/utils/index.ts"], "fileInfos": [{"version": "f33e5332b24c3773e930e212cbb8b6867c8ba3ec4492064ea78e55a524d57450", "affectsGlobalScope": true}, "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "26f2f787e82c4222710f3b676b4d83eb5ad0a72fa7b746f03449e7a026ce5073", "9a68c0c07ae2fa71b44384a839b7b8d81662a236d4b9ac30916718f7510b1b2d", "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", {"version": "21e41a76098aa7a191028256e52a726baafd45a925ea5cf0222eb430c96c1d83", "affectsGlobalScope": true}, {"version": "35299ae4a62086698444a5aaee27fc7aa377c68cbb90b441c9ace246ffd05c97", "affectsGlobalScope": true}, {"version": "138fb588d26538783b78d1e3b2c2cc12d55840b97bf5e08bca7f7a174fbe2f17", "affectsGlobalScope": true}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true}, {"version": "4443e68b35f3332f753eacc66a04ac1d2053b8b035a0e0ac1d455392b5e243b3", "affectsGlobalScope": true}, {"version": "bc47685641087c015972a3f072480889f0d6c65515f12bd85222f49a98952ed7", "affectsGlobalScope": true}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true}, {"version": "93495ff27b8746f55d19fcbcdbaccc99fd95f19d057aed1bd2c0cafe1335fbf0", "affectsGlobalScope": true}, {"version": "6fc23bb8c3965964be8c597310a2878b53a0306edb71d4b5a4dfe760186bcc01", "affectsGlobalScope": true}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true}, {"version": "bb42a7797d996412ecdc5b2787720de477103a0b2e53058569069a0e2bae6c7e", "affectsGlobalScope": true}, {"version": "4738f2420687fd85629c9efb470793bb753709c2379e5f85bc1815d875ceadcd", "affectsGlobalScope": true}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true}, {"version": "9fc46429fbe091ac5ad2608c657201eb68b6f1b8341bd6d670047d32ed0a88fa", "affectsGlobalScope": true}, {"version": "61c37c1de663cf4171e1192466e52c7a382afa58da01b1dc75058f032ddf0839", "affectsGlobalScope": true}, {"version": "b541a838a13f9234aba650a825393ffc2292dc0fc87681a5d81ef0c96d281e7a", "affectsGlobalScope": true}, {"version": "e0275cd0e42990dc3a16f0b7c8bca3efe87f1c8ad404f80c6db1c7c0b828c59f", "affectsGlobalScope": true}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true}, {"version": "49ed889be54031e1044af0ad2c603d627b8bda8b50c1a68435fe85583901d072", "affectsGlobalScope": true}, {"version": "e93d098658ce4f0c8a0779e6cab91d0259efb88a318137f686ad76f8410ca270", "affectsGlobalScope": true}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true}, {"version": "bf14a426dbbf1022d11bd08d6b8e709a2e9d246f0c6c1032f3b2edb9a902adbe", "affectsGlobalScope": true}, {"version": "ec0104fee478075cb5171e5f4e3f23add8e02d845ae0165bfa3f1099241fa2aa", "affectsGlobalScope": true}, {"version": "2b72d528b2e2fe3c57889ca7baef5e13a56c957b946906d03767c642f386bbc3", "affectsGlobalScope": true}, {"version": "acae90d417bee324b1372813b5a00829d31c7eb670d299cd7f8f9a648ac05688", "affectsGlobalScope": true}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true}, {"version": "33358442698bb565130f52ba79bfd3d4d484ac85fe33f3cb1759c54d18201393", "affectsGlobalScope": true}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true}, "282f98006ed7fa9bb2cd9bdbe2524595cfc4bcd58a0bb3232e4519f2138df811", "6222e987b58abfe92597e1273ad7233626285bc2d78409d4a7b113d81a83496b", "cbe726263ae9a7bf32352380f7e8ab66ee25b3457137e316929269c19e18a2be", "7f698624bbbb060ece7c0e51b7236520ebada74b747d7523c7df376453ed6fea", {"version": "4025a454b1ca489b179ee8c684bdd70ff8c1967e382076ade53e7e4653e1daec", "affectsGlobalScope": true}, {"version": "984c09345059b76fc4221c2c54e53511f4c27a0794dfd6e9f81dc60f0b564e05", "affectsGlobalScope": true}, "25e0492430a92b27414c02e43d9a67a96d915cc9982caa3f36096933e1492f1e", "a28ac3e717907284b3910b8e9b3f9844a4e0b0a861bea7b923e5adf90f620330", "82e5a50e17833a10eb091923b7e429dc846d42f1c6161eb6beeb964288d98a15", "947942f1c1822a3a751c26d5a971664bd1cf2c1030940288d6a092fcda9ac55f", "cf76e2f59b26aea7c923015783083b4820d6c0d85cda2fec3a0f232aabfc56c3", "616a55b9694bdb765470c1f66dc9606779b250520a36b87f4de2e4c594cea9bc", {"version": "7e7187b0314b6ee31f37db0f82da408112ef548713ccbe28796ef551d47a6e0c", "affectsGlobalScope": true}, "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "591c55c575d645b7f600dd2be6d9cf8d28c4b4a5297d9dfcd41b861fddce04ab", "c0191592be8eb7906f99ac4b8798d80a585b94001ea1a5f50d6ce5b0d13a5c62", {"version": "9081565c2038a9e776007d7c25f4e3789e094b5367efd87327c9ffbcb41810b9", "affectsGlobalScope": true}, "269536033c45b92c13747175bf6d197b959559acb119247b3eb9a0eee250c8c5", "52f5c39e78a90c1d8ed7db18f39d890b2e8464a3f44d4233617893f6648e317d", "a69e8bce30aea7ec98f3b6ddfbc378c92826fede01aafbaec703057c2503ea51", "97403268e8d1dcb6721073b5221109b4f7feff04a9e9251c8c75d7a6f80fff2c", "07c952ebf71447e4141a4bc97a08f3ea5408b29a3f2ce8e6c789b96e2dd11de8", "82d44b4d7d7b6949047c3ccee236740ec32f59b01beb5808111a7955c4428abf", "dc012971aa234063a6446c256f9756e26778b7e33a21b21fa812d9feb93223bf", "fa188224c82a757e87e8a6db07a2c8d06d14c4010b42c8510a2fab92cf2f25e8", "5be272c6749cb4993249e532de7a2ffb25aebba84a3f09ba0ab298e9d3bee569", "ac05d7db7112e9fa7a3e8336b7d90847eda20beeff477c064814c121145f28c7", "70bf8f382b9ff9a49091bfe47423bf2829702b2e6ccd4c716ed814a8f4db646a", "ebca2325bc9ad17397a53db229b743ebf1287669d0fe199d609b2ffe45a0286a", "dc95c7af485d8e50007df90706fb881ec05473cbdefd4e25c1a2c5a78600e36b", "25c6c2285ff9f5db5c33cd709c3d5b85a132516c8c3f0c07abd3dcafd48592df", "58dcd0d3c196a0638c3d2cbdbece7f35b00af7d187b229af762efb5770e1a0e7", "c33be757ecbdb130ffa53d979d2e2274ec0829af9cc308b6c9f34d6fcf9ee053", "2335cbcd7623ffc7b52b732606070e68be65a9f85adbcb9223cd5a50c5021b5a", "1c8f3888a901c982a1f3b9e3804c1a850c8ae353e1c42ed86779e0052c987ff9", "6b274f9521d70761306cc465fd73db5f818f9d1f74f5fa566f495bbd1cd3130d", "c742b9398263931f526cd0dae91e53e6de7fae60a531de0c21e8c0659bb08165", "9d5bbe6f932d15166c508366e0f2fab9febb73da376aacf0321fb0ff99a05c10", "99402bfac31ca0690c25548946ce8bfe569338daa0ced635b8d35bbb7e9f259b", "cf28767fdcca9bb4bd171ebe00625b8645a89f289a43c78545cc96a4a77a872f", "61d0813ff52e62399403858b772399e0abad9c1251277b7369d535dee162fdc5", "6fbf13c3fbfb7bf412a383b2797ea701513023bd585f259fe3913b058c7d5322", "cf2a7c84021412b95c2bbdc1416484e29054ab304abcf461ac09a22a34bbb565", "ff7cd91f9e4f4eafbf900b03e0b55ff307837ef9ce11487dd9e400c5810e47d3", "315cd3156613208e7b6c7d0bb83a735cef36be01d20111941cef2e69af3ba2e0", "8cc0facd77100eb689aebc7af21a309cf872eef0b6978624a8dd644917e8e8a7"], "root": [62, [67, 91]], "options": {"composite": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "skipLibCheck": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[51, 61, 65, 66], [53], [52, 53, 54], [55], [52], [52, 57, 58, 60], [57, 58, 59, 60], [61, 64, 66], [50], [46], [47], [48, 49], [61, 65, 66], [56, 60], [60], [63, 74, 75], [63, 73], [63, 74, 76, 77, 78, 79], [61, 63, 65, 66], [63], [51, 61, 62, 63, 65, 66, 67, 70], [63, 66, 68], [62, 63, 66, 67, 69], [61, 63, 65, 66, 75, 80], [63, 74], [63, 72]], "referencedMap": [[62, 1], [54, 2], [55, 3], [56, 4], [57, 5], [58, 6], [60, 7], [65, 8], [51, 9], [47, 10], [48, 11], [50, 12], [64, 13], [66, 13], [61, 14], [63, 15], [76, 16], [74, 17], [77, 16], [80, 18], [79, 16], [78, 16], [81, 19], [82, 19], [83, 19], [72, 19], [84, 20], [71, 21], [69, 22], [70, 23], [68, 19], [85, 24], [67, 19], [86, 24], [87, 24], [88, 24], [75, 20], [89, 20], [90, 25], [73, 26], [91, 20]], "exportedModulesMap": [[62, 1], [54, 2], [55, 3], [56, 4], [57, 5], [58, 6], [60, 7], [65, 8], [51, 9], [47, 10], [48, 11], [50, 12], [64, 13], [66, 13], [61, 14], [63, 15], [76, 16], [74, 17], [77, 16], [80, 18], [79, 16], [78, 16], [81, 19], [82, 19], [83, 19], [72, 19], [84, 20], [71, 21], [69, 22], [70, 23], [68, 19], [85, 24], [67, 19], [86, 24], [87, 24], [88, 24], [75, 20], [89, 20], [90, 25], [73, 26], [91, 20]], "semanticDiagnosticsPerFile": [62, 54, 53, 55, 56, 57, 58, 60, 52, 59, 65, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 51, 47, 46, 48, 49, 50, 64, 66, 61, 63, 76, 74, [77, [{"file": "./src/api/dashboard.ts", "start": 1222, "length": 34, "messageText": "Expected 1 arguments, but got 2.", "category": 1, "code": 2554}, {"file": "./src/api/dashboard.ts", "start": 1330, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'unknown' is not assignable to type 'BlobPart'."}, {"file": "./src/api/dashboard.ts", "start": 1456, "length": 8, "messageText": "'response' is of type 'unknown'.", "category": 1, "code": 18046}]], 80, [79, [{"file": "./src/api/subscription.ts", "start": 4457, "length": 7, "messageText": "Property 'baseURL' is private and only accessible within class 'ApiClient'.", "category": 1, "code": 2341}]], [78, [{"file": "./src/api/usage.ts", "start": 3623, "length": 7, "messageText": "Property 'baseURL' is private and only accessible within class 'ApiClient'.", "category": 1, "code": 2341}]], 81, 82, 83, 72, 84, [71, [{"file": "./src/main.ts", "start": 1118, "length": 23, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(error: Error, instance: ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}, {}, string, {}, {}, {}, string, ComponentProvideOptions>, ... 4 more ..., any> | null, info: string) => void' is not assignable to type '(err: unknown, instance: ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}, {}, string, {}, {}, {}, string, ComponentProvideOptions>, ... 4 more ..., any> | null, info: string) => void'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'error' and 'err' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'unknown' is not assignable to type 'Error'.", "category": 1, "code": 2322}]}]}}]], 69, 70, 68, [85, [{"file": "./src/stores/apikeys.ts", "start": 5055, "length": 10, "code": 2322, "category": 1, "messageText": {"messageText": "Type '\"active\" | \"inactive\"' is not assignable to type 'ApiKeyStatus'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"active\"' is not assignable to type 'ApiKeyStatus'. Did you mean 'ApiKeyStatus.INACTIVE'?", "category": 1, "code": 2820}]}}]], 67, 86, [87, [{"file": "./src/stores/subscription.ts", "start": 3860, "length": 11, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ currentUsage: number; usageLimit: number; usagePercentage: number; resetDate: string; projectedUsage: number; overage?: { amount: number; cost: number; } | undefined; }' is not assignable to type '{ currentUsage: number; usageLimit: number; usagePercentage: number; resetDate: string; projectedUsage: number; overage: { amount: number; cost: number; } | null; } | { currentUsage: number; ... 4 more ...; overage: { ...; } | null; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ currentUsage: number; usageLimit: number; usagePercentage: number; resetDate: string; projectedUsage: number; overage?: { amount: number; cost: number; } | undefined; }' is not assignable to type '{ currentUsage: number; usageLimit: number; usagePercentage: number; resetDate: string; projectedUsage: number; overage: { amount: number; cost: number; } | null; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'overage' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '{ amount: number; cost: number; } | undefined' is not assignable to type '{ amount: number; cost: number; } | null'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type '{ amount: number; cost: number; } | null'.", "category": 1, "code": 2322}]}]}]}]}}, {"file": "./src/stores/subscription.ts", "start": 6930, "length": 24, "code": 2322, "category": 1, "messageText": {"messageText": "Type '\"active\" | \"cancelled\"' is not assignable to type 'PlanStatus'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"active\"' is not assignable to type 'PlanStatus'. Did you mean 'PlanStatus.INACTIVE'?", "category": 1, "code": 2820}]}}, {"file": "./src/stores/subscription.ts", "start": 9154, "length": 20, "code": 2551, "category": 1, "messageText": "Property 'exportBillingHistory' does not exist on type '{ getCurrentSubscription(): Promise<SubscriptionPlan>; getAvailablePlans(): Promise<SubscriptionPlan[]>; createSubscription(data: { ...; }): Promise<...>; ... 10 more ...; getBillingSummary(): Promise<...>; }'. Did you mean 'getBillingHistory'?", "relatedInformation": [{"file": "./src/api/subscription.ts", "start": 2656, "length": 17, "messageText": "'getBillingHistory' is declared here.", "category": 3, "code": 2728}]}]], [88, [{"file": "./src/stores/usage.ts", "start": 8461, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}]], 75, 89, 90, 73, 91], "affectedFilesPendingEmit": [76, 74, 77, 80, 79, 78, 81, 82, 83, 72, 84, 71, 69, 70, 68, 85, 67, 86, 87, 88, 75, 89, 90, 73, 91], "emitSignatures": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91]}, "version": "5.3.3"}