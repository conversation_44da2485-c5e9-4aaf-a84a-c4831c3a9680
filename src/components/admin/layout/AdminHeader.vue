<template>
  <header class="header">
    <div class="header-top fixed">
      <div class="header-top-wrap wrapper">
        <div class="left-entry">
          <div class="mobile-show">
            <div id="mobile-menu-button" @click="showMenu" class="menu-icon">
              <i class="ri-menu-2-line"></i>
            </div>
          </div>
          <div class="header-logo">
            <router-link to="/" class="logo">
              <img itemprop="logo" src="/src/assets/dashboard/picture/组-107.svg" alt="管理后台" />
            </router-link>
          </div>
          <div id="top-menu" class="menu-container">
            <ul class="menu">
              <li><router-link to="/admin">管理概览</router-link></li>
              <li><router-link to="/admin/users">用户管理</router-link></li>
              <li><router-link to="/admin/api-keys">API管理</router-link></li>
              <li><router-link to="/admin/usage">使用统计</router-link></li>
              <li><router-link to="/admin/subscriptions">订阅管理</router-link></li>
              <li v-if="adminAuthStore.isSuperAdmin"><router-link to="/admin/settings">系统设置</router-link></li>
            </ul>
          </div>
        </div>
        
        <div class="center-entry">
          <div class="menu-search-mask"></div>
        </div>
        
        <div class="right-entry">
          <div class="menu-search mobile-show">
            <div id="mobile-search-button" @click="showSearch" class="search-icon">
              <i class="ri-search-line"></i>
            </div>
          </div>
          
          <!-- 管理员菜单 -->
          <div class="user-menu">
            <div class="user-avatar" @click="toggleUserMenu">
              <img :src="adminUser?.avatar || '/src/assets/dashboard/picture/a6e.png'" alt="管理员头像" />
              <i class="ri-arrow-down-s-line"></i>
            </div>
            
            <div v-show="showUserMenu" class="user-dropdown">
              <div class="user-info">
                <div class="user-email">{{ adminUser?.username }}</div>
                <div class="user-plan">{{ getRoleName(adminUser?.role) }}</div>
              </div>
              <hr>
              <router-link to="/admin" class="dropdown-item">
                <i class="ri-dashboard-line"></i>
                管理概览
              </router-link>
              <router-link to="/admin/users" class="dropdown-item">
                <i class="ri-user-line"></i>
                用户管理
              </router-link>
              <router-link v-if="adminAuthStore.isSuperAdmin" to="/admin/settings" class="dropdown-item">
                <i class="ri-settings-line"></i>
                系统设置
              </router-link>
              <hr>
              <button @click="handleLogout" class="dropdown-item logout">
                <i class="ri-logout-box-line"></i>
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAdminAuthStore } from '@/stores/adminAuth'

const router = useRouter()
const adminAuthStore = useAdminAuthStore()

const adminUser = computed(() => adminAuthStore.adminUser)
const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const showMobileSearch = ref(false)

const getRoleName = (role?: string): string => {
  const roleMap: Record<string, string> = {
    'super_admin': '超级管理员',
    'admin': '管理员', 
    'operator': '操作员'
  }
  return roleMap[role || ''] || '管理员'
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const showMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const showSearch = () => {
  showMobileSearch.value = !showMobileSearch.value
}

const handleLogout = async () => {
  try {
    adminAuthStore.logout()
    router.push('/admin/login')
  } catch (error) {
    console.error('Admin logout failed:', error)
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event: MouseEvent) => {
  const userMenu = document.querySelector('.user-menu')
  if (userMenu && !userMenu.contains(event.target as Node)) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* 修复logo大小问题 */
.header-logo img {
  height: calc(var(--top-menu-height) - 25px) !important;
  width: auto !important;
  max-height: 31px !important;
}

.header-logo {
  max-height: calc(var(--top-menu-height) - 10px) !important;
}

/* 修复头部导航样式 */
.menu-container {
  display: flex !important;
  align-items: center;
}

.menu {
  display: flex !important;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
}

.menu li {
  margin: 0 8px;
}

.menu li a {
  color: var(--color-text-regular);
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: block;
}

.menu li a:hover,
.menu li a.router-link-active {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* 移动端菜单隐藏 */
@media (max-width: 768px) {
  .menu-container {
    display: none !important;
  }
  
  .mobile-show {
    display: block !important;
  }
}

@media (min-width: 769px) {
  .mobile-show {
    display: none !important;
  }
}

.user-menu {
  position: relative;
  display: flex;
  align-items: center;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.user-avatar:hover {
  background-color: var(--bg-muted-color);
}

.user-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
  padding: 8px 0;
}

.user-info {
  padding: 12px 16px;
}

.user-email {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.user-plan {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: var(--color-text-primary);
  text-decoration: none;
  transition: background-color 0.2s;
  border: none;
  background: none;
  width: 100%;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: var(--bg-muted-color);
}

.dropdown-item i {
  margin-right: 8px;
  width: 16px;
}

.dropdown-item.logout {
  color: #e74c3c;
}

hr {
  margin: 8px 0;
  border: none;
  border-top: 1px solid var(--border-color-base);
}

/* 管理员主题色调整 */
.menu a.router-link-active {
  color: #667eea !important;
}
</style>