import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

interface AdminUser {
  id: string
  username: string
  email: string
  role: 'super_admin' | 'admin' | 'operator'
  permissions: string[]
  avatar?: string
}

interface AdminLoginData {
  username: string
  password: string
}

export const useAdminAuthStore = defineStore('adminAuth', () => {
  // 状态
  const adminUser = ref<AdminUser | null>(null)
  const adminToken = ref<string | null>(null)
  const isLoading = ref(true)
  const isInitialized = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!adminUser.value && !!adminToken.value)
  const isSuperAdmin = computed(() => adminUser.value?.role === 'super_admin')
  const isAdmin = computed(() => adminUser.value?.role === 'admin' || adminUser.value?.role === 'super_admin')

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!adminUser.value) return false
    if (adminUser.value.role === 'super_admin') return true
    return adminUser.value.permissions.includes(permission)
  }

  // 初始化管理员认证状态
  const initAuth = async () => {
    if (isInitialized.value) return
    
    isLoading.value = true
    
    try {
      const savedToken = localStorage.getItem('admin_token')
      const savedUser = localStorage.getItem('admin_user')
      
      if (savedToken && savedUser) {
        try {
          adminToken.value = savedToken
          adminUser.value = JSON.parse(savedUser)
          console.log('Admin auth restored:', { username: adminUser.value?.username })
        } catch (error) {
          console.error('Failed to parse saved admin data:', error)
          clearAuth()
        }
      }
    } catch (error) {
      console.error('Failed to initialize admin auth:', error)
      clearAuth()
    } finally {
      isLoading.value = false
      isInitialized.value = true
    }
  }

  // 清除认证数据
  const clearAuth = () => {
    adminUser.value = null
    adminToken.value = null
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
  }

  // 保存认证数据
  const saveAuth = (userData: AdminUser, authToken: string) => {
    adminUser.value = userData
    adminToken.value = authToken
    localStorage.setItem('admin_token', authToken)
    localStorage.setItem('admin_user', JSON.stringify(userData))
  }

  // 模拟 API 延迟
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

  // 管理员登录
  const login = async (loginData: AdminLoginData) => {
    isLoading.value = true
    
    try {
      // 模拟 API 调用
      await delay(1500)
      
      // 模拟验证逻辑
      if (loginData.username === 'superadmin' && loginData.password === 'admin123') {
        const userData: AdminUser = {
          id: 'admin-1',
          username: 'superadmin',
          email: '<EMAIL>',
          role: 'super_admin',
          permissions: ['*'],
          avatar: 'https://ui-avatars.com/api/?name=Super+Admin&background=ef4444&color=fff'
        }
        
        const authToken = 'admin-jwt-token-' + Date.now()
        saveAuth(userData, authToken)
        console.log('Admin login successful')
        
        return userData
      } else if (loginData.username === 'admin' && loginData.password === 'admin456') {
        const userData: AdminUser = {
          id: 'admin-2',
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          permissions: ['users.view', 'users.edit', 'api.view', 'stats.view', 'subscription.view', 'subscription.edit'],
          avatar: 'https://ui-avatars.com/api/?name=Admin&background=3b82f6&color=fff'
        }
        
        const authToken = 'admin-jwt-token-' + Date.now()
        saveAuth(userData, authToken)
        console.log('Admin login successful')
        
        return userData
      } else if (loginData.username === 'operator' && loginData.password === 'operator789') {
        const userData: AdminUser = {
          id: 'admin-3',
          username: 'operator',
          email: '<EMAIL>',
          role: 'operator',
          permissions: ['users.view', 'api.view', 'stats.view'],
          avatar: 'https://ui-avatars.com/api/?name=Operator&background=22c55e&color=fff'
        }
        
        const authToken = 'admin-jwt-token-' + Date.now()
        saveAuth(userData, authToken)
        console.log('Admin login successful')
        
        return userData
      } else {
        throw { message: '用户名或密码错误' }
      }
    } catch (error: any) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = () => {
    console.log('Admin logging out')
    clearAuth()
  }

  // 更新管理员信息
  const updateAdminUser = (userData: Partial<AdminUser>) => {
    if (adminUser.value) {
      adminUser.value = { ...adminUser.value, ...userData }
      localStorage.setItem('admin_user', JSON.stringify(adminUser.value))
    }
  }

  // 自动初始化认证状态
  initAuth()

  return {
    // 状态
    adminUser: computed(() => adminUser.value),
    adminToken: computed(() => adminToken.value),
    isLoading: computed(() => isLoading.value),
    isInitialized: computed(() => isInitialized.value),
    isAuthenticated,
    isSuperAdmin,
    isAdmin,
    
    // 方法
    initAuth,
    login,
    logout,
    updateAdminUser,
    clearAuth,
    hasPermission
  }
})