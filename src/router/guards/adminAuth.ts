import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useAdminAuthStore } from '@/stores/adminAuth'
import { AppError, ERROR_CODES, errorLogger } from '@/utils/errorHandler'

/**
 * 管理员认证路由守卫
 * 增强的权限验证和安全检查
 */
export const adminAuthGuard = async (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const adminAuthStore = useAdminAuthStore()
  
  console.log('Admin auth guard - route check:', {
    to: to.path,
    isInitialized: adminAuthStore.isInitialized,
    isAuthenticated: adminAuthStore.isAuthenticated,
    adminUser: adminAuthStore.adminUser?.username
  })
  
  try {
    // ========== 初始化检查 ==========
    
    // 主动触发初始化
    if (!adminAuthStore.isInitialized) {
      console.log('Initializing admin auth store...')
      await adminAuthStore.initAuth()
    }
    
    // 等待认证状态初始化，增加更严格的超时控制
    let waitCount = 0
    const maxWaitTime = 100 // 5秒超时
    while (!adminAuthStore.isInitialized && waitCount < maxWaitTime) {
      await new Promise(resolve => setTimeout(resolve, 50))
      waitCount++
    }
    
    // 超时检查
    if (!adminAuthStore.isInitialized) {
      errorLogger.log(
        new AppError(
          '管理员认证初始化超时',
          ERROR_CODES.TIMEOUT_ERROR,
          'high',
          { 
            component: 'AdminAuthGuard', 
            action: 'initialization',
            targetRoute: to.path,
            waitTime: waitCount * 50
          }
        )
      )
      next('/admin/login')
      return
    }
    
    // ========== 登录页面特殊处理 ==========
    
    if (to.path === '/admin/login') {
      if (adminAuthStore.isAuthenticated) {
        console.log('Already authenticated, redirecting to admin dashboard')
        
        // 记录已认证用户访问登录页
        errorLogger.log(
          new AppError('已认证用户访问登录页', 'AUTH_REDIRECT', 'low', {
            component: 'AdminAuthGuard',
            action: 'login_redirect',
            userId: adminAuthStore.adminUser?.id
          })
        )
        
        next('/admin')
      } else {
        next()
      }
      return
    }
    
    // ========== 基础认证检查 ==========
    
    if (!adminAuthStore.isAuthenticated) {
      console.log('Not authenticated, redirecting to login')
      
      errorLogger.log(
        new AppError(
          '未认证访问管理员页面',
          ERROR_CODES.AUTHENTICATION_ERROR,
          'medium',
          { 
            component: 'AdminAuthGuard', 
            action: 'access_denied',
            targetRoute: to.path,
            fromRoute: from.path 
          }
        )
      )
      
      next({
        path: '/admin/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // ========== 权限检查 ==========
    
    const adminUser = adminAuthStore.adminUser
    if (!adminUser) {
      console.error('No admin user found despite being authenticated')
      errorLogger.log(
        new AppError(
          '认证状态异常：无用户信息',
          ERROR_CODES.DATA_CORRUPTION_ERROR,
          'high',
          { component: 'AdminAuthGuard', action: 'user_data_missing' }
        )
      )
      adminAuthStore.clearAuthData()
      next('/admin/login')
      return
    }
    
    // 检查路由级别权限
    const requiredPermission = to.meta.permission as string | undefined
    if (requiredPermission) {
      // 前端基础检查
      const hasBasicPermission = adminAuthStore.hasPermission(requiredPermission)
      
      if (!hasBasicPermission) {
        console.log(`Permission denied: ${requiredPermission}`)
        
        errorLogger.log(
          new AppError(
            `权限不足: ${requiredPermission}`,
            ERROR_CODES.PERMISSION_ERROR,
            'medium',
            { 
              component: 'AdminAuthGuard', 
              action: 'permission_denied',
              permission: requiredPermission,
              userRole: adminUser.role,
              targetRoute: to.path,
              userPermissions: adminUser.permissions
            }
          )
        )
        
        // 跳转回管理员首页而不是登录页
        next({
          path: '/admin',
          query: { error: 'permission_denied', required: requiredPermission }
        })
        return
      }

      // TODO: 后续可以添加服务端权限验证
      // 当前保持注释状态，准备集成真实API时启用
      // try {
      //   const serverPermissionCheck = await adminAuthStore.verifyPermission(requiredPermission)
      //   if (!serverPermissionCheck) {
      //     console.log(`Server permission denied: ${requiredPermission}`)
      //     errorLogger.log(
      //       new AppError(
      //         `服务端权限验证失败: ${requiredPermission}`,
      //         ERROR_CODES.PERMISSION_ERROR,
      //         'high',
      //         { component: 'AdminAuthGuard', action: 'server_permission_denied' }
      //       )
      //     )
      //     next('/admin')
      //     return
      //   }
      // } catch (error) {
      //   console.error('Server permission check failed:', error)
      //   // 网络错误时允许访问（避免网络问题导致无法使用）
      //   if (error instanceof AppError && error.code === ERROR_CODES.NETWORK_ERROR) {
      //     console.warn('Permission check failed due to network, allowing access')
      //   } else {
      //     next('/admin')
      //     return
      //   }
      // }
    }
    
    // 检查角色要求
    const requiredRole = to.meta.role as string | undefined
    if (requiredRole) {
      const hasRole = adminAuthStore.hasRole(requiredRole)
      if (!hasRole) {
        console.log(`Role denied: required ${requiredRole}, user has ${adminUser.role}`)
        
        errorLogger.log(
          new AppError(
            `角色权限不足: 需要 ${requiredRole}，当前 ${adminUser.role}`,
            ERROR_CODES.AUTHORIZATION_ERROR,
            'medium',
            { 
              component: 'AdminAuthGuard', 
              action: 'role_denied',
              requiredRole,
              userRole: adminUser.role,
              targetRoute: to.path
            }
          )
        )
        
        next({
          path: '/admin',
          query: { error: 'role_denied', required: requiredRole }
        })
        return
      }
    }

    // ========== 安全检查 ==========
    
    // 检查会话有效性
    const token = adminAuthStore.adminToken
    if (token) {
      try {
        // 开发环境下放宽token验证
        const isDevelopment = import.meta.env.DEV
        
        if (isDevelopment && token.startsWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9')) {
          // 开发环境下的mock JWT token，跳过详细验证
          console.log('Development mode: Using mock JWT token')
        } else {
          // JWT格式基础验证
          const parts = token.split('.')
          if (parts.length !== 3) {
            console.warn('Invalid token format detected')
            
            errorLogger.log(
              new AppError(
                '无效的Token格式',
                ERROR_CODES.SECURITY_ERROR,
                'high',
                { 
                  component: 'AdminAuthGuard', 
                  action: 'invalid_token',
                  tokenParts: parts.length 
                }
              )
            )
            
            adminAuthStore.clearAuthData()
            next('/admin/login')
            return
          }
        }

        // TODO: 可以添加更多token验证逻辑
        // 例如检查token过期时间、签名验证等
        // 当集成真实JWT时启用这些功能
        
      } catch (error) {
        console.error('Token validation error:', error)
        
        errorLogger.log(
          new AppError(
            'Token验证失败',
            ERROR_CODES.SECURITY_ERROR,
            'high',
            { 
              component: 'AdminAuthGuard', 
              action: 'token_validation_error',
              error: error instanceof Error ? error.message : String(error)
            }
          )
        )
        
        adminAuthStore.clearAuthData()
        next('/admin/login')
        return
      }
    }

    // ========== 会话活跃度检查 ==========
    
    // 记录访问日志并更新最后活跃时间
    const lastActive = adminUser.lastActiveAt
    const now = new Date()
    const timeSinceLastActive = now.getTime() - new Date(lastActive).getTime()
    
    // 如果超过4小时未活跃，记录警告（但不阻止访问）
    if (timeSinceLastActive > 4 * 60 * 60 * 1000) {
      errorLogger.log(
        new AppError('长时间未活跃的管理员会话', 'SESSION_WARNING', 'low', {
          component: 'AdminAuthGuard',
          action: 'inactive_session',
          userId: adminUser.id,
          inactiveTime: timeSinceLastActive
        })
      )
    }

    // ========== 记录访问日志 ==========
    
    errorLogger.log(
      new AppError('Admin route access', 'ROUTE_ACCESS', 'low', {
        component: 'AdminAuthGuard',
        action: 'route_access',
        route: to.path,
        userId: adminUser.id,
        userRole: adminUser.role,
        permission: requiredPermission,
        requiredRole: requiredRole
      })
    )
    
    console.log('Admin auth guard passed, proceeding to:', to.path)
    next()
    
  } catch (error) {
    console.error('Admin auth guard error:', error)
    
    errorLogger.log(
      new AppError(
        '管理员路由守卫异常',
        ERROR_CODES.UNKNOWN_ERROR,
        'high',
        { 
          component: 'AdminAuthGuard', 
          action: 'guard_error',
          targetRoute: to.path,
          error: error instanceof Error ? error.message : String(error) 
        }
      )
    )
    
    // 发生异常时，安全起见重定向到登录页
    next('/admin/login')
  }
}

/**
 * 权限检查辅助函数
 * 提供给组件内使用的权限验证
 */
export const checkAdminPermission = (permission: string): boolean => {
  const adminAuthStore = useAdminAuthStore()
  return adminAuthStore.hasPermission(permission)
}

/**
 * 角色检查辅助函数
 * 提供给组件内使用的角色验证
 */
export const checkAdminRole = (role: string): boolean => {
  const adminAuthStore = useAdminAuthStore()
  return adminAuthStore.hasRole(role)
}