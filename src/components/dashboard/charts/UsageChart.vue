<template>
  <div class="usage-chart">
    <div v-if="loading" class="chart-loading">
      <div class="loading-spinner"></div>
      <div>加载图表数据...</div>
    </div>
    
    <div v-else-if="chartData.length === 0" class="chart-empty">
      <i class="ri-bar-chart-line"></i>
      <div>暂无数据</div>
    </div>
    
    <div v-else class="chart-container">
      <div class="chart-legend">
        <div class="legend-item">
          <div class="legend-color legend-color--requests"></div>
          <span>API调用</span>
        </div>
        <div class="legend-item">
          <div class="legend-color legend-color--errors"></div>
          <span>错误次数</span>
        </div>
        <div class="legend-item">
          <div class="legend-color legend-color--response-time"></div>
          <span>平均响应时间(ms)</span>
        </div>
      </div>
      
      <div class="chart-wrapper">
        <svg ref="chartSvg" class="chart-svg" :viewBox="`0 0 ${chartWidth} ${chartHeight}`">
          <!-- 网格线 -->
          <g class="grid">
            <line
              v-for="i in 5"
              :key="`grid-${i}`"
              :x1="chartPadding.left"
              :y1="chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) * (i - 1) / 4"
              :x2="chartWidth - chartPadding.right"
              :y2="chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) * (i - 1) / 4"
              stroke="#f0f0f0"
              stroke-width="1"
            />
          </g>
          
          <!-- Y轴标签 -->
          <g class="y-axis">
            <text
              v-for="i in 5"
              :key="`y-label-${i}`"
              :x="chartPadding.left - 10"
              :y="chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) * (i - 1) / 4 + 4"
              text-anchor="end"
              class="axis-label"
            >
              {{ formatYAxisLabel(maxValue * (5 - i) / 4) }}
            </text>
          </g>
          
          <!-- 请求量折线图 -->
          <polyline
            :points="requestsPath"
            fill="none"
            stroke="#3b82f6"
            stroke-width="2"
            class="chart-line"
          />
          
          <!-- 错误量折线图 -->
          <polyline
            :points="errorsPath"
            fill="none"
            stroke="#ef4444"
            stroke-width="2"
            class="chart-line"
          />
          
          <!-- 数据点 -->
          <g class="data-points">
            <circle
              v-for="(point, index) in chartData"
              :key="`point-${index}`"
              :cx="getX(index)"
              :cy="getY(point.requests)"
              r="4"
              fill="#3b82f6"
              class="data-point"
              @mouseover="showTooltip($event, point, index)"
              @mouseout="hideTooltip"
            />
          </g>
          
          <!-- X轴标签 -->
          <g class="x-axis">
            <text
              v-for="(point, index) in displayPoints"
              :key="`x-label-${index}`"
              :x="getX(point.index)"
              :y="chartHeight - chartPadding.bottom + 20"
              text-anchor="middle"
              class="axis-label"
            >
              {{ formatDate(point.date) }}
            </text>
          </g>
        </svg>
      </div>
    </div>
    
    <!-- 提示框 -->
    <div
      v-show="tooltipVisible"
      ref="tooltip"
      class="chart-tooltip"
      :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
    >
      <div class="tooltip-date">{{ tooltipData.date }}</div>
      <div class="tooltip-item">
        <span class="tooltip-color tooltip-color--requests"></span>
        调用量: {{ tooltipData.requests }}
      </div>
      <div class="tooltip-item">
        <span class="tooltip-color tooltip-color--errors"></span>
        错误: {{ tooltipData.errors }}
      </div>
      <div class="tooltip-item">
        响应时间: {{ tooltipData.responseTime }}ms
      </div>
      <div class="tooltip-item">
        成功率: {{ (tooltipData.errorRate * 100).toFixed(1) }}%
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import type { UsageChartData, UsageChartPoint } from '@/types/dashboard'

interface Props {
  data: UsageChartData
  period: number
}

const props = defineProps<Props>()

const loading = ref(false)
const tooltipVisible = ref(false)
const tooltipX = ref(0)
const tooltipY = ref(0)
const tooltipData = ref<any>({})
const chartSvg = ref<SVGElement>()
const tooltip = ref<HTMLElement>()

const chartWidth = 800
const chartHeight = 400
const chartPadding = {
  top: 20,
  right: 20,
  bottom: 60,
  left: 60
}

const chartData = computed(() => props.data || [])

const maxValue = computed(() => {
  if (chartData.value.length === 0) return 100
  return Math.max(...chartData.value.map(d => Math.max(d.requests, d.errors))) * 1.1
})

const displayPoints = computed(() => {
  const data = chartData.value
  if (data.length <= 7) return data.map((d, i) => ({ ...d, index: i }))
  
  const step = Math.ceil(data.length / 7)
  return data.filter((_, i) => i % step === 0).map((d, i) => ({ ...d, index: i * step }))
})

const requestsPath = computed(() => {
  return chartData.value
    .map((point, index) => `${getX(index)},${getY(point.requests)}`)
    .join(' ')
})

const errorsPath = computed(() => {
  return chartData.value
    .map((point, index) => `${getX(index)},${getY(point.errors)}`)
    .join(' ')
})

const getX = (index: number): number => {
  const dataWidth = chartWidth - chartPadding.left - chartPadding.right
  return chartPadding.left + (dataWidth * index) / Math.max(1, chartData.value.length - 1)
}

const getY = (value: number): number => {
  const dataHeight = chartHeight - chartPadding.top - chartPadding.bottom
  return chartPadding.top + dataHeight - (dataHeight * value) / maxValue.value
}

const formatYAxisLabel = (value: number): string => {
  if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
  if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
  return Math.round(value).toString()
}

const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const showTooltip = (event: MouseEvent, point: UsageChartPoint, index: number) => {
  tooltipData.value = {
    date: formatDate(point.date),
    requests: point.requests,
    errors: point.errors,
    responseTime: point.responseTime,
    errorRate: point.errorRate
  }
  
  const rect = (event.target as Element).getBoundingClientRect()
  const chartRect = chartSvg.value?.getBoundingClientRect()
  
  if (chartRect) {
    tooltipX.value = event.clientX - chartRect.left + 10
    tooltipY.value = event.clientY - chartRect.top - 10
  }
  
  tooltipVisible.value = true
}

const hideTooltip = () => {
  tooltipVisible.value = false
}

onMounted(() => {
  // 初始化图表
})
</script>

<style scoped>
.usage-chart {
  position: relative;
  width: 100%;
  height: 400px;
}

.chart-loading,
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color-base);
  border-top: 3px solid var(--theme-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-empty i {
  font-size: 48px;
  margin-bottom: 16px;
}

.chart-container {
  height: 100%;
}

.chart-legend {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--color-text-regular);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.legend-color--requests {
  background: #3b82f6;
}

.legend-color--errors {
  background: #ef4444;
}

.legend-color--response-time {
  background: #f59e0b;
}

.chart-wrapper {
  position: relative;
  height: calc(100% - 60px);
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.axis-label {
  font-size: 12px;
  fill: var(--color-text-secondary);
}

.chart-line {
  transition: stroke-width 0.2s;
}

.chart-line:hover {
  stroke-width: 3;
}

.data-point {
  cursor: pointer;
  transition: r 0.2s;
}

.data-point:hover {
  r: 6;
}

.chart-tooltip {
  position: absolute;
  background: white;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  z-index: 1000;
  min-width: 150px;
}

.tooltip-date {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--color-text-primary);
}

.tooltip-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
  color: var(--color-text-regular);
}

.tooltip-item:last-child {
  margin-bottom: 0;
}

.tooltip-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.tooltip-color--requests {
  background: #3b82f6;
}

.tooltip-color--errors {
  background: #ef4444;
}
</style>