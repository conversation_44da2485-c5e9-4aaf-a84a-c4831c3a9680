<template>
  <div class="trend-chart">
    <div v-if="loading" class="chart-loading">
      <div class="loading-spinner"></div>
      <div>加载趋势数据...</div>
    </div>
    
    <div v-else-if="chartData.length === 0" class="chart-empty">
      <i class="ri-line-chart-line"></i>
      <div>暂无趋势数据</div>
    </div>
    
    <div v-else class="chart-container">
      <div class="chart-legend">
        <div class="legend-item">
          <div class="legend-color" :class="`legend-color--${metric}`"></div>
          <span>{{ metricLabel }}</span>
        </div>
      </div>
      
      <div class="chart-wrapper">
        <svg ref="chartSvg" class="chart-svg" :viewBox="`0 0 ${chartWidth} ${chartHeight}`">
          <!-- 网格线 -->
          <g class="grid">
            <line
              v-for="i in 5"
              :key="`grid-${i}`"
              :x1="chartPadding.left"
              :y1="chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) * (i - 1) / 4"
              :x2="chartWidth - chartPadding.right"
              :y2="chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) * (i - 1) / 4"
              stroke="#f0f0f0"
              stroke-width="1"
            />
          </g>
          
          <!-- Y轴标签 -->
          <g class="y-axis">
            <text
              v-for="i in 5"
              :key="`y-label-${i}`"
              :x="chartPadding.left - 10"
              :y="chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) * (i - 1) / 4 + 4"
              text-anchor="end"
              class="axis-label"
            >
              {{ formatYAxisLabel(maxValue * (5 - i) / 4) }}
            </text>
          </g>
          
          <!-- 数据区域 -->
          <path
            :d="areaPath"
            :fill="`url(#gradient-${metric})`"
            opacity="0.3"
          />
          
          <!-- 数据线 -->
          <polyline
            :points="linePath"
            fill="none"
            :stroke="lineColor"
            stroke-width="3"
            class="chart-line"
          />
          
          <!-- 数据点 -->
          <g class="data-points">
            <circle
              v-for="(point, index) in chartData"
              :key="`point-${index}`"
              :cx="getX(index)"
              :cy="getY(getValue(point))"
              r="5"
              :fill="lineColor"
              class="data-point"
              @mouseover="showTooltip($event, point, index)"
              @mouseout="hideTooltip"
            />
          </g>
          
          <!-- X轴标签 -->
          <g class="x-axis">
            <text
              v-for="(point, index) in displayPoints"
              :key="`x-label-${index}`"
              :x="getX(point.index)"
              :y="chartHeight - chartPadding.bottom + 20"
              text-anchor="middle"
              class="axis-label"
            >
              {{ formatDate(point.date) }}
            </text>
          </g>
          
          <!-- 渐变定义 -->
          <defs>
            <linearGradient :id="`gradient-${metric}`" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" :stop-color="lineColor" stop-opacity="0.8"/>
              <stop offset="100%" :stop-color="lineColor" stop-opacity="0"/>
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
    
    <!-- 提示框 -->
    <div
      v-show="tooltipVisible"
      ref="tooltip"
      class="chart-tooltip"
      :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
    >
      <div class="tooltip-date">{{ tooltipData.date }}</div>
      <div class="tooltip-value">
        {{ metricLabel }}: {{ tooltipData.value }}{{ metricUnit }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface ChartPoint {
  date: string
  requests: number
  errors: number
  responseTime: number
}

interface Props {
  data: ChartPoint[]
  metric: 'requests' | 'errors' | 'responseTime'
  period: number
  loading: boolean
}

const props = defineProps<Props>()

const tooltipVisible = ref(false)
const tooltipX = ref(0)
const tooltipY = ref(0)
const tooltipData = ref<any>({})
const chartSvg = ref<SVGElement>()

const chartWidth = 800
const chartHeight = 350
const chartPadding = {
  top: 20,
  right: 20,
  bottom: 60,
  left: 60
}

const chartData = computed(() => props.data || [])

const metricLabel = computed(() => {
  const labels = {
    requests: 'API调用',
    errors: '错误次数',
    responseTime: '响应时间'
  }
  return labels[props.metric]
})

const metricUnit = computed(() => {
  const units = {
    requests: '次',
    errors: '次',
    responseTime: 'ms'
  }
  return units[props.metric]
})

const lineColor = computed(() => {
  const colors = {
    requests: '#3b82f6',
    errors: '#ef4444',
    responseTime: '#f59e0b'
  }
  return colors[props.metric]
})

const maxValue = computed(() => {
  if (chartData.value.length === 0) return 100
  const values = chartData.value.map(d => getValue(d))
  return Math.max(...values) * 1.1
})

const displayPoints = computed(() => {
  const data = chartData.value
  if (data.length <= 7) return data.map((d, i) => ({ ...d, index: i }))
  
  const step = Math.ceil(data.length / 7)
  return data.filter((_, i) => i % step === 0).map((d, i) => ({ ...d, index: i * step }))
})

const linePath = computed(() => {
  return chartData.value
    .map((point, index) => `${getX(index)},${getY(getValue(point))}`)
    .join(' ')
})

const areaPath = computed(() => {
  if (chartData.value.length === 0) return ''
  
  const points = chartData.value.map((point, index) => 
    `${getX(index)},${getY(getValue(point))}`
  )
  
  const firstX = getX(0)
  const lastX = getX(chartData.value.length - 1)
  const bottomY = chartHeight - chartPadding.bottom
  
  return `M ${firstX},${bottomY} L ${points.join(' L ')} L ${lastX},${bottomY} Z`
})

const getValue = (point: ChartPoint): number => {
  return point[props.metric]
}

const getX = (index: number): number => {
  const dataWidth = chartWidth - chartPadding.left - chartPadding.right
  return chartPadding.left + (dataWidth * index) / Math.max(1, chartData.value.length - 1)
}

const getY = (value: number): number => {
  const dataHeight = chartHeight - chartPadding.top - chartPadding.bottom
  return chartPadding.top + dataHeight - (dataHeight * value) / maxValue.value
}

const formatYAxisLabel = (value: number): string => {
  if (props.metric === 'responseTime') {
    return Math.round(value).toString()
  }
  if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
  if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
  return Math.round(value).toString()
}

const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const showTooltip = (event: MouseEvent, point: ChartPoint, index: number) => {
  tooltipData.value = {
    date: formatDate(point.date),
    value: getValue(point)
  }
  
  const rect = (event.target as Element).getBoundingClientRect()
  const chartRect = chartSvg.value?.getBoundingClientRect()
  
  if (chartRect) {
    tooltipX.value = event.clientX - chartRect.left + 10
    tooltipY.value = event.clientY - chartRect.top - 10
  }
  
  tooltipVisible.value = true
}

const hideTooltip = () => {
  tooltipVisible.value = false
}
</script>

<style scoped>
.trend-chart {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-loading,
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color-base);
  border-top: 3px solid var(--theme-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-empty i {
  font-size: 48px;
  margin-bottom: 16px;
}

.chart-container {
  height: 100%;
}

.chart-legend {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--color-text-regular);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.legend-color--requests {
  background: #3b82f6;
}

.legend-color--errors {
  background: #ef4444;
}

.legend-color--responseTime {
  background: #f59e0b;
}

.chart-wrapper {
  position: relative;
  height: calc(100% - 40px);
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.axis-label {
  font-size: 12px;
  fill: var(--color-text-secondary);
}

.chart-line {
  transition: stroke-width 0.2s;
}

.data-point {
  cursor: pointer;
  transition: r 0.2s;
}

.data-point:hover {
  r: 7;
}

.chart-tooltip {
  position: absolute;
  background: white;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  z-index: 1000;
  min-width: 120px;
}

.tooltip-date {
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--color-text-primary);
}

.tooltip-value {
  font-size: 14px;
  color: var(--color-text-regular);
}
</style>