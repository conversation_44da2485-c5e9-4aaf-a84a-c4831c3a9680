<template>
  <div class="admin-users">
    <!-- 页面头部 -->
    <div class="page-header mg-b">
      <div class="header-content">
        <h1 class="page-title">用户管理</h1>
        <p class="page-subtitle">管理系统所有用户账户</p>
      </div>
      <button @click="showAddModal = true" class="add-user-btn">
        <i class="ri-add-line"></i>
        添加用户
      </button>
    </div>

    <!-- 搜索和筛选工具栏 -->
    <div class="filter-toolbar mg-b">
      <div class="search-box">
        <i class="ri-search-line search-icon"></i>
        <input
          v-model="searchQuery"
          type="text"
          class="search-input"
          placeholder="搜索用户名、邮箱..."
        >
      </div>
      <div class="filter-selects">
        <select v-model="filterStatus" class="filter-select">
          <option value="">所有状态</option>
          <option value="active">活跃</option>
          <option value="inactive">未激活</option>
          <option value="banned">已封禁</option>
        </select>
        <select v-model="filterPlan" class="filter-select">
          <option value="">所有套餐</option>
          <option value="free">免费版</option>
          <option value="basic">基础版</option>
          <option value="pro">专业版</option>
          <option value="enterprise">企业版</option>
        </select>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="users-list">
      <div v-for="user in filteredUsers" :key="user.id" class="user-item">
        <div class="user-avatar">
          <img
            :src="user.avatar || `https://ui-avatars.com/api/?name=${user.name}&background=random`"
            :alt="user.name"
          >
        </div>
        <div class="user-info">
          <div class="user-name">{{ user.name }}</div>
          <div class="user-email">{{ user.email }}</div>
          <div class="user-meta">
            <span class="meta-item">
              <i class="ri-calendar-line"></i>
              注册于 {{ formatDate(user.createdAt) }}
            </span>
            <span class="meta-item">
              <i class="ri-time-line"></i>
              最后活跃 {{ formatDate(user.lastActive) }}
            </span>
          </div>
        </div>
        <div class="user-stats">
          <div class="stat-item">
            <span class="stat-label">套餐:</span>
            <span :class="getPlanClass(user.plan)">
              {{ getPlanName(user.plan) }}
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">API调用:</span>
            <span class="stat-value">{{ user.apiCalls.toLocaleString() }}</span>
          </div>
          <div class="stat-item">
            <span :class="getStatusClass(user.status)">
              {{ getStatusName(user.status) }}
            </span>
          </div>
        </div>
        <div class="user-actions">
          <button @click="editUser(user)" class="edit-btn">
            编辑
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div v-if="showEditModal" class="fixed z-10 inset-0 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeEditModal"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              编辑用户信息
            </h3>
            
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">用户名</label>
                <input
                  v-model="editingUser.name"
                  type="text"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">邮箱</label>
                <input
                  v-model="editingUser.email"
                  type="email"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">套餐</label>
                <select
                  v-model="editingUser.plan"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="free">免费版</option>
                  <option value="basic">基础版</option>
                  <option value="pro">专业版</option>
                  <option value="enterprise">企业版</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">状态</label>
                <select
                  v-model="editingUser.status"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="active">活跃</option>
                  <option value="inactive">未激活</option>
                  <option value="banned">已封禁</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="saveUser"
              type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              保存
            </button>
            <button
              @click="closeEditModal"
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface User {
  id: string
  name: string
  email: string
  avatar?: string
  plan: 'free' | 'basic' | 'pro' | 'enterprise'
  status: 'active' | 'inactive' | 'banned'
  apiCalls: number
  createdAt: Date
  lastActive: Date
}

// 模拟用户数据
const users = ref<User[]>([
  {
    id: '1',
    name: '张三',
    email: '<EMAIL>',
    plan: 'pro',
    status: 'active',
    apiCalls: 125420,
    createdAt: new Date('2024-01-15'),
    lastActive: new Date('2025-01-30')
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    plan: 'basic',
    status: 'active',
    apiCalls: 54300,
    createdAt: new Date('2024-03-20'),
    lastActive: new Date('2025-01-29')
  },
  {
    id: '3',
    name: '王五',
    email: '<EMAIL>',
    plan: 'free',
    status: 'inactive',
    apiCalls: 1200,
    createdAt: new Date('2024-06-10'),
    lastActive: new Date('2024-12-15')
  },
  {
    id: '4',
    name: '赵六',
    email: '<EMAIL>',
    plan: 'enterprise',
    status: 'active',
    apiCalls: 892000,
    createdAt: new Date('2023-11-05'),
    lastActive: new Date('2025-01-30')
  },
  {
    id: '5',
    name: '钱七',
    email: '<EMAIL>',
    plan: 'pro',
    status: 'banned',
    apiCalls: 0,
    createdAt: new Date('2024-02-28'),
    lastActive: new Date('2024-10-20')
  }
])

const searchQuery = ref('')
const filterStatus = ref('')
const filterPlan = ref('')
const showEditModal = ref(false)
const showAddModal = ref(false)
const editingUser = ref<Partial<User>>({})

const filteredUsers = computed(() => {
  return users.value.filter(user => {
    const matchesSearch = !searchQuery.value || 
      user.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesStatus = !filterStatus.value || user.status === filterStatus.value
    const matchesPlan = !filterPlan.value || user.plan === filterPlan.value
    
    return matchesSearch && matchesStatus && matchesPlan
  })
})

const getPlanName = (plan: string) => {
  const planNames: Record<string, string> = {
    free: '免费版',
    basic: '基础版',
    pro: '专业版',
    enterprise: '企业版'
  }
  return planNames[plan] || plan
}

const getPlanClass = (plan: string) => {
  const planClasses: Record<string, string> = {
    free: 'plan-tag plan-tag--free',
    basic: 'plan-tag plan-tag--basic',
    pro: 'plan-tag plan-tag--pro',
    enterprise: 'plan-tag plan-tag--enterprise'
  }
  return planClasses[plan] || 'plan-tag'
}

const getStatusName = (status: string) => {
  const statusNames: Record<string, string> = {
    active: '活跃',
    inactive: '未激活',
    banned: '已封禁'
  }
  return statusNames[status] || status
}

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    active: 'status-tag status-tag--active',
    inactive: 'status-tag status-tag--inactive',
    banned: 'status-tag status-tag--banned'
  }
  return statusClasses[status] || 'status-tag'
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date)
}

const editUser = (user: User) => {
  editingUser.value = { ...user }
  showEditModal.value = true
}

const closeEditModal = () => {
  showEditModal.value = false
  editingUser.value = {}
}

const saveUser = () => {
  // 这里添加保存用户的逻辑
  const index = users.value.findIndex(u => u.id === editingUser.value.id)
  if (index !== -1) {
    users.value[index] = { ...users.value[index], ...editingUser.value } as User
  }
  closeEditModal()
}
</script>

<style scoped>
.admin-users {
  padding: 24px;
  max-width: none;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 16px;
  color: var(--color-text-secondary);
  margin: 0;
}

.add-user-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.add-user-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 搜索和筛选工具栏 */
.filter-toolbar {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--bg-main-color);
  border: 1px solid var(--border-color-base);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-box {
  position: relative;
  flex: 2;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
  font-size: 18px;
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--border-color-base);
  border-radius: 8px;
  font-size: 14px;
  background: var(--bg-main-color);
  color: var(--color-text-primary);
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-selects {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 12px 16px;
  border: 1px solid var(--border-color-base);
  border-radius: 8px;
  font-size: 14px;
  background: var(--bg-main-color);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 用户列表 */
.users-list {
  background: var(--bg-main-color);
  border: 1px solid var(--border-color-base);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.user-item {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color-base);
  transition: all 0.2s ease;
}

.user-item:hover {
  background: rgba(102, 126, 234, 0.02);
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  flex-shrink: 0;
  margin-right: 16px;
}

.user-avatar img {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.user-email {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
}

.user-meta {
  display: flex;
  gap: 24px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: var(--color-text-secondary);
}

.meta-item i {
  font-size: 14px;
  opacity: 0.7;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-right: 24px;
  min-width: 200px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.stat-label {
  color: var(--color-text-secondary);
  font-weight: 500;
}

.stat-value {
  color: var(--color-text-primary);
  font-weight: 600;
}

/* 标签样式 */
.plan-tag {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.plan-tag--free {
  background: rgba(156, 163, 175, 0.1);
  color: #6b7280;
}

.plan-tag--basic {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.plan-tag--pro {
  background: rgba(147, 51, 234, 0.1);
  color: #9333ea;
}

.plan-tag--enterprise {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.status-tag {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-tag--active {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.status-tag--inactive {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-tag--banned {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.user-actions {
  flex-shrink: 0;
}

.edit-btn {
  padding: 8px 16px;
  background: transparent;
  color: #667eea;
  border: 1px solid #667eea;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filter-selects {
    justify-content: space-between;
  }
  
  .user-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
  }
  
  .user-stats {
    margin-right: 0;
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .admin-users {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .add-user-btn {
    width: 100%;
    justify-content: center;
  }
  
  .user-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .user-stats {
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>