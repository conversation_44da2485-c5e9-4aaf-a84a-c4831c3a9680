-- API转发服务核心表结构
\c xiangluai_api;

-- 用户管理模块
CREATE TABLE user_management.users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    email_verified BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'active', -- active, suspended, deleted
    role VARCHAR(20) DEFAULT 'user', -- user, admin
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- API管理模块
CREATE TABLE api_management.api_keys (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES user_management.users(id) ON DELETE CASCADE,
    key_hash VARCHAR(255) UNIQUE NOT NULL, -- 存储hash，不存储原始key
    key_prefix VARCHAR(20) NOT NULL, -- 显示用的前缀，如 "sk-xxx...abc"
    name VARCHAR(100) NOT NULL,
    permissions JSONB DEFAULT '{}', -- 权限配置
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_day INTEGER DEFAULT 1000,
    status VARCHAR(20) DEFAULT 'active', -- active, suspended, expired
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 套餐管理
CREATE TABLE billing.plans (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2),
    api_calls_limit INTEGER NOT NULL, -- 每月API调用限制
    rate_limit_per_minute INTEGER DEFAULT 60,
    features JSONB DEFAULT '[]', -- 功能特性列表
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户订阅
CREATE TABLE billing.subscriptions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES user_management.users(id) ON DELETE CASCADE,
    plan_id BIGINT NOT NULL REFERENCES billing.plans(id),
    status VARCHAR(20) DEFAULT 'active', -- active, cancelled, expired
    billing_cycle VARCHAR(20) DEFAULT 'monthly', -- monthly, yearly
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 使用统计 (按天聚合)
CREATE TABLE analytics.daily_usage (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES user_management.users(id) ON DELETE CASCADE,
    api_key_id BIGINT REFERENCES api_management.api_keys(id) ON DELETE SET NULL,
    date DATE NOT NULL,
    total_calls INTEGER DEFAULT 0,
    successful_calls INTEGER DEFAULT 0,
    failed_calls INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, api_key_id, date)
);

-- API调用实时统计 (Redis缓存的备份)
CREATE TABLE analytics.realtime_stats (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES user_management.users(id) ON DELETE CASCADE,
    api_key_id BIGINT REFERENCES api_management.api_keys(id) ON DELETE SET NULL,
    calls_today INTEGER DEFAULT 0,
    calls_this_month INTEGER DEFAULT 0,
    last_call_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, api_key_id)
);

-- 系统配置
CREATE TABLE system.configs (
    id BIGSERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_email ON user_management.users(email);
CREATE INDEX idx_users_status ON user_management.users(status);

CREATE INDEX idx_api_keys_user_id ON api_management.api_keys(user_id);
CREATE INDEX idx_api_keys_hash ON api_management.api_keys(key_hash);
CREATE INDEX idx_api_keys_status ON api_management.api_keys(status);

CREATE INDEX idx_subscriptions_user_id ON billing.subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON billing.subscriptions(status);

CREATE INDEX idx_daily_usage_user_date ON analytics.daily_usage(user_id, date);
CREATE INDEX idx_daily_usage_api_key_date ON analytics.daily_usage(api_key_id, date);

CREATE INDEX idx_realtime_stats_user_id ON analytics.realtime_stats(user_id);

-- 插入默认套餐
INSERT INTO billing.plans (name, description, price_monthly, price_yearly, api_calls_limit, rate_limit_per_minute, features) VALUES
('免费版', '适合个人开发者试用', 0.00, 0.00, 1000, 10, '["基础API访问", "社区支持"]'),
('专业版', '适合小型团队', 29.99, 299.99, 50000, 60, '["高级API访问", "优先支持", "使用统计"]'),
('企业版', '适合大型企业', 99.99, 999.99, 500000, 300, '["全功能API访问", "专属支持", "高级统计", "自定义限制"]');

-- 插入系统配置
INSERT INTO system.configs (key, value, description) VALUES
('api_base_url', '"https://api.yourdomain.com"', 'API服务基础URL'),
('default_rate_limit', '{"per_minute": 60, "per_day": 1000}', '默认限流配置'),
('supported_models', '["gpt-3.5-turbo", "gpt-4", "claude-3"]', '支持的模型列表');
