// 增强的错误处理工具函数
import { toast } from '@/composables/useToast'

// ==================== 错误类型定义 ====================

export interface ErrorContext {
  component?: string
  action?: string
  userId?: string
  timestamp?: number
  metadata?: Record<string, any>
  // Additional properties used in auth guard and stores
  targetRoute?: string
  permission?: string
  requiredRole?: string
  userRole?: string
  route?: string
  error?: string
  // Extended context properties for enhanced logging
  fromRoute?: string
  waitTime?: number
  userPermissions?: string[]
  tokenParts?: number
  inactiveTime?: number
}

export interface ErrorReport {
  id: string
  message: string
  code: string
  stack?: string
  context: ErrorContext
  severity: 'low' | 'medium' | 'high' | 'critical'
}

// ==================== 错误码定义 ====================

export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  CONFLICT_ERROR: 'CONFLICT_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  // 新增的错误类型
  SECURITY_ERROR: 'SECURITY_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  DATA_CORRUPTION_ERROR: 'DATA_CORRUPTION_ERROR',
  RESOURCE_EXHAUSTED: 'RESOURCE_EXHAUSTED'
} as const

// ==================== 错误消息定义 ====================

export const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_CODES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ERROR_CODES.VALIDATION_ERROR]: '输入数据格式错误',
  [ERROR_CODES.AUTHENTICATION_ERROR]: '身份验证失败，请重新登录',
  [ERROR_CODES.AUTHORIZATION_ERROR]: '权限不足，无法执行此操作',
  [ERROR_CODES.NOT_FOUND_ERROR]: '请求的资源不存在',
  [ERROR_CODES.CONFLICT_ERROR]: '数据冲突，请刷新后重试',
  [ERROR_CODES.RATE_LIMIT_ERROR]: '请求过于频繁，请稍后重试',
  [ERROR_CODES.SERVER_ERROR]: '服务器内部错误，请联系管理员',
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误，请稍后重试',
  [ERROR_CODES.SECURITY_ERROR]: '安全验证失败',
  [ERROR_CODES.PERMISSION_ERROR]: '操作权限不足',
  [ERROR_CODES.DATA_CORRUPTION_ERROR]: '数据异常，请刷新页面',
  [ERROR_CODES.RESOURCE_EXHAUSTED]: '资源已用尽，请稍后重试'
} as const

// ==================== 错误处理类 ====================

export class AppError extends Error {
  public readonly code: string
  public readonly severity: 'low' | 'medium' | 'high' | 'critical'
  public readonly context: ErrorContext
  public readonly timestamp: number
  public readonly retryable: boolean
  
  constructor(
    message: string,
    code: string = ERROR_CODES.UNKNOWN_ERROR,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    context: ErrorContext = {},
    retryable: boolean = false
  ) {
    super(message)
    this.name = 'AppError'
    this.code = code
    this.severity = severity
    this.context = { ...context, timestamp: Date.now() }
    this.timestamp = Date.now()
    this.retryable = retryable
  }

  toReport(): ErrorReport {
    return {
      id: `error_${this.timestamp}_${Math.random().toString(36).substr(2, 9)}`,
      message: this.message,
      code: this.code,
      stack: this.stack,
      context: this.context,
      severity: this.severity
    }
  }
}

// ==================== 错误日志收集器 ====================

class ErrorLogger {
  private errorReports: ErrorReport[] = []
  private maxReports: number = 100

  log(error: AppError | Error, context?: ErrorContext): void {
    let report: ErrorReport

    if (error instanceof AppError) {
      report = error.toReport()
    } else {
      report = {
        id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        message: error.message,
        code: ERROR_CODES.UNKNOWN_ERROR,
        stack: error.stack,
        context: context || { timestamp: Date.now() },
        severity: 'medium'
      }
    }

    this.errorReports.unshift(report)
    
    // 保持最大报告数量
    if (this.errorReports.length > this.maxReports) {
      this.errorReports = this.errorReports.slice(0, this.maxReports)
    }

    // 控制台输出（开发环境）
    if (import.meta.env.DEV) {
      console.error('Error logged:', report)
    }

    // 严重错误需要特殊处理
    if (report.severity === 'critical' || report.severity === 'high') {
      this.handleCriticalError(report)
    }
  }

  private handleCriticalError(report: ErrorReport): void {
    // TODO: 发送到错误监控服务
    console.error('Critical error detected:', report)
    
    // 可以添加更多处理逻辑，如：
    // - 发送错误报告到服务器
    // - 触发紧急通知
    // - 清理异常状态
  }

  getReports(): ErrorReport[] {
    return [...this.errorReports]
  }

  getRecentReports(limit: number = 10): ErrorReport[] {
    return this.errorReports.slice(0, limit)
  }

  clearReports(): void {
    this.errorReports = []
  }
}

// 全局错误日志实例
export const errorLogger = new ErrorLogger()

// ==================== 用户提示函数 ====================

/**
 * 显示API错误提示
 */
export const showApiError = (title: string, errorCode?: string, message?: string) => {
  const errorMessage = errorCode && ERROR_MESSAGES[errorCode as keyof typeof ERROR_MESSAGES] 
    ? ERROR_MESSAGES[errorCode as keyof typeof ERROR_MESSAGES]
    : message || '发生未知错误'
  
  toast.error(title, errorMessage)
}

/**
 * 显示成功提示
 */
export const showApiSuccess = (title: string, message?: string) => {
  toast.success(title, message)
}

/**
 * 显示警告提示
 */
export const showWarning = (title: string, message?: string) => {
  toast.warning(title, message)
}

/**
 * 显示信息提示
 */
export const showInfo = (title: string, message?: string) => {
  toast.info(title, message)
}

// ==================== 错误处理辅助函数 ====================

/**
 * HTTP状态码到错误码的映射
 */
export const mapStatusToErrorCode = (status: number): string => {
  switch (status) {
    case 400:
      return ERROR_CODES.VALIDATION_ERROR
    case 401:
      return ERROR_CODES.AUTHENTICATION_ERROR
    case 403:
      return ERROR_CODES.AUTHORIZATION_ERROR
    case 404:
      return ERROR_CODES.NOT_FOUND_ERROR
    case 409:
      return ERROR_CODES.CONFLICT_ERROR
    case 429:
      return ERROR_CODES.RATE_LIMIT_ERROR
    case 408:
    case 504:
      return ERROR_CODES.TIMEOUT_ERROR
    case 500:
    case 502:
    case 503:
      return ERROR_CODES.SERVER_ERROR
    default:
      return ERROR_CODES.UNKNOWN_ERROR
  }
}

/**
 * 安全的异步函数执行器
 */
export const safeAsync = async <T>(
  asyncFn: () => Promise<T>,
  context?: ErrorContext,
  fallbackValue?: T
): Promise<T | undefined> => {
  try {
    return await asyncFn()
  } catch (error) {
    const appError = error instanceof AppError 
      ? error 
      : new AppError(
          error instanceof Error ? error.message : '未知错误',
          ERROR_CODES.UNKNOWN_ERROR,
          'medium',
          context
        )
    
    errorLogger.log(appError)
    showApiError('操作失败', appError.code, appError.message)
    
    return fallbackValue
  }
}

/**
 * 重试机制
 */
export const withRetry = async <T>(
  asyncFn: () => Promise<T>,
  options: {
    maxRetries?: number
    delay?: number
    backoff?: boolean
    context?: ErrorContext
  } = {}
): Promise<T> => {
  const { maxRetries = 3, delay = 1000, backoff = true, context } = options
  let lastError: Error | AppError = new Error('No attempts made')

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await asyncFn()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))
      
      if (attempt === maxRetries) {
        break
      }

      // 计算延迟时间
      const currentDelay = backoff ? delay * Math.pow(2, attempt) : delay
      await new Promise(resolve => setTimeout(resolve, currentDelay))
    }
  }

  const appError = lastError instanceof AppError 
    ? lastError 
    : new AppError(
        lastError!.message,
        ERROR_CODES.UNKNOWN_ERROR,
        'medium',
        context
      )

  errorLogger.log(appError)
  throw appError
}

// ==================== Vue错误边界 ====================

/**
 * Vue全局错误处理器
 */
export const setupGlobalErrorHandler = (app: any) => {
  app.config.errorHandler = (error: Error, instance: any, info: string) => {
    const appError = new AppError(
      error.message,
      ERROR_CODES.UNKNOWN_ERROR,
      'high',
      {
        component: instance?.$options.name || 'Unknown',
        action: info,
        timestamp: Date.now()
      }
    )

    errorLogger.log(appError)
    
    // 在生产环境中不显示技术错误详情
    if (import.meta.env.PROD) {
      showApiError('系统错误', ERROR_CODES.UNKNOWN_ERROR)
    } else {
      showApiError('开发错误', appError.code, appError.message)
    }
  }

  // 处理未捕获的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    const appError = new AppError(
      event.reason?.message || '未处理的Promise拒绝',
      ERROR_CODES.UNKNOWN_ERROR,
      'high',
      {
        action: 'unhandledrejection',
        timestamp: Date.now()
      }
    )

    errorLogger.log(appError)
    showApiError('系统错误', appError.code, appError.message)
    
    event.preventDefault()
  })
}