declare module '@heroicons/vue/24/outline' {
  import type { DefineComponent } from 'vue'
  
  export const HomeIcon: DefineComponent
  export const UsersIcon: DefineComponent
  export const KeyIcon: DefineComponent
  export const ChartBarIcon: DefineComponent
  export const CreditCardIcon: DefineComponent
  export const CogIcon: DefineComponent
  export const DocumentTextIcon: DefineComponent
  export const MagnifyingGlassIcon: DefineComponent
  export const PlusIcon: DefineComponent
  export const CalendarIcon: DefineComponent
  export const ClockIcon: DefineComponent
  export const ArrowUpIcon: DefineComponent
  export const ArrowDownIcon: DefineComponent
  export const ExclamationCircleIcon: DefineComponent
  export const ExclamationTriangleIcon: DefineComponent
  export const CheckCircleIcon: DefineComponent
  export const CurrencyDollarIcon: DefineComponent
  export const TrendingUpIcon: DefineComponent
  export const UserGroupIcon: DefineComponent
  export const ArrowDownTrayIcon: DefineComponent
  export const ChevronLeftIcon: DefineComponent
  export const ChevronRightIcon: DefineComponent
}

declare module '@heroicons/vue/24/solid' {
  import type { DefineComponent } from 'vue'
  
  export const HomeIcon: DefineComponent
  export const UsersIcon: DefineComponent
  export const KeyIcon: DefineComponent
  export const ChartBarIcon: DefineComponent
  export const CreditCardIcon: DefineComponent
  export const CogIcon: DefineComponent
  export const DocumentTextIcon: DefineComponent
}