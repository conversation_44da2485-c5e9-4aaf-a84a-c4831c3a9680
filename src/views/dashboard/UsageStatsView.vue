<template>
  <div class="usage-stats">
    <div class="page-header box qk-radius">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">使用统计</h1>
          <p class="page-description">查看详细的API使用情况和性能指标</p>
        </div>
        <div class="header-actions">
          <div class="time-range-selector">
            <select v-model="selectedPeriod" @change="updateData">
              <option value="7">最近7天</option>
              <option value="30">最近30天</option>
              <option value="90">最近90天</option>
              <option value="365">最近1年</option>
            </select>
          </div>
          <button @click="downloadReport" class="btn btn-outline" :disabled="isLoading">
            <i class="ri-download-line"></i>
            导出报告
          </button>
        </div>
      </div>
    </div>

    <div class="stats-overview">
      <div class="overview-grid">
        <div class="overview-card box qk-radius">
          <div class="card-icon">
            <i class="ri-line-chart-line"></i>
          </div>
          <div class="card-content">
            <div class="card-title">总调用次数</div>
            <div class="card-value">{{ formatNumber(stats.totalUsage) }}</div>
            <div class="card-change" :class="{ 'positive': usageTrend > 0, 'negative': usageTrend < 0 }">
              <i :class="usageTrend > 0 ? 'ri-arrow-up-line' : 'ri-arrow-down-line'"></i>
              {{ Math.abs(usageTrend).toFixed(1) }}% 较上期
            </div>
          </div>
        </div>

        <div class="overview-card box qk-radius">
          <div class="card-icon success">
            <i class="ri-check-line"></i>
          </div>
          <div class="card-content">
            <div class="card-title">成功率</div>
            <div class="card-value">{{ (stats.successRate * 100).toFixed(1) }}%</div>
            <div class="card-change" :class="{ 'positive': successRateTrend > 0, 'negative': successRateTrend < 0 }">
              <i :class="successRateTrend > 0 ? 'ri-arrow-up-line' : 'ri-arrow-down-line'"></i>
              {{ Math.abs(successRateTrend).toFixed(1) }}% 较上期
            </div>
          </div>
        </div>

        <div class="overview-card box qk-radius">
          <div class="card-icon warning">
            <i class="ri-time-line"></i>
          </div>
          <div class="card-content">
            <div class="card-title">平均响应时间</div>
            <div class="card-value">{{ stats.averageResponseTime }}ms</div>
            <div class="card-change" :class="{ 'positive': responseTimeTrend < 0, 'negative': responseTimeTrend > 0 }">
              <i :class="responseTimeTrend < 0 ? 'ri-arrow-down-line' : 'ri-arrow-up-line'"></i>
              {{ Math.abs(responseTimeTrend).toFixed(1) }}ms 较上期
            </div>
          </div>
        </div>

        <div class="overview-card box qk-radius">
          <div class="card-icon danger">
            <i class="ri-error-warning-line"></i>
          </div>
          <div class="card-content">
            <div class="card-title">错误次数</div>
            <div class="card-value">{{ formatNumber(stats.errorCount) }}</div>
            <div class="card-change" :class="{ 'positive': errorTrend < 0, 'negative': errorTrend > 0 }">
              <i :class="errorTrend < 0 ? 'ri-arrow-down-line' : 'ri-arrow-up-line'"></i>
              {{ Math.abs(errorTrend).toFixed(1) }}% 较上期
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="charts-section">
      <div class="charts-row">
        <div class="chart-card box qk-radius">
          <div class="chart-header">
            <h3>使用趋势</h3>
            <div class="chart-controls">
              <div class="metric-selector">
                <label>显示指标:</label>
                <select v-model="selectedMetric" @change="updateChartData">
                  <option value="requests">请求量</option>
                  <option value="errors">错误数</option>
                  <option value="responseTime">响应时间</option>
                </select>
              </div>
            </div>
          </div>
          <div class="chart-content">
            <TrendChart
              :data="chartData"
              :metric="selectedMetric"
              :period="selectedPeriod"
              :loading="chartLoading"
            />
          </div>
        </div>
      </div>

      <div class="charts-row">
        <div class="chart-card box qk-radius half-width">
          <div class="chart-header">
            <h3>状态码分布</h3>
          </div>
          <div class="chart-content">
            <StatusCodeChart :data="statusCodeData" :loading="chartLoading" />
          </div>
        </div>

        <div class="chart-card box qk-radius half-width">
          <div class="chart-header">
            <h3>API端点统计</h3>
          </div>
          <div class="chart-content">
            <EndpointChart :data="endpointData" :loading="chartLoading" />
          </div>
        </div>
      </div>
    </div>

    <div class="detailed-stats">
      <div class="stats-table box qk-radius">
        <div class="table-header">
          <h3>详细统计表</h3>
          <div class="table-controls">
            <div class="search-box">
              <i class="ri-search-line"></i>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索API端点..."
                class="search-input"
              >
            </div>
            <select v-model="sortBy" @change="updateTableData">
              <option value="usage">按使用量排序</option>
              <option value="errors">按错误数排序</option>
              <option value="responseTime">按响应时间排序</option>
              <option value="endpoint">按端点名称排序</option>
            </select>
          </div>
        </div>

        <div v-if="isLoading" class="table-loading">
          <div class="loading-spinner"></div>
          <div>加载统计数据...</div>
        </div>

        <div v-else-if="filteredTableData.length === 0" class="table-empty">
          <i class="ri-bar-chart-line"></i>
          <div>{{ searchQuery ? '没有找到匹配的数据' : '暂无统计数据' }}</div>
        </div>

        <div v-else class="table-content">
          <table class="stats-data-table">
            <thead>
              <tr>
                <th>API端点</th>
                <th>调用次数</th>
                <th>成功率</th>
                <th>平均响应时间</th>
                <th>错误次数</th>
                <th>最后调用</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in filteredTableData" :key="item.endpoint">
                <td class="endpoint-cell">
                  <div class="endpoint-name">{{ item.endpoint }}</div>
                  <div class="endpoint-method" :class="item.method.toLowerCase()">
                    {{ item.method }}
                  </div>
                </td>
                <td class="number-cell">{{ formatNumber(item.usage) }}</td>
                <td class="percentage-cell">
                  <div class="percentage-bar">
                    <div 
                      class="percentage-fill" 
                      :style="{ width: `${item.successRate * 100}%` }"
                      :class="{ 
                        'high': item.successRate > 0.95,
                        'medium': item.successRate > 0.8,
                        'low': item.successRate <= 0.8
                      }"
                    ></div>
                    <span class="percentage-text">{{ (item.successRate * 100).toFixed(1) }}%</span>
                  </div>
                </td>
                <td class="number-cell">{{ item.avgResponseTime }}ms</td>
                <td class="number-cell error-count">{{ item.errors }}</td>
                <td class="date-cell">{{ formatRelativeTime(item.lastUsed) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import TrendChart from '@/components/dashboard/charts/TrendChart.vue'
import StatusCodeChart from '@/components/dashboard/charts/StatusCodeChart.vue'
import EndpointChart from '@/components/dashboard/charts/EndpointChart.vue'

// 使用mock数据避免API依赖问题
const isLoading = ref(false)
const chartLoading = ref(false)
const selectedPeriod = ref(30)
const selectedMetric = ref('requests')
const searchQuery = ref('')
const sortBy = ref('usage')

// Mock统计数据
const mockStats = ref({
  totalUsage: 15420,
  successRate: 0.982,
  averageResponseTime: 245,
  errorCount: 278,
  todayUsage: 842,
  monthlyUsage: 12356,
  errorRate: 0.018
})

// Mock图表数据
const mockChartData = ref([
  { date: '2024-01-20', requests: 850, errors: 12, responseTime: 230 },
  { date: '2024-01-21', requests: 920, errors: 8, responseTime: 245 },
  { date: '2024-01-22', requests: 780, errors: 15, responseTime: 260 },
  { date: '2024-01-23', requests: 1100, errors: 22, responseTime: 220 },
  { date: '2024-01-24', requests: 950, errors: 18, responseTime: 240 },
  { date: '2024-01-25', requests: 1050, errors: 9, responseTime: 235 },
  { date: '2024-01-26', requests: 890, errors: 13, responseTime: 250 }
])

// Mock状态码数据
const mockStatusCodeData = ref([
  { code: 200, count: 14850, percentage: 89.2 },
  { code: 404, count: 850, percentage: 5.1 },
  { code: 500, count: 420, percentage: 2.5 },
  { code: 401, count: 300, percentage: 1.8 },
  { code: 429, count: 230, percentage: 1.4 }
])

// Mock端点数据
const mockEndpointData = ref([
  { endpoint: '/api/v1/users', method: 'GET', usage: 4520, percentage: 29.3, successRate: 0.98, avgResponseTime: 180 },
  { endpoint: '/api/v1/auth/login', method: 'POST', usage: 2850, percentage: 18.5, successRate: 0.95, avgResponseTime: 320 },
  { endpoint: '/api/v1/data/search', method: 'GET', usage: 2100, percentage: 13.6, successRate: 0.99, avgResponseTime: 150 },
  { endpoint: '/api/v1/upload', method: 'POST', usage: 1680, percentage: 10.9, successRate: 0.92, avgResponseTime: 850 },
  { endpoint: '/api/v1/settings', method: 'PUT', usage: 1250, percentage: 8.1, successRate: 0.97, avgResponseTime: 200 }
])

// Mock详细统计数据
const mockTableData = ref([
  { 
    endpoint: '/api/v1/users', 
    method: 'GET', 
    usage: 4520, 
    successRate: 0.98, 
    avgResponseTime: 180, 
    errors: 90, 
    lastUsed: new Date('2024-01-26T14:30:00') 
  },
  { 
    endpoint: '/api/v1/auth/login', 
    method: 'POST', 
    usage: 2850, 
    successRate: 0.95, 
    avgResponseTime: 320, 
    errors: 142, 
    lastUsed: new Date('2024-01-26T14:25:00') 
  },
  { 
    endpoint: '/api/v1/data/search', 
    method: 'GET', 
    usage: 2100, 
    successRate: 0.99, 
    avgResponseTime: 150, 
    errors: 21, 
    lastUsed: new Date('2024-01-26T14:28:00') 
  },
  { 
    endpoint: '/api/v1/upload', 
    method: 'POST', 
    usage: 1680, 
    successRate: 0.92, 
    avgResponseTime: 850, 
    errors: 134, 
    lastUsed: new Date('2024-01-26T13:45:00') 
  },
  { 
    endpoint: '/api/v1/settings', 
    method: 'PUT', 
    usage: 1250, 
    successRate: 0.97, 
    avgResponseTime: 200, 
    errors: 37, 
    lastUsed: new Date('2024-01-26T12:15:00') 
  }
])

const stats = computed(() => mockStats.value)
const chartData = computed(() => mockChartData.value)
const statusCodeData = computed(() => mockStatusCodeData.value)
const endpointData = computed(() => mockEndpointData.value)
const tableData = computed(() => mockTableData.value)

// 模拟趋势数据
const usageTrend = ref(12.5)
const successRateTrend = ref(2.1)
const responseTimeTrend = ref(-15.3)
const errorTrend = ref(-8.7)

const filteredTableData = computed(() => {
  let filtered = tableData.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.endpoint.toLowerCase().includes(query)
    )
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'usage':
        return b.usage - a.usage
      case 'errors':
        return b.errors - a.errors
      case 'responseTime':
        return b.avgResponseTime - a.avgResponseTime
      case 'endpoint':
        return a.endpoint.localeCompare(b.endpoint)
      default:
        return 0
    }
  })

  return filtered
})

const updateData = async () => {
  isLoading.value = true
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    console.log('Usage stats data updated')
  } catch (error) {
    console.error('Failed to update usage data:', error)
  } finally {
    isLoading.value = false
  }
}

const updateChartData = async () => {
  chartLoading.value = true
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    console.log('Chart data updated for metric:', selectedMetric.value)
  } catch (error) {
    console.error('Failed to update chart data:', error)
  } finally {
    chartLoading.value = false
  }
}

const updateTableData = async () => {
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    console.log('Table data updated with sort:', sortBy.value)
  } catch (error) {
    console.error('Failed to update table data:', error)
  }
}

const downloadReport = async () => {
  try {
    // 模拟报告下载
    const reportData = `Usage Report - Period: ${selectedPeriod.value} days\n\nTotal Usage: ${mockStats.value.totalUsage}\nSuccess Rate: ${(mockStats.value.successRate * 100).toFixed(1)}%\nAverage Response Time: ${mockStats.value.averageResponseTime}ms\nError Count: ${mockStats.value.errorCount}`
    
    const blob = new Blob([reportData], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `usage-report-${selectedPeriod.value}days-${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download report:', error)
  }
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
  return num.toString()
}

const formatRelativeTime = (date: Date): string => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

onMounted(async () => {
  await updateData()
})
</script>

<style scoped>
.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.page-header {
  padding: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.page-description {
  color: var(--color-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.time-range-selector select {
  padding: 8px 12px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  background: white;
}

.btn {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  border: 1px solid transparent;
  border-radius: var(--radius);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  gap: 8px;
}

.btn-outline {
  background: white;
  border-color: var(--border-color-base);
  color: var(--color-text-primary);
}

.btn-outline:hover {
  border-color: var(--theme-color);
  color: var(--theme-color);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 24px;
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-text-color);
  color: var(--theme-color);
  margin-right: 20px;
  font-size: 28px;
}

.card-icon.success {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.card-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.card-icon.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.card-change {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  gap: 4px;
}

.card-change.positive {
  color: #22c55e;
}

.card-change.negative {
  color: #ef4444;
}

.charts-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.charts-row {
  display: flex;
  gap: 24px;
}

.chart-card {
  flex: 1;
  padding: 24px;
}

.chart-card.half-width {
  flex: 0 0 calc(50% - 12px);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.chart-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.metric-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.metric-selector label {
  color: var(--color-text-secondary);
}

.metric-selector select {
  padding: 6px 10px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  background: white;
}

.chart-content {
  height: 400px;
}

.stats-table {
  padding: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.table-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: var(--color-text-secondary);
}

.search-input {
  padding: 8px 12px 8px 36px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  width: 200px;
}

.table-controls select {
  padding: 8px 12px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  background: white;
}

.table-loading,
.table-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color-base);
  border-top: 3px solid var(--theme-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-empty i {
  font-size: 48px;
  margin-bottom: 16px;
}

.stats-data-table {
  width: 100%;
  border-collapse: collapse;
}

.stats-data-table th {
  background: var(--bg-muted-color);
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--color-text-primary);
  border-bottom: 1px solid var(--border-color-base);
}

.stats-data-table td {
  padding: 16px;
  border-bottom: 1px solid var(--border-color-base);
}

.endpoint-cell {
  min-width: 200px;
}

.endpoint-name {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.endpoint-method {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.endpoint-method.get {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.endpoint-method.post {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.endpoint-method.put {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.endpoint-method.delete {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.number-cell {
  text-align: right;
  font-weight: 600;
  color: var(--color-text-primary);
}

.error-count {
  color: #ef4444;
}

.percentage-cell {
  min-width: 120px;
}

.percentage-bar {
  position: relative;
  height: 24px;
  background: var(--bg-muted-color);
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.percentage-fill {
  height: 100%;
  border-radius: 12px;
  transition: width 0.3s ease;
}

.percentage-fill.high {
  background: #22c55e;
}

.percentage-fill.medium {
  background: #f59e0b;
}

.percentage-fill.low {
  background: #ef4444;
}

.percentage-text {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.date-cell {
  color: var(--color-text-secondary);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .charts-row {
    flex-direction: column;
  }
  
  .chart-card.half-width {
    flex: 1;
  }
  
  .table-controls {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-data-table {
    font-size: 14px;
  }
  
  .stats-data-table th,
  .stats-data-table td {
    padding: 8px 12px;
  }
}
</style>