import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usageApi } from '@/api'
import type { UsageChartData } from '@/types/dashboard'

export const useUsageStore = defineStore('usage', () => {
  // 状态
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 统计数据
  const stats = ref({
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    successRate: 0,
    avgResponseTime: 0,
    errorCount: 0,
    uniqueUsers: 0
  })

  // 图表数据
  const chartData = ref<UsageChartData[]>([])
  const selectedPeriod = ref<'7d' | '30d' | '90d'>('30d')
  const chartType = ref<'requests' | 'response_time'>('requests')

  // 状态码分布
  const statusCodes = ref<Array<{
    statusCode: string
    count: number
    percentage: number
  }>>([])

  // 端点统计
  const endpoints = ref<Array<{
    endpoint: string
    method: string
    calls: number
    successRate: number
    avgResponseTime: number
    lastCall: string
    errors: number
  }>>([])

  // 错误日志
  const errorLogs = ref<Array<{
    id: string
    timestamp: string
    level: 'error' | 'warning' | 'info'
    message: string
    endpoint: string
    method: string
    statusCode: number
    userId?: string
    apiKeyId?: string
    responseTime: number
    userAgent?: string
    ip?: string
  }>>([])

  const errorLogsPagination = ref({
    total: 0,
    page: 1,
    totalPages: 0
  })

  // 用户活跃度
  const userActivity = ref({
    activeUsers: 0,
    newUsers: 0,
    returningUsers: 0,
    userGrowth: 0,
    dailyActiveUsers: [] as Array<{
      date: string
      users: number
    }>
  })

  // 实时监控数据
  const realTimeMetrics = ref({
    currentRPS: 0,
    activeConnections: 0,
    queueLength: 0,
    systemLoad: 0,
    memoryUsage: 0,
    responseTimeP95: 0,
    errorRate: 0
  })

  // 计算属性
  const hasChartData = computed(() => chartData.value.length > 0)
  
  const topEndpoints = computed(() => 
    endpoints.value
      .sort((a, b) => b.calls - a.calls)
      .slice(0, 10)
  )

  const recentErrors = computed(() => 
    errorLogs.value
      .filter(log => log.level === 'error')
      .slice(0, 5)
  )

  const systemHealthScore = computed(() => {
    const { errorRate, responseTimeP95, systemLoad } = realTimeMetrics.value
    
    let score = 100
    if (errorRate > 5) score -= 30
    else if (errorRate > 1) score -= 15
    
    if (responseTimeP95 > 2000) score -= 25
    else if (responseTimeP95 > 1000) score -= 10
    
    if (systemLoad > 80) score -= 20
    else if (systemLoad > 60) score -= 10
    
    return Math.max(0, score)
  })

  // 操作方法
  const fetchUsageStats = async (period: '7d' | '30d' | '90d' = '30d') => {
    isLoading.value = true
    error.value = null

    try {
      const data = await usageApi.getUsageStats(period)
      stats.value = {
        totalRequests: data.totalRequests,
        successfulRequests: data.successfulRequests,
        failedRequests: data.failedRequests,
        successRate: data.successRate,
        avgResponseTime: data.avgResponseTime,
        errorCount: data.errorCount,
        uniqueUsers: data.uniqueUsers
      }
      selectedPeriod.value = period
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取使用统计失败'
      console.error('Failed to fetch usage stats:', err)
    } finally {
      isLoading.value = false
    }
  }

  // 为使用统计页面兼容的方法
  const fetchStats = async (period: number) => {
    const periodMap: { [key: number]: '7d' | '30d' | '90d' } = {
      7: '7d',
      30: '30d', 
      90: '90d'
    }
    return fetchUsageStats(periodMap[period] || '30d')
  }

  const fetchChartData = async (period: number, metric: string) => {
    const periodMap: { [key: number]: '7d' | '30d' | '90d' } = {
      7: '7d',
      30: '30d', 
      90: '90d'
    }
    return fetchUsageTrend(periodMap[period] || '30d')
  }

  const fetchDetailedStats = async (period: number, sortBy: string) => {
    const periodMap: { [key: number]: '7d' | '30d' | '90d' } = {
      7: '7d',
      30: '30d', 
      90: '90d'
    }
    return fetchEndpointStats(periodMap[period] || '30d')
  }

  const downloadReport = async (period: number) => {
    return exportUsageData('csv', true)
  }

  // 为图表组件提供的计算属性
  const statusCodeData = computed(() => 
    statusCodes.value.map(item => ({
      code: parseInt(item.statusCode),
      count: item.count,
      percentage: item.percentage
    }))
  )

  const endpointData = computed(() => 
    endpoints.value.map(item => ({
      endpoint: item.endpoint,
      method: item.method,
      usage: item.calls,
      percentage: (item.calls / (endpoints.value.reduce((sum, e) => sum + e.calls, 0) || 1)) * 100,
      successRate: item.successRate,
      avgResponseTime: item.avgResponseTime
    }))
  )

  const detailedStats = computed(() => 
    endpoints.value.map(item => ({
      endpoint: item.endpoint,
      method: item.method,
      usage: item.calls,
      successRate: item.successRate,
      avgResponseTime: item.avgResponseTime,
      errors: item.errors,
      lastUsed: new Date(item.lastCall)
    }))
  )

  const fetchUsageTrend = async (period: '7d' | '30d' | '90d' = '30d') => {
    try {
      const data = await usageApi.getUsageTrend(period)
      chartData.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取趋势数据失败'
      console.error('Failed to fetch usage trend:', err)
    }
  }

  const fetchStatusCodeDistribution = async (period: '7d' | '30d' | '90d' = '30d') => {
    try {
      const data = await usageApi.getStatusCodeDistribution(period)
      statusCodes.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取状态码分布失败'
      console.error('Failed to fetch status code distribution:', err)
    }
  }

  const fetchEndpointStats = async (period: '7d' | '30d' | '90d' = '30d') => {
    try {
      const data = await usageApi.getEndpointStats(period)
      endpoints.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取端点统计失败'
      console.error('Failed to fetch endpoint stats:', err)
    }
  }

  const fetchErrorLogs = async (params?: {
    page?: number
    pageSize?: number
    level?: 'error' | 'warning' | 'info'
    startDate?: string
    endDate?: string
  }) => {
    try {
      const data = await usageApi.getErrorLogs(params)
      errorLogs.value = data.logs
      errorLogsPagination.value = {
        total: data.total,
        page: data.page,
        totalPages: data.totalPages
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取错误日志失败'
      console.error('Failed to fetch error logs:', err)
    }
  }

  const fetchUserActivity = async (period: '7d' | '30d' | '90d' = '30d') => {
    try {
      const data = await usageApi.getUserActivity(period)
      userActivity.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取用户活跃度失败'
      console.error('Failed to fetch user activity:', err)
    }
  }

  const fetchRealTimeMetrics = async () => {
    try {
      const data = await usageApi.getRealTimeMetrics()
      realTimeMetrics.value = data
    } catch (err) {
      console.error('Failed to fetch real-time metrics:', err)
      // 不设置错误状态，因为实时数据获取失败不应该影响整体UI
    }
  }

  // 导出数据
  const exportUsageData = async (format: 'csv' | 'json' | 'xlsx', includeDetails = false) => {
    try {
      const blob = await usageApi.exportUsageData({
        format,
        period: selectedPeriod.value,
        includeDetails
      })

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `usage-data-${selectedPeriod.value}-${new Date().toISOString().split('T')[0]}.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '导出数据失败'
      throw err
    }
  }

  // 初始化和刷新数据
  const loadAllData = async (period: '7d' | '30d' | '90d' = '30d') => {
    await Promise.all([
      fetchUsageStats(period),
      fetchUsageTrend(period),
      fetchStatusCodeDistribution(period),
      fetchEndpointStats(period),
      fetchErrorLogs({ pageSize: 5 }),
      fetchUserActivity(period)
    ])
  }

  const refreshData = async () => {
    await loadAllData(selectedPeriod.value)
  }

  // 定时更新实时数据
  let realTimeInterval: NodeJS.Timeout | null = null
  let isDestroyed = false

  const startRealTimeUpdates = (intervalMs = 30000) => {
    if (realTimeInterval) {
      clearInterval(realTimeInterval)
    }
    
    if (isDestroyed) return
    
    fetchRealTimeMetrics() // 立即获取一次
    realTimeInterval = setInterval(() => {
      if (!isDestroyed) {
        fetchRealTimeMetrics()
      }
    }, intervalMs)
  }

  const stopRealTimeUpdates = () => {
    if (realTimeInterval) {
      clearInterval(realTimeInterval)
      realTimeInterval = null
    }
  }

  // Store销毁时的清理函数
  const destroy = () => {
    isDestroyed = true
    stopRealTimeUpdates()
    clearData()
  }

  // 设置方法
  const setPeriod = (period: '7d' | '30d' | '90d') => {
    selectedPeriod.value = period
    loadAllData(period)
  }

  const setChartType = (type: 'requests' | 'response_time') => {
    chartType.value = type
  }

  // 清除数据
  const clearData = () => {
    stats.value = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      successRate: 0,
      avgResponseTime: 0,
      errorCount: 0,
      uniqueUsers: 0
    }
    chartData.value = []
    statusCodes.value = []
    endpoints.value = []
    errorLogs.value = []
    userActivity.value = {
      activeUsers: 0,
      newUsers: 0,
      returningUsers: 0,
      userGrowth: 0,
      dailyActiveUsers: []
    }
    error.value = null
    stopRealTimeUpdates()
  }

    return {
    // 状态
    isLoading,
    error,
    stats,
    chartData,
    selectedPeriod,
    chartType,
    statusCodes,
    endpoints,
    errorLogs,
    errorLogsPagination,
    userActivity,
    realTimeMetrics,

    // 计算属性
    hasChartData,
    topEndpoints,
    recentErrors,
    systemHealthScore,
    statusCodeData,
    endpointData,
    detailedStats,

    // 方法
    fetchUsageStats,
    fetchUsageTrend,
    fetchStatusCodeDistribution,
    fetchEndpointStats,
    fetchErrorLogs,
    fetchUserActivity,
    fetchRealTimeMetrics,
    exportUsageData,
    loadAllData,
    refreshData,
    startRealTimeUpdates,
    stopRealTimeUpdates,
    setPeriod,
    setChartType,
    clearData,
    destroy,
    
    // 兼容方法
    fetchStats,
    fetchChartData,
    fetchDetailedStats,
    downloadReport
  }
})