<template>
  <div class="dashboard-container">
    <div id="page" class="site">
      <DashboardHeader />
      
      <div id="content" class="site-content">
        <div class="content-wrapper">
          <div id="account" class="account wrapper">
            <div class="account-header mg-b account-mobile-hidden box">
              <div class="mask-wrapper" style="background-image: url(/src/assets/dashboard/image/fill_w1440_h288_g0_mark_20201223055552.png);"></div>
              <div class="account-panel">
                <div class="account-profile">
                  <div class="left-user-info">
                    <div class="user-avatar">
                      <img :src="userStore.user?.avatar || '/src/assets/dashboard/picture/a6e.png'" class="avatar-face w-h" alt="用户头像" />
                    </div>
                    <div class="user-info">
                      <div class="user-info-name">
                        <span class="user-name">{{ userStore.user?.email || '用户' }}</span>
                        <span class="user-lv"><img src="/src/assets/dashboard/picture/LV2.png" class="lv-img-icon"></span>
                      </div>
                      <div class="desc text-ellipsis">
                        <div class="verify">
                          <span>{{ formatPlanStatus(currentPlan.status) }}</span>
                          <span v-if="currentPlan.endDate">
                            - {{ formatDate(currentPlan.endDate) }}到期
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="right-user-action">
                    <div class="profile-primary-button no-hover">{{ currentPlan.name }}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="account-page-content">
              <DashboardSidebar />
              
              <div class="account-page-right">
                <router-view />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import DashboardHeader from './DashboardHeader.vue'
import DashboardSidebar from './DashboardSidebar.vue'
import { PlanStatus, PlanType } from '@/types/dashboard'

// 使用mock数据，避免store依赖导致的循环依赖问题
const mockUser = ref({
  email: '<EMAIL>',
  avatar: '/src/assets/dashboard/picture/a6e.png'
})

const mockCurrentPlan = ref({
  name: '免费版',
  type: PlanType.FREE,
  status: PlanStatus.ACTIVE,
  endDate: null
})

const userStore = computed(() => ({
  user: mockUser.value
}))

const currentPlan = computed(() => mockCurrentPlan.value)

const formatPlanStatus = (status: PlanStatus): string => {
  const statusMap = {
    [PlanStatus.ACTIVE]: '已激活',
    [PlanStatus.INACTIVE]: '未激活',
    [PlanStatus.EXPIRED]: '已过期',
    [PlanStatus.CANCELLED]: '已取消'
  }
  return statusMap[status] || status
}

const formatDate = (date: Date | null): string => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
/* Dashboard布局优化 - 调整宽度比例 */
.dashboard-container {
  min-height: 100vh;
}

/* 修复header在dashboard中的位置问题 */
.dashboard-container :deep(.header .header-top) {
  position: relative !important;
  top: auto !important;
  max-width: 100% !important;
  width: 100% !important;
  z-index: auto !important;
}

.dashboard-container :deep(.header .header-top-wrap) {
  max-width: 100% !important;
  padding: 0 24px;
}

/* 调整账户页面内容布局比例 */
.dashboard-container :deep(.account-page-content) {
  display: flex;
  gap: 24px;
}

/* 优化左侧边栏宽度 - 调整为更合适的比例 */
.dashboard-container :deep(.account-page-left) {
  width: 22% !important;
  min-width: 240px !important;
  max-width: 280px;
  flex-shrink: 0;
}

/* 确保右侧内容区域充分利用空间 */
.dashboard-container :deep(.account-page-right) {
  flex: 1;
  width: auto !important;
}

/* 优化左侧边栏内部间距 */
.dashboard-container :deep(.account-page-left > div) {
  padding: 16px !important;
}

/* 优化VIP卡片高度 */
.dashboard-container :deep(.vip-card) {
  height: 75px !important;
}

/* 优化统计项目的显示 */
.dashboard-container :deep(.counts-item .single-count-item) {
  min-width: auto;
  padding: 2px;
}

/* 优化导航链接的间距和显示 */
.dashboard-container :deep(.tab-links) {
  margin-top: 8px;
}

.dashboard-container :deep(.tab-links .link-item) {
  padding: 0 16px !important;
  height: 42px !important;
  line-height: 42px !important;
  margin-bottom: 6px;
  font-size: 14px;
  border-radius: 6px;
  justify-content: space-between;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

/* 确保导航文字和图标正确显示 */
.dashboard-container :deep(.link-title) {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: visible;
}

.dashboard-container :deep(.link-title i) {
  margin-right: 10px !important;
  font-size: 18px !important;
}

/* 用户头像相关样式 */
.user-avatar {
  --avatar-size: 112px;
  width: var(--avatar-size);
  height: var(--avatar-size);
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.lv-img-icon {
  height: 18px;
  width: auto;
  margin-left: 8px;
}

.user-name {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.profile-primary-button {
  background: var(--bg-text-color);
  border-radius: var(--radius);
  box-shadow: 0 2px 10px 0 rgba(0,0,0,.03);
  color: var(--color-primary);
  cursor: pointer;
  line-height: 40px;
  height: 40px;
  text-align: center;
  width: 100%;
  display: block;
  padding: 0 16px;
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .dashboard-container :deep(.account-page-left) {
    width: 24% !important;
    min-width: 220px !important;
  }
}

@media (max-width: 1200px) {
  .dashboard-container :deep(.account-page-left) {
    width: 26% !important;
    min-width: 200px !important;
  }
}

@media (max-width: 768px) {
  .dashboard-container :deep(.account-page-content) {
    flex-direction: column;
  }
  
  .dashboard-container :deep(.account-page-left) {
    width: 100% !important;
    min-width: auto !important;
    max-width: none;
  }
  
  .dashboard-container :deep(.header .header-top-wrap) {
    padding: 0 16px;
  }
}
</style>