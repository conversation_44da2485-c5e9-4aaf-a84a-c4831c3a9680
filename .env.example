# 环境配置示例

# 应用配置
NODE_ENV=production
PORT=3000
APP_NAME=xiangluai-api

# 数据库配置 - 本地Docker版本
# DATABASE_URL=postgresql://xiangluai:your_secure_password@localhost:5432/xiangluai_api
# REDIS_URL=redis://:your_redis_password@localhost:6379

# 数据库配置 - 云服务版本（迁移时启用）
DATABASE_URL=*****************************************************/xiangluai_api
REDIS_URL=redis://:password@your-redis-endpoint:6379

# ClickHouse配置（可选）
CLICKHOUSE_URL=http://your-clickhouse-endpoint:8123
CLICKHOUSE_DATABASE=xiangluai_analytics
CLICKHOUSE_USERNAME=xiangluai
CLICKHOUSE_PASSWORD=your_clickhouse_password

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# API配置
API_RATE_LIMIT=1000
API_TIMEOUT=30000

# 第三方服务配置
OPENAI_API_KEY=your-openai-key
CLAUDE_API_KEY=your-claude-key

# 支付配置
STRIPE_SECRET_KEY=your-stripe-secret
ALIPAY_APP_ID=your-alipay-app-id

# 邮件服务
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# 监控配置
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# 云服务配置
ALIYUN_ACCESS_KEY=your-aliyun-access-key
ALIYUN_SECRET_KEY=your-aliyun-secret-key
ALIYUN_REGION=cn-hangzhou
