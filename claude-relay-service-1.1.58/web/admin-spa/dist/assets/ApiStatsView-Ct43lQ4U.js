import{aR as B,r as $,c as J,aW as A,x as f,y as m,z as t,O as y,K as H,aq as Q,u as s,aa as G,f as X,P as r,C as P,L as C,Q as O,ac as F,q as Z,aU as tt,V as st,o as et,R as S,J as at,av as it}from"./vue-vendor-CKToUHZx.js";import{L as ot}from"./LogoTitle-C8aMR_x8.js";import{_ as K}from"./index-Ch5822Og.js";import{b as nt}from"./vendor-BDiMbLwQ.js";import lt from"./TutorialView-CVBo-wV5.js";/* empty css                                                                  */import"./element-plus-B8Fs_0jW.js";class rt{constructor(){this.baseURL=window.location.origin,this.isDev=!1}async request(n,a={}){try{this.isDev&&n.startsWith("/admin")&&(n="/webapi"+n);const l=await fetch(`${this.baseURL}${n}`,{headers:{"Content-Type":"application/json",...a.headers},...a}),c=await l.json();if(!l.ok)throw new Error(c.message||`请求失败: ${l.status}`);return c}catch(l){throw console.error("API Stats request error:",l),l}}async getKeyId(n){return this.request("/apiStats/api/get-key-id",{method:"POST",body:JSON.stringify({apiKey:n})})}async getUserStats(n){return this.request("/apiStats/api/user-stats",{method:"POST",body:JSON.stringify({apiId:n})})}async getUserModelStats(n,a="daily"){return this.request("/apiStats/api/user-model-stats",{method:"POST",body:JSON.stringify({apiId:n,period:a})})}async getOemSettings(){try{return await this.request("/admin/oem-settings")}catch(n){return console.error("Failed to load OEM settings:",n),{success:!0,data:{siteName:"Claude Relay Service",siteIcon:"",siteIconData:""}}}}}const D=new rt,M=B("apistats",()=>{const w=$(""),n=$(null),a=$(!1),l=$(!1),c=$(!0),e=$(""),i=$("daily"),d=$(null),v=$([]),_=$(null),h=$(null),x=$({siteName:"",siteIcon:"",siteIconData:""}),u=J(()=>{const o={requests:0,inputTokens:0,outputTokens:0,cacheCreateTokens:0,cacheReadTokens:0,allTokens:0,cost:0,formattedCost:"$0.000000"};return i.value==="daily"?_.value||o:h.value||o}),U=J(()=>{if(!d.value||!u.value)return{tokenUsage:0,costUsage:0,requestUsage:0};const o=u.value,g=d.value.limits;return{tokenUsage:g.tokenLimit>0?Math.min(o.allTokens/g.tokenLimit*100,100):0,costUsage:g.dailyCostLimit>0?Math.min(o.cost/g.dailyCostLimit*100,100):0,requestUsage:g.rateLimitRequests>0?Math.min(o.requests/g.rateLimitRequests*100,100):0}});async function T(){if(!w.value.trim()){e.value="请输入 API Key";return}a.value=!0,e.value="",d.value=null,v.value=[],n.value=null;try{const o=await D.getKeyId(w.value);if(o.success){n.value=o.data.id;const g=await D.getUserStats(n.value);if(g.success)d.value=g.data,await R(),e.value="",j();else throw new Error(g.message||"查询失败")}else throw new Error(o.message||"获取 API Key ID 失败")}catch(o){console.error("Query stats error:",o),e.value=o.message||"查询统计数据失败，请检查您的 API Key 是否正确",d.value=null,v.value=[],n.value=null}finally{a.value=!1}}async function R(){n.value&&(await Promise.all([L("daily"),L("monthly")]),await q(i.value))}async function L(o){try{const g=await D.getUserModelStats(n.value,o);if(g.success){const z=g.data||[],k={requests:0,inputTokens:0,outputTokens:0,cacheCreateTokens:0,cacheReadTokens:0,allTokens:0,cost:0,formattedCost:"$0.000000"};z.forEach(I=>{var W;k.requests+=I.requests||0,k.inputTokens+=I.inputTokens||0,k.outputTokens+=I.outputTokens||0,k.cacheCreateTokens+=I.cacheCreateTokens||0,k.cacheReadTokens+=I.cacheReadTokens||0,k.allTokens+=I.allTokens||0,k.cost+=((W=I.costs)==null?void 0:W.total)||0}),k.formattedCost=N(k.cost),o==="daily"?_.value=k:h.value=k}else console.warn(`Failed to load ${o} stats:`,g.message)}catch(g){console.error(`Load ${o} stats error:`,g)}}async function q(o="daily"){if(n.value){l.value=!0;try{const g=await D.getUserModelStats(n.value,o);if(g.success)v.value=g.data||[];else throw new Error(g.message||"加载模型统计失败")}catch(g){console.error("Load model stats error:",g),v.value=[]}finally{l.value=!1}}}async function E(o){i.value===o||l.value||(i.value=o,(o==="daily"&&!_.value||o==="monthly"&&!h.value)&&await L(o),await q(o))}async function b(){if(n.value){a.value=!0,e.value="",d.value=null,v.value=[];try{const o=await D.getUserStats(n.value);if(o.success)d.value=o.data,await R(),e.value="";else throw new Error(o.message||"查询失败")}catch(o){console.error("Load stats with apiId error:",o),e.value=o.message||"查询统计数据失败",d.value=null,v.value=[]}finally{a.value=!1}}}async function p(){c.value=!0;try{const o=await D.getOemSettings();o&&o.success&&o.data&&(x.value={...x.value,...o.data})}catch(o){console.error("Error loading OEM settings:",o),x.value={siteName:"Claude Relay Service",siteIcon:"",siteIconData:""}}finally{c.value=!1}}function N(o){return typeof o!="number"||o===0?"$0.000000":o>=1?"$"+o.toFixed(2):o>=.01?"$"+o.toFixed(4):"$"+o.toFixed(6)}function j(){if(n.value){const o=new URL(window.location);o.searchParams.set("apiId",n.value),window.history.pushState({},"",o)}}function V(){d.value=null,v.value=[],_.value=null,h.value=null,e.value="",i.value="daily",n.value=null}function Y(){w.value="",V()}return{apiKey:w,apiId:n,loading:a,modelStatsLoading:l,oemLoading:c,error:e,statsPeriod:i,statsData:d,modelStats:v,dailyStats:_,monthlyStats:h,oemSettings:x,currentPeriodData:u,usagePercentages:U,queryStats:T,loadAllPeriodStats:R,loadPeriodStats:L,loadModelStats:q,switchPeriod:E,loadStatsWithApiId:b,loadOemSettings:p,clearData:V,reset:Y}}),dt={class:"api-input-wide-card glass-strong rounded-3xl p-6 mb-8 shadow-xl"},ct={class:"max-w-4xl mx-auto"},ut={class:"api-input-grid grid grid-cols-1 lg:grid-cols-4"},ft={class:"lg:col-span-3"},mt=["disabled"],xt={class:"lg:col-span-1"},yt=["disabled"],pt={key:0,class:"fas fa-spinner loading-spinner"},gt={key:1,class:"fas fa-search"},vt={__name:"ApiKeyInput",setup(w){const n=M(),{apiKey:a,loading:l}=A(n),{queryStats:c}=n;return(e,i)=>(m(),f("div",dt,[i[6]||(i[6]=t("div",{class:"wide-card-title text-center mb-6"},[t("h2",{class:"text-2xl font-bold mb-2"},[t("i",{class:"fas fa-chart-line mr-3"}),y(" 使用统计查询 ")]),t("p",{class:"text-base text-gray-600"}," 查询您的 API Key 使用情况和统计数据 ")],-1)),t("div",ct,[t("div",ut,[t("div",ft,[i[3]||(i[3]=t("label",{class:"block text-sm font-medium mb-2 text-gray-700"},[t("i",{class:"fas fa-key mr-2"}),y(" 输入您的 API Key ")],-1)),H(t("input",{"onUpdate:modelValue":i[0]||(i[0]=d=>X(a)?a.value=d:null),type:"password",placeholder:"请输入您的 API Key (cr_...)",class:"wide-card-input w-full",disabled:s(l),onKeyup:i[1]||(i[1]=G((...d)=>s(c)&&s(c)(...d),["enter"]))},null,40,mt),[[Q,s(a)]])]),t("div",xt,[i[4]||(i[4]=t("label",{class:"hidden lg:block text-sm font-medium mb-2 text-gray-700"},"   ",-1)),t("button",{disabled:s(l)||!s(a).trim(),class:"btn btn-primary btn-query w-full h-full flex items-center justify-center gap-2",onClick:i[2]||(i[2]=(...d)=>s(c)&&s(c)(...d))},[s(l)?(m(),f("i",pt)):(m(),f("i",gt)),y(" "+r(s(l)?"查询中...":"查询统计"),1)],8,yt)])]),i[5]||(i[5]=t("div",{class:"security-notice mt-4"},[t("i",{class:"fas fa-shield-alt mr-2"}),y(" 您的 API Key 仅用于查询自己的统计数据，不会被存储或用于其他用途 ")],-1))])]))}},_t=K(vt,[["__scopeId","data-v-d80546e3"]]),bt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},ht={class:"card p-6"},$t={class:"space-y-3"},wt={class:"flex justify-between items-center"},kt={class:"font-medium text-gray-900"},St={class:"flex justify-between items-center"},Tt={class:"flex justify-between items-center"},It={class:"font-medium text-gray-900"},Ct={class:"flex justify-between items-center"},Lt={class:"font-medium text-gray-900"},Dt={class:"flex justify-between items-center"},Pt={key:0},At={key:0,class:"text-red-600 font-medium"},Kt={key:1,class:"text-orange-600 font-medium"},Mt={key:2,class:"text-gray-900 font-medium"},Rt={key:1,class:"text-gray-400 font-medium"},qt={class:"card p-6"},jt={class:"text-xl font-bold mb-4 flex items-center text-gray-900"},Ut={class:"text-sm font-normal text-gray-600 ml-2"},Et={class:"grid grid-cols-2 gap-4"},Nt={class:"stat-card text-center"},Ot={class:"text-3xl font-bold text-green-600"},Ft={class:"text-sm text-gray-600"},Vt={class:"stat-card text-center"},Wt={class:"text-3xl font-bold text-blue-600"},Jt={class:"text-sm text-gray-600"},Yt={class:"stat-card text-center"},zt={class:"text-3xl font-bold text-purple-600"},Bt={class:"text-sm text-gray-600"},Ht={class:"stat-card text-center"},Qt={class:"text-3xl font-bold text-yellow-600"},Gt={class:"text-sm text-gray-600"},Xt={__name:"StatsOverview",setup(w){const n=M(),{statsData:a,statsPeriod:l,currentPeriodData:c}=A(n),e=x=>{if(!x)return"无";try{return nt(x).format("YYYY年MM月DD日 HH:mm")}catch{return"格式错误"}},i=x=>x?new Date(x).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"",d=x=>x?new Date(x)<new Date:!1,v=x=>{if(!x)return!1;const T=(new Date(x)-new Date)/(1e3*60*60*24);return T>0&&T<=7},_=x=>(typeof x!="number"&&(x=parseInt(x)||0),x===0?"0":x>=1e6?(x/1e6).toFixed(1)+"M":x>=1e3?(x/1e3).toFixed(1)+"K":x.toLocaleString()),h=x=>({claude:"Claude",gemini:"Gemini",all:"全部模型"})[x]||x||"未知";return(x,u)=>(m(),f("div",bt,[t("div",ht,[u[8]||(u[8]=t("h3",{class:"text-xl font-bold mb-4 flex items-center text-gray-900"},[t("i",{class:"fas fa-info-circle mr-3 text-blue-500"}),y(" API Key 信息 ")],-1)),t("div",$t,[t("div",wt,[u[0]||(u[0]=t("span",{class:"text-gray-600"},"名称",-1)),t("span",kt,r(s(a).name),1)]),t("div",St,[u[1]||(u[1]=t("span",{class:"text-gray-600"},"状态",-1)),t("span",{class:P([s(a).isActive?"text-green-600":"text-red-600","font-medium"])},[t("i",{class:P([s(a).isActive?"fas fa-check-circle":"fas fa-times-circle","mr-1"])},null,2),y(" "+r(s(a).isActive?"活跃":"已停用"),1)],2)]),t("div",Tt,[u[2]||(u[2]=t("span",{class:"text-gray-600"},"权限",-1)),t("span",It,r(h(s(a).permissions)),1)]),t("div",Ct,[u[3]||(u[3]=t("span",{class:"text-gray-600"},"创建时间",-1)),t("span",Lt,r(e(s(a).createdAt)),1)]),t("div",Dt,[u[7]||(u[7]=t("span",{class:"text-gray-600"},"过期时间",-1)),s(a).expiresAt?(m(),f("div",Pt,[d(s(a).expiresAt)?(m(),f("div",At,u[4]||(u[4]=[t("i",{class:"fas fa-exclamation-circle mr-1"},null,-1),y(" 已过期 ",-1)]))):v(s(a).expiresAt)?(m(),f("div",Kt,[u[5]||(u[5]=t("i",{class:"fas fa-clock mr-1"},null,-1)),y(" "+r(i(s(a).expiresAt)),1)])):(m(),f("div",Mt,r(i(s(a).expiresAt)),1))])):(m(),f("div",Rt,u[6]||(u[6]=[t("i",{class:"fas fa-infinity mr-1"},null,-1),y(" 永不过期 ",-1)])))])])]),t("div",qt,[t("h3",jt,[u[9]||(u[9]=t("i",{class:"fas fa-chart-bar mr-3 text-green-500"},null,-1)),u[10]||(u[10]=y(" 使用统计概览 ",-1)),t("span",Ut,"("+r(s(l)==="daily"?"今日":"本月")+")",1)]),t("div",Et,[t("div",Nt,[t("div",Ot,r(_(s(c).requests)),1),t("div",Ft,r(s(l)==="daily"?"今日":"本月")+"请求数 ",1)]),t("div",Vt,[t("div",Wt,r(_(s(c).allTokens)),1),t("div",Jt,r(s(l)==="daily"?"今日":"本月")+"Token数 ",1)]),t("div",Yt,[t("div",zt,r(s(c).formattedCost||"$0.000000"),1),t("div",Bt,r(s(l)==="daily"?"今日":"本月")+"费用 ",1)]),t("div",Ht,[t("div",Qt,r(_(s(c).inputTokens)),1),t("div",Gt,r(s(l)==="daily"?"今日":"本月")+"输入Token ",1)])])])]))}},Zt=K(Xt,[["__scopeId","data-v-e7abc110"]]),ts={class:"card p-6"},ss={class:"text-xl font-bold mb-4 flex items-center text-gray-900"},es={class:"text-sm font-normal text-gray-600 ml-2"},as={class:"space-y-3"},is={class:"flex justify-between items-center"},os={class:"font-medium text-gray-900"},ns={class:"flex justify-between items-center"},ls={class:"font-medium text-gray-900"},rs={class:"flex justify-between items-center"},ds={class:"font-medium text-gray-900"},cs={class:"flex justify-between items-center"},us={class:"font-medium text-gray-900"},fs={class:"mt-4 pt-4 border-t border-gray-200"},ms={class:"flex justify-between items-center font-bold text-gray-900"},xs={class:"text-xl"},ys={__name:"TokenDistribution",setup(w){const n=M(),{statsPeriod:a,currentPeriodData:l}=A(n),c=e=>(typeof e!="number"&&(e=parseInt(e)||0),e===0?"0":e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toLocaleString());return(e,i)=>(m(),f("div",ts,[t("h3",ss,[i[0]||(i[0]=t("i",{class:"fas fa-coins mr-3 text-yellow-500"},null,-1)),i[1]||(i[1]=y(" Token 使用分布 ",-1)),t("span",es,"("+r(s(a)==="daily"?"今日":"本月")+")",1)]),t("div",as,[t("div",is,[i[2]||(i[2]=t("span",{class:"text-gray-600 flex items-center"},[t("i",{class:"fas fa-arrow-right mr-2 text-green-500"}),y(" 输入 Token ")],-1)),t("span",os,r(c(s(l).inputTokens)),1)]),t("div",ns,[i[3]||(i[3]=t("span",{class:"text-gray-600 flex items-center"},[t("i",{class:"fas fa-arrow-left mr-2 text-blue-500"}),y(" 输出 Token ")],-1)),t("span",ls,r(c(s(l).outputTokens)),1)]),t("div",rs,[i[4]||(i[4]=t("span",{class:"text-gray-600 flex items-center"},[t("i",{class:"fas fa-save mr-2 text-purple-500"}),y(" 缓存创建 Token ")],-1)),t("span",ds,r(c(s(l).cacheCreateTokens)),1)]),t("div",cs,[i[5]||(i[5]=t("span",{class:"text-gray-600 flex items-center"},[t("i",{class:"fas fa-download mr-2 text-orange-500"}),y(" 缓存读取 Token ")],-1)),t("span",us,r(c(s(l).cacheReadTokens)),1)])]),t("div",fs,[t("div",ms,[t("span",null,r(s(a)==="daily"?"今日":"本月")+"总计",1),t("span",xs,r(c(s(l).allTokens)),1)])])]))}},ps=K(ys,[["__scopeId","data-v-ff744b2f"]]),gs={class:"card p-6"},vs={class:"space-y-3"},_s={class:"flex justify-between items-center"},bs={class:"font-medium text-gray-900"},hs={class:"flex justify-between items-center"},$s={class:"font-medium text-gray-900"},ws={class:"flex justify-between items-center"},ks={class:"font-medium text-gray-900"},Ss={class:"flex justify-between items-center"},Ts={class:"font-medium text-gray-900"},Is={class:"flex justify-between items-center"},Cs={class:"font-medium text-gray-900"},Ls={key:0,class:"text-orange-600"},Ds={key:1,class:"text-green-600"},Ps={class:"flex justify-between items-center"},As={class:"font-medium text-gray-900"},Ks={key:0,class:"text-orange-600"},Ms={key:1,class:"text-green-600"},Rs={key:0,class:"card p-6 mt-6"},qs={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},js={key:0,class:"bg-amber-50 border border-amber-200 rounded-lg p-4"},Us={class:"space-y-2"},Es={class:"text-gray-800"},Ns={key:1,class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},Os={class:"space-y-2"},Fs={class:"text-gray-800"},Vs={__name:"LimitConfig",setup(w){const n=M(),{statsData:a}=A(n),l=c=>(typeof c!="number"&&(c=parseInt(c)||0),c===0?"0":c>=1e6?(c/1e6).toFixed(1)+"M":c>=1e3?(c/1e3).toFixed(1)+"K":c.toLocaleString());return(c,e)=>(m(),f("div",null,[t("div",gs,[e[10]||(e[10]=t("h3",{class:"text-xl font-bold mb-4 flex items-center text-gray-900"},[t("i",{class:"fas fa-shield-alt mr-3 text-red-500"}),y(" 限制配置 ")],-1)),t("div",vs,[t("div",_s,[e[0]||(e[0]=t("span",{class:"text-gray-600"},"Token 限制",-1)),t("span",bs,r(s(a).limits.tokenLimit>0?l(s(a).limits.tokenLimit):"无限制"),1)]),t("div",hs,[e[1]||(e[1]=t("span",{class:"text-gray-600"},"并发限制",-1)),t("span",$s,r(s(a).limits.concurrencyLimit>0?s(a).limits.concurrencyLimit:"无限制"),1)]),t("div",ws,[e[2]||(e[2]=t("span",{class:"text-gray-600"},"速率限制",-1)),t("span",ks,r(s(a).limits.rateLimitRequests>0&&s(a).limits.rateLimitWindow>0?`${s(a).limits.rateLimitRequests}次/${s(a).limits.rateLimitWindow}分钟`:"无限制"),1)]),t("div",Ss,[e[3]||(e[3]=t("span",{class:"text-gray-600"},"每日费用限制",-1)),t("span",Ts,r(s(a).limits.dailyCostLimit>0?"$"+s(a).limits.dailyCostLimit:"无限制"),1)]),t("div",Is,[e[6]||(e[6]=t("span",{class:"text-gray-600"},"模型限制",-1)),t("span",Cs,[s(a).restrictions.enableModelRestriction&&s(a).restrictions.restrictedModels.length>0?(m(),f("span",Ls,[e[4]||(e[4]=t("i",{class:"fas fa-exclamation-triangle mr-1"},null,-1)),y(" 限制 "+r(s(a).restrictions.restrictedModels.length)+" 个模型 ",1)])):(m(),f("span",Ds,e[5]||(e[5]=[t("i",{class:"fas fa-check-circle mr-1"},null,-1),y(" 允许所有模型 ",-1)])))])]),t("div",Ps,[e[9]||(e[9]=t("span",{class:"text-gray-600"},"客户端限制",-1)),t("span",As,[s(a).restrictions.enableClientRestriction&&s(a).restrictions.allowedClients.length>0?(m(),f("span",Ks,[e[7]||(e[7]=t("i",{class:"fas fa-exclamation-triangle mr-1"},null,-1)),y(" 限制 "+r(s(a).restrictions.allowedClients.length)+" 个客户端 ",1)])):(m(),f("span",Ms,e[8]||(e[8]=[t("i",{class:"fas fa-check-circle mr-1"},null,-1),y(" 允许所有客户端 ",-1)])))])])])]),s(a).restrictions.enableModelRestriction&&s(a).restrictions.restrictedModels.length>0||s(a).restrictions.enableClientRestriction&&s(a).restrictions.allowedClients.length>0?(m(),f("div",Rs,[e[17]||(e[17]=t("h3",{class:"text-xl font-bold mb-4 flex items-center text-gray-900"},[t("i",{class:"fas fa-list-alt mr-3 text-amber-500"}),y(" 详细限制信息 ")],-1)),t("div",qs,[s(a).restrictions.enableModelRestriction&&s(a).restrictions.restrictedModels.length>0?(m(),f("div",js,[e[12]||(e[12]=t("h4",{class:"font-bold text-amber-800 mb-3 flex items-center"},[t("i",{class:"fas fa-robot mr-2"}),y(" 受限模型列表 ")],-1)),t("div",Us,[(m(!0),f(O,null,F(s(a).restrictions.restrictedModels,i=>(m(),f("div",{key:i,class:"bg-white rounded px-3 py-2 text-sm border border-amber-200"},[e[11]||(e[11]=t("i",{class:"fas fa-ban mr-2 text-red-500"},null,-1)),t("span",Es,r(i),1)]))),128))]),e[13]||(e[13]=t("p",{class:"text-xs text-amber-700 mt-3"},[t("i",{class:"fas fa-info-circle mr-1"}),y(" 此 API Key 不能访问以上列出的模型 ")],-1))])):C("",!0),s(a).restrictions.enableClientRestriction&&s(a).restrictions.allowedClients.length>0?(m(),f("div",Ns,[e[15]||(e[15]=t("h4",{class:"font-bold text-blue-800 mb-3 flex items-center"},[t("i",{class:"fas fa-desktop mr-2"}),y(" 允许的客户端 ")],-1)),t("div",Os,[(m(!0),f(O,null,F(s(a).restrictions.allowedClients,i=>(m(),f("div",{key:i,class:"bg-white rounded px-3 py-2 text-sm border border-blue-200"},[e[14]||(e[14]=t("i",{class:"fas fa-check mr-2 text-green-500"},null,-1)),t("span",Fs,r(i),1)]))),128))]),e[16]||(e[16]=t("p",{class:"text-xs text-blue-700 mt-3"},[t("i",{class:"fas fa-info-circle mr-1"}),y(" 此 API Key 只能被以上列出的客户端使用 ")],-1))])):C("",!0)])])):C("",!0)]))}},Ws=K(Vs,[["__scopeId","data-v-f97c29cf"]]),Js={class:"card p-6"},Ys={class:"mb-6"},zs={class:"text-xl font-bold flex items-center text-gray-900"},Bs={class:"text-sm font-normal text-gray-600 ml-2"},Hs={key:0,class:"text-center py-8"},Qs={key:1,class:"space-y-4"},Gs={class:"flex justify-between items-start mb-3"},Xs={class:"font-bold text-lg text-gray-900"},Zs={class:"text-gray-600 text-sm"},te={class:"text-right"},se={class:"text-lg font-bold text-green-600"},ee={class:"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm"},ae={class:"bg-gray-50 rounded p-2"},ie={class:"font-medium text-gray-900"},oe={class:"bg-gray-50 rounded p-2"},ne={class:"font-medium text-gray-900"},le={class:"bg-gray-50 rounded p-2"},re={class:"font-medium text-gray-900"},de={class:"bg-gray-50 rounded p-2"},ce={class:"font-medium text-gray-900"},ue={key:2,class:"text-center py-8 text-gray-500"},fe={__name:"ModelUsageStats",setup(w){const n=M(),{statsPeriod:a,modelStats:l,modelStatsLoading:c}=A(n),e=i=>(typeof i!="number"&&(i=parseInt(i)||0),i===0?"0":i>=1e6?(i/1e6).toFixed(1)+"M":i>=1e3?(i/1e3).toFixed(1)+"K":i.toLocaleString());return(i,d)=>(m(),f("div",Js,[t("div",Ys,[t("h3",zs,[d[0]||(d[0]=t("i",{class:"fas fa-robot mr-3 text-indigo-500"},null,-1)),d[1]||(d[1]=y(" 模型使用统计 ",-1)),t("span",Bs,"("+r(s(a)==="daily"?"今日":"本月")+")",1)])]),s(c)?(m(),f("div",Hs,d[2]||(d[2]=[t("i",{class:"fas fa-spinner loading-spinner text-2xl mb-2 text-gray-600"},null,-1),t("p",{class:"text-gray-600"}," 加载模型统计数据中... ",-1)]))):s(l).length>0?(m(),f("div",Qs,[(m(!0),f(O,null,F(s(l),(v,_)=>{var h;return m(),f("div",{key:_,class:"model-usage-item"},[t("div",Gs,[t("div",null,[t("h4",Xs,r(v.model),1),t("p",Zs,r(v.requests)+" 次请求 ",1)]),t("div",te,[t("div",se,r(((h=v.formatted)==null?void 0:h.total)||"$0.000000"),1),d[3]||(d[3]=t("div",{class:"text-sm text-gray-600"}," 总费用 ",-1))])]),t("div",ee,[t("div",ae,[d[4]||(d[4]=t("div",{class:"text-gray-600"}," 输入 Token ",-1)),t("div",ie,r(e(v.inputTokens)),1)]),t("div",oe,[d[5]||(d[5]=t("div",{class:"text-gray-600"}," 输出 Token ",-1)),t("div",ne,r(e(v.outputTokens)),1)]),t("div",le,[d[6]||(d[6]=t("div",{class:"text-gray-600"}," 缓存创建 ",-1)),t("div",re,r(e(v.cacheCreateTokens)),1)]),t("div",de,[d[7]||(d[7]=t("div",{class:"text-gray-600"}," 缓存读取 ",-1)),t("div",ce,r(e(v.cacheReadTokens)),1)])])])}),128))])):(m(),f("div",ue,[d[8]||(d[8]=t("i",{class:"fas fa-chart-pie text-3xl mb-3"},null,-1)),t("p",null,"暂无"+r(s(a)==="daily"?"今日":"本月")+"模型使用数据",1)]))]))}},me=K(fe,[["__scopeId","data-v-ccf97793"]]),xe={class:"min-h-screen gradient-bg p-6"},ye={class:"glass-strong rounded-3xl p-6 mb-8 shadow-xl"},pe={class:"flex flex-col md:flex-row justify-between items-center gap-4"},ge={class:"flex items-center gap-3"},ve={class:"mb-8"},_e={class:"flex justify-center"},be={class:"inline-flex bg-white/10 backdrop-blur-xl rounded-full p-1 shadow-lg border border-white/20"},he={key:0,class:"tab-content"},$e={key:0,class:"mb-8"},we={class:"bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-red-800 backdrop-blur-sm"},ke={key:1,class:"fade-in"},Se={class:"glass-strong rounded-3xl p-6 shadow-xl"},Te={class:"mb-6 pb-6 border-b border-gray-200"},Ie={class:"flex flex-col md:flex-row items-start md:items-center justify-between gap-4"},Ce={class:"flex gap-2"},Le=["disabled"],De=["disabled"],Pe={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},Ae={key:1,class:"tab-content"},Ke={class:"glass-strong rounded-3xl shadow-xl"},Me={__name:"ApiStatsView",setup(w){const n=tt(),a=M(),l=$("stats"),{apiKey:c,apiId:e,loading:i,modelStatsLoading:d,oemLoading:v,error:_,statsPeriod:h,statsData:x,oemSettings:u}=A(a),{queryStats:U,switchPeriod:T,loadStatsWithApiId:R,loadOemSettings:L,reset:q}=a,E=b=>{(b.ctrlKey||b.metaKey)&&b.key==="Enter"&&(!i.value&&c.value.trim()&&U(),b.preventDefault()),b.key==="Escape"&&q()};return Z(()=>{console.log("API Stats Page loaded"),L();const b=n.query.apiId,p=n.query.apiKey;b&&b.match(/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i)?(e.value=b,R()):p&&p.length>10&&(c.value=p),document.addEventListener("keydown",E)}),st(()=>{document.removeEventListener("keydown",E)}),et(c,b=>{b||a.clearData()}),(b,p)=>{const N=it("router-link");return m(),f("div",xe,[t("div",ye,[t("div",pe,[S(ot,{loading:s(v),title:s(u).siteName,subtitle:l.value==="stats"?"API Key 使用统计":"使用教程","logo-src":s(u).siteIconData||s(u).siteIcon},null,8,["loading","title","subtitle","logo-src"]),t("div",ge,[S(N,{to:"/dashboard",class:"admin-button rounded-xl px-4 py-2 text-white transition-all duration-300 flex items-center gap-2"},{default:at(()=>p[4]||(p[4]=[t("i",{class:"fas fa-cog text-sm"},null,-1),t("span",{class:"text-sm font-medium"},"管理后台",-1)])),_:1,__:[4]})])])]),t("div",ve,[t("div",_e,[t("div",be,[t("button",{class:P(["tab-pill-button",l.value==="stats"?"active":""]),onClick:p[0]||(p[0]=j=>l.value="stats")},p[5]||(p[5]=[t("i",{class:"fas fa-chart-line mr-2"},null,-1),t("span",null,"统计查询",-1)]),2),t("button",{class:P(["tab-pill-button",l.value==="tutorial"?"active":""]),onClick:p[1]||(p[1]=j=>l.value="tutorial")},p[6]||(p[6]=[t("i",{class:"fas fa-graduation-cap mr-2"},null,-1),t("span",null,"使用教程",-1)]),2)])])]),l.value==="stats"?(m(),f("div",he,[S(_t),s(_)?(m(),f("div",$e,[t("div",we,[p[7]||(p[7]=t("i",{class:"fas fa-exclamation-triangle mr-2"},null,-1)),y(" "+r(s(_)),1)])])):C("",!0),s(x)?(m(),f("div",ke,[t("div",Se,[t("div",Te,[t("div",Ie,[p[10]||(p[10]=t("div",{class:"flex items-center gap-3"},[t("i",{class:"fas fa-clock text-blue-500 text-lg"}),t("span",{class:"text-lg font-medium text-gray-700"},"统计时间范围")],-1)),t("div",Ce,[t("button",{class:P([["period-btn",{active:s(h)==="daily"}],"px-6 py-2 text-sm font-medium flex items-center gap-2"]),disabled:s(i)||s(d),onClick:p[2]||(p[2]=j=>s(T)("daily"))},p[8]||(p[8]=[t("i",{class:"fas fa-calendar-day"},null,-1),y(" 今日 ",-1)]),10,Le),t("button",{class:P([["period-btn",{active:s(h)==="monthly"}],"px-6 py-2 text-sm font-medium flex items-center gap-2"]),disabled:s(i)||s(d),onClick:p[3]||(p[3]=j=>s(T)("monthly"))},p[9]||(p[9]=[t("i",{class:"fas fa-calendar-alt"},null,-1),y(" 本月 ",-1)]),10,De)])])]),S(Zt),t("div",Pe,[S(ps),S(Ws)]),S(me)])])):C("",!0)])):C("",!0),l.value==="tutorial"?(m(),f("div",Ae,[t("div",Ke,[S(lt)])])):C("",!0)])}}},Fe=K(Me,[["__scopeId","data-v-38cbdfe3"]]);export{Fe as default};
