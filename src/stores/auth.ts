import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'

interface User {
  id: string
  name: string
  email: string
  avatar?: string
}

interface LoginData {
  email: string
  password: string
  rememberMe: boolean
}

interface RegisterData {
  name: string
  email: string
  password: string
  acceptMarketing: boolean
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(true) // 初始状态设为 true，表示正在加载
  const isInitialized = ref(false) // 新增：标记是否已初始化

  // 计算属性
  const isAuthenticated = computed(() => !!user.value && !!token.value)

  // 初始化认证状态
  const initAuth = async () => {
    if (isInitialized.value) return // 避免重复初始化
    
    isLoading.value = true
    
    try {
      // 首先检查localStorage（remember me），然后检查sessionStorage
      const savedToken = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
      const savedUser = localStorage.getItem('auth_user') || sessionStorage.getItem('auth_user')
      
      if (savedToken && savedUser) {
        try {
          token.value = savedToken
          user.value = JSON.parse(savedUser)
          console.log('Auth restored from storage:', { email: user.value?.email })
        } catch (error) {
          console.error('Failed to parse saved user data:', error)
          clearAuth()
        }
      }
    } catch (error) {
      console.error('Failed to initialize auth:', error)
      clearAuth()
    } finally {
      isLoading.value = false
      isInitialized.value = true
    }
  }

  // 清除认证数据
  const clearAuth = () => {
    user.value = null
    token.value = null
    // 清除localStorage和sessionStorage中的数据
    localStorage.removeItem('auth_token')
    localStorage.removeItem('auth_user')
    sessionStorage.removeItem('auth_token')
    sessionStorage.removeItem('auth_user')
  }

  // 保存认证数据
  const saveAuth = (userData: User, authToken: string, rememberMe: boolean = false) => {
    user.value = userData
    token.value = authToken
    
    if (rememberMe) {
      // 记住登录状态，保存到localStorage（持久化）
      localStorage.setItem('auth_token', authToken)
      localStorage.setItem('auth_user', JSON.stringify(userData))
      // 清除sessionStorage中可能存在的数据
      sessionStorage.removeItem('auth_token')
      sessionStorage.removeItem('auth_user')
    } else {
      // 不记住登录状态，保存到sessionStorage（会话级别）
      sessionStorage.setItem('auth_token', authToken)
      sessionStorage.setItem('auth_user', JSON.stringify(userData))
      // 清除localStorage中可能存在的数据
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_user')
    }
  }

  // 模拟 API 延迟
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

  // 登录
  const login = async (loginData: LoginData) => {
    isLoading.value = true
    
    try {
      // 模拟 API 调用
      await delay(1500)
      
      // 模拟验证逻辑
      if (loginData.email === '<EMAIL>' && loginData.password === 'password') {
        const userData: User = {
          id: '1',
          name: 'Admin User',
          email: loginData.email,
          avatar: 'https://ui-avatars.com/api/?name=Admin+User&background=3758f9&color=fff'
        }
        
        const authToken = 'mock-jwt-token-' + Date.now()
        saveAuth(userData, authToken, loginData.rememberMe)
        console.log('Login successful, remember me:', loginData.rememberMe)
        
        return userData
      } else if (loginData.email === '<EMAIL>' && loginData.password === '123456') {
        const userData: User = {
          id: '2',
          name: 'Test User',
          email: loginData.email,
          avatar: 'https://ui-avatars.com/api/?name=Test+User&background=01a3a4&color=fff'
        }
        
        const authToken = 'mock-jwt-token-' + Date.now()
        saveAuth(userData, authToken, loginData.rememberMe)
        console.log('Login successful, remember me:', loginData.rememberMe)
        
        return userData
      } else {
        // 检查具体的错误类型
        if (loginData.email !== '<EMAIL>' && loginData.email !== '<EMAIL>') {
          throw { field: 'email', message: '没有找到使用该邮箱的账户' }
        } else {
          throw { field: 'password', message: '密码错误' }
        }
      }
    } catch (error: any) {
      if (error.field) {
        throw error
      }
      throw { message: '登录失败，请重试。' }
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterData) => {
    isLoading.value = true
    
    try {
      // 模拟 API 调用
      await delay(2000)
      
      // 模拟邮箱重复检查
      if (registerData.email === '<EMAIL>' || registerData.email === '<EMAIL>') {
        throw { field: 'email', message: '该邮箱已被注册' }
      }
      
      // 模拟成功注册
      const userData: User = {
        id: Date.now().toString(),
        name: registerData.name,
        email: registerData.email,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(registerData.name)}&background=3758f9&color=fff`
      }
      
      const authToken = 'mock-jwt-token-' + Date.now()
      saveAuth(userData, authToken, false) // 注册后默认不记住登录状态
      
      return userData
    } catch (error: any) {
      if (error.field) {
        throw error
      }
      throw { message: '注册失败，请重试。' }
    } finally {
      isLoading.value = false
    }
  }

  // Google 登录
  const loginWithGoogle = async () => {
    isLoading.value = true
    
    try {
      // 模拟 Google OAuth 流程
      await delay(1000)
      
      const userData: User = {
        id: 'google-' + Date.now(),
        name: 'Google User',
        email: '<EMAIL>',
        avatar: 'https://ui-avatars.com/api/?name=Google+User&background=4285f4&color=fff'
      }
      
      const authToken = 'mock-google-token-' + Date.now()
      saveAuth(userData, authToken, false) // 社交登录默认不记住登录状态
      
      return userData
    } catch (error) {
      throw { message: 'Google 登录失败，请重试。' }
    } finally {
      isLoading.value = false
    }
  }

  // GitHub 登录
  const loginWithGithub = async () => {
    isLoading.value = true
    
    try {
      // 模拟 GitHub OAuth 流程
      await delay(1000)
      
      const userData: User = {
        id: 'github-' + Date.now(),
        name: 'GitHub User',
        email: '<EMAIL>',
        avatar: 'https://ui-avatars.com/api/?name=GitHub+User&background=24292e&color=fff'
      }
      
      const authToken = 'mock-github-token-' + Date.now()
      saveAuth(userData, authToken, false) // 社交登录默认不记住登录状态
      
      return userData
    } catch (error) {
      throw { message: 'GitHub 登录失败，请重试。' }
    } finally {
      isLoading.value = false
    }
  }

  // Google 注册
  const registerWithGoogle = async () => {
    return loginWithGoogle() // 对于社交登录，登录和注册是同一个流程
  }

  // GitHub 注册
  const registerWithGithub = async () => {
    return loginWithGithub() // 对于社交登录，登录和注册是同一个流程
  }

  // 忘记密码
  const forgotPassword = async (email: string) => {
    isLoading.value = true
    
    try {
      // 模拟 API 调用
      await delay(1500)
      
      // 模拟邮箱验证
      if (!email.includes('@')) {
        throw { field: 'email', message: '请输入有效的邮箱地址' }
      }
      
      // 模拟发送邮件成功
      return { message: '密码重置邮件已发送' }
    } catch (error: any) {
      if (error.field) {
        throw error
      }
      throw { message: '发送失败，请稍后重试。' }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = () => {
    console.log('Logging out user')
    clearAuth()
  }

  // 更新用户信息
  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      const updatedUserData = JSON.stringify(user.value)
      
      // 更新localStorage或sessionStorage中的用户数据，取决于数据存储在哪里
      if (localStorage.getItem('auth_user')) {
        localStorage.setItem('auth_user', updatedUserData)
      } else if (sessionStorage.getItem('auth_user')) {
        sessionStorage.setItem('auth_user', updatedUserData)
      }
    }
  }

  // 自动初始化认证状态
  initAuth()

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isLoading: readonly(isLoading),
    isInitialized: readonly(isInitialized),
    isAuthenticated,
    
    // 方法
    initAuth,
    login,
    register,
    loginWithGoogle,
    loginWithGithub,
    registerWithGoogle,
    registerWithGithub,
    forgotPassword,
    logout,
    updateUser,
    clearAuth
  }
})