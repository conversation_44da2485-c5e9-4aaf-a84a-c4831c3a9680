import type { RouteRecordRaw } from 'vue-router'
import AdminLayout from '@/layouts/AdminLayout.vue'
import { adminAuthGuard } from '@/router/guards/adminAuth'

export const adminRoutes: RouteRecordRaw[] = [
  {
    path: '/admin/login',
    name: 'admin-login',
    component: () => import('@/views/admin/LoginView.vue')
  },
  {
    path: '/admin/diagnostic',
    name: 'admin-diagnostic',
    component: () => import('@/views/admin/DiagnosticView.vue')
  },
  {
    path: '/admin',
    component: AdminLayout,
    beforeEnter: adminAuthGuard,
    children: [
      {
        path: '',
        name: 'admin-dashboard',
        component: () => import('@/views/admin/DashboardView.vue'),
        meta: { title: '管理员仪表盘' }
      },
      {
        path: 'users',
        name: 'admin-users',
        component: () => import('@/views/admin/UsersView.vue'),
        meta: { 
          title: '用户管理',
          permission: 'users.view'
        }
      },
      {
        path: 'api-keys',
        name: 'admin-api-keys',
        component: () => import('@/views/admin/ApiKeysView.vue'),
        meta: { 
          title: 'API密钥管理',
          permission: 'api.view'
        }
      },
      {
        path: 'usage',
        name: 'admin-usage',
        component: () => import('@/views/admin/UsageView.vue'),
        meta: { 
          title: '使用统计监控',
          permission: 'stats.view'
        }
      },
      {
        path: 'subscriptions',
        name: 'admin-subscriptions',
        component: () => import('@/views/admin/SubscriptionsView.vue'),
        meta: { 
          title: '订阅套餐管理',
          permission: 'subscription.view'
        }
      },
      {
        path: 'settings',
        name: 'admin-settings',
        component: () => import('@/views/admin/SettingsView.vue'),
        meta: { 
          title: '系统配置',
          role: 'super_admin'
        }
      },
      {
        path: 'finance',
        name: 'admin-finance',
        component: () => import('@/views/admin/FinanceView.vue'),
        meta: { 
          title: '财务报表',
          role: 'super_admin'
        }
      }
    ]
  }
]