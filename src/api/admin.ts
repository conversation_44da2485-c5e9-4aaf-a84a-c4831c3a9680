/**
 * Admin API 接口定义
 * 为管理员功能提供统一的API调用接口
 * 后续可直接连接到真实的后端API
 */

import { apiClient, type ApiResponse, type PaginatedResponse } from './client'

// ==================== 类型定义 ====================

export interface AdminUser {
  id: string
  username: string
  email: string
  role: 'super_admin' | 'admin' | 'operator'
  permissions: string[]
  avatar?: string
  createdAt: Date
  lastActiveAt: Date
}

export interface AdminLoginData {
  username: string
  password: string
}

export interface AdminLoginResponse {
  user: AdminUser
  token: string
  refreshToken?: string
}

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  plan: 'free' | 'basic' | 'pro' | 'enterprise'
  status: 'active' | 'inactive' | 'banned'
  apiCalls: number
  createdAt: Date
  lastActive: Date
}

export interface ApiKey {
  id: string
  name: string
  key: string
  userId: string
  permissions: string[]
  usageCount: number
  lastUsed?: Date
  status: 'active' | 'disabled'
  createdAt: Date
}

export interface UsageStats {
  totalRequests: number
  uniqueUsers: number
  averageResponseTime: number
  errorRate: number
  topEndpoints: Array<{
    endpoint: string
    count: number
  }>
}

export interface Subscription {
  id: string
  userId: string
  plan: string
  status: 'active' | 'canceled' | 'expired'
  currentPeriodStart: Date
  currentPeriodEnd: Date
  cancelAtPeriodEnd: boolean
}

// ==================== API 查询参数 ====================

export interface UserQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: string
  plan?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface ApiKeyQueryParams {
  page?: number
  pageSize?: number
  userId?: string
  status?: string
  search?: string
}

export interface UsageQueryParams {
  startDate?: string
  endDate?: string
  userId?: string
  endpoint?: string
}

// ==================== Admin API 类 ====================

export class AdminAPI {
  
  // ========== 认证相关 ==========
  
  /**
   * 管理员登录
   * TODO: 后续连接真实认证API
   */
  static async login(credentials: AdminLoginData): Promise<ApiResponse<AdminLoginResponse>> {
    // 暂时使用模拟数据，保持与现有store逻辑兼容
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟认证逻辑
        if (credentials.username === 'superadmin' && credentials.password === 'admin123') {
          resolve({
            success: true,
            data: {
              user: {
                id: 'admin-1',
                username: 'superadmin',
                email: '<EMAIL>',
                role: 'super_admin',
                permissions: ['*'],
                avatar: 'https://ui-avatars.com/api/?name=Super+Admin&background=ef4444&color=fff',
                createdAt: new Date(),
                lastActiveAt: new Date()
              },
              token: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbi0xIiwidXNlcm5hbWUiOiJzdXBlcmFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaWF0Ijoke0RhdGUubm93KCl9LCJleHAiOiR7RGF0ZS5ub3coKSArIDI0ICogNjAgKiA2MCAqIDEwMDB9fQ.mock-signature-${Date.now()}`
            }
          })
        } else {
          reject({
            success: false,
            message: '用户名或密码错误',
            code: 401
          })
        }
      }, 1500)
    })
  }

  /**
   * 验证管理员权限
   * TODO: 后续连接真实权限验证API
   */
  static async verifyPermission(permission: string): Promise<ApiResponse<boolean>> {
    return apiClient.post('/admin/verify-permission', { permission })
  }

  /**
   * 刷新访问令牌
   */
  static async refreshToken(refreshToken: string): Promise<ApiResponse<{ token: string }>> {
    return apiClient.post('/admin/refresh-token', { refreshToken })
  }

  // ========== 用户管理 ==========

  /**
   * 获取用户列表
   */
  static async getUsers(params?: UserQueryParams): Promise<ApiResponse<PaginatedResponse<User>>> {
    // TODO: 替换为真实API调用
    // return apiClient.get('/admin/users', params)
    
    // 暂时返回模拟数据
    return Promise.resolve({
      success: true,
      data: {
        items: [], // 现有的模拟用户数据
        total: 0,
        page: params?.page || 1,
        pageSize: params?.pageSize || 20,
        totalPages: 0
      }
    })
  }

  /**
   * 获取单个用户详情
   */
  static async getUser(id: string): Promise<ApiResponse<User>> {
    return apiClient.get(`/admin/users/${id}`)
  }

  /**
   * 更新用户信息
   */
  static async updateUser(id: string, data: Partial<User>): Promise<ApiResponse<User>> {
    return apiClient.put(`/admin/users/${id}`, data)
  }

  /**
   * 禁用/启用用户
   */
  static async toggleUserStatus(id: string, status: 'active' | 'banned'): Promise<ApiResponse<User>> {
    return apiClient.patch(`/admin/users/${id}/status`, { status })
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/admin/users/${id}`)
  }

  // ========== API密钥管理 ==========

  /**
   * 获取API密钥列表
   */
  static async getApiKeys(params?: ApiKeyQueryParams): Promise<ApiResponse<PaginatedResponse<ApiKey>>> {
    return apiClient.get('/admin/api-keys', params)
  }

  /**
   * 创建API密钥
   */
  static async createApiKey(data: Partial<ApiKey>): Promise<ApiResponse<ApiKey>> {
    return apiClient.post('/admin/api-keys', data)
  }

  /**
   * 更新API密钥
   */
  static async updateApiKey(id: string, data: Partial<ApiKey>): Promise<ApiResponse<ApiKey>> {
    return apiClient.put(`/admin/api-keys/${id}`, data)
  }

  /**
   * 禁用/启用API密钥
   */
  static async toggleApiKeyStatus(id: string, status: 'active' | 'disabled'): Promise<ApiResponse<ApiKey>> {
    return apiClient.patch(`/admin/api-keys/${id}/status`, { status })
  }

  /**
   * 删除API密钥
   */
  static async deleteApiKey(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/admin/api-keys/${id}`)
  }

  // ========== 使用统计 ==========

  /**
   * 获取使用统计概览
   */
  static async getUsageStats(params?: UsageQueryParams): Promise<ApiResponse<UsageStats>> {
    return apiClient.get('/admin/usage/stats', params)
  }

  /**
   * 获取详细使用数据
   */
  static async getUsageDetails(params?: UsageQueryParams): Promise<ApiResponse<any[]>> {
    return apiClient.get('/admin/usage/details', params)
  }

  // ========== 订阅管理 ==========

  /**
   * 获取订阅列表
   */
  static async getSubscriptions(params?: any): Promise<ApiResponse<PaginatedResponse<Subscription>>> {
    return apiClient.get('/admin/subscriptions', params)
  }

  /**
   * 更新订阅
   */
  static async updateSubscription(id: string, data: Partial<Subscription>): Promise<ApiResponse<Subscription>> {
    return apiClient.put(`/admin/subscriptions/${id}`, data)
  }

  /**
   * 取消订阅
   */
  static async cancelSubscription(id: string): Promise<ApiResponse<Subscription>> {
    return apiClient.post(`/admin/subscriptions/${id}/cancel`)
  }

  // ========== 系统配置 ==========

  /**
   * 获取系统配置
   */
  static async getSystemSettings(): Promise<ApiResponse<Record<string, any>>> {
    return apiClient.get('/admin/settings')
  }

  /**
   * 更新系统配置
   */
  static async updateSystemSettings(settings: Record<string, any>): Promise<ApiResponse<Record<string, any>>> {
    return apiClient.put('/admin/settings', settings)
  }

  // ========== 财务报表 ==========

  /**
   * 获取财务报表数据
   */
  static async getFinanceReport(params?: {
    startDate?: string
    endDate?: string
    type?: 'revenue' | 'users' | 'subscriptions'
  }): Promise<ApiResponse<any>> {
    return apiClient.get('/admin/finance/report', params)
  }

  /**
   * 导出财务报表
   */
  static async exportFinanceReport(params?: any): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.post('/admin/finance/export', params)
  }
}

// ==================== 导出 ====================

export default AdminAPI

/**
 * 使用示例：
 * 
 * // 登录
 * const loginResult = await AdminAPI.login({ username: 'admin', password: 'password' })
 * 
 * // 获取用户列表
 * const users = await AdminAPI.getUsers({ page: 1, pageSize: 20, status: 'active' })
 * 
 * // 更新用户
 * const updatedUser = await AdminAPI.updateUser('user-id', { status: 'banned' })
 */