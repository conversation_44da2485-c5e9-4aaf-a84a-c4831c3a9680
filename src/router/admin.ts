import type { RouteRecordRaw } from 'vue-router'
import AdminLayout from '@/layouts/AdminLayout.vue'
import DashboardView from '@/views/admin/DashboardView.vue'
import UsersView from '@/views/admin/UsersView.vue'
import ApiKeysView from '@/views/admin/ApiKeysView.vue'
import UsageView from '@/views/admin/UsageView.vue'
import SubscriptionsView from '@/views/admin/SubscriptionsView.vue'
import SettingsView from '@/views/admin/SettingsView.vue'
import FinanceView from '@/views/admin/FinanceView.vue'
import LoginView from '@/views/admin/LoginView.vue'
import DiagnosticView from '@/views/admin/DiagnosticView.vue'
import { adminAuthGuard } from '@/router/guards/adminAuth'

export const adminRoutes: RouteRecordRaw[] = [
  {
    path: '/admin/login',
    name: 'admin-login',
    component: LoginView
  },
  {
    path: '/admin/diagnostic',
    name: 'admin-diagnostic',
    component: DiagnosticView
  },
  {
    path: '/admin',
    component: AdminLayout,
    beforeEnter: adminAuthGuard,
    children: [
      {
        path: '',
        name: 'admin-dashboard',
        component: DashboardView,
        meta: { title: '管理员仪表盘' }
      },
      {
        path: 'users',
        name: 'admin-users',
        component: UsersView,
        meta: { 
          title: '用户管理',
          permission: 'users.view'
        }
      },
      {
        path: 'api-keys',
        name: 'admin-api-keys',
        component: ApiKeysView,
        meta: { 
          title: 'API密钥管理',
          permission: 'api.view'
        }
      },
      {
        path: 'usage',
        name: 'admin-usage',
        component: UsageView,
        meta: { 
          title: '使用统计监控',
          permission: 'stats.view'
        }
      },
      {
        path: 'subscriptions',
        name: 'admin-subscriptions',
        component: SubscriptionsView,
        meta: { 
          title: '订阅套餐管理',
          permission: 'subscription.view'
        }
      },
      {
        path: 'settings',
        name: 'admin-settings',
        component: SettingsView,
        meta: { 
          title: '系统配置',
          role: 'super_admin'
        }
      },
      {
        path: 'finance',
        name: 'admin-finance',
        component: FinanceView,
        meta: { 
          title: '财务报表',
          role: 'super_admin'
        }
      }
    ]
  }
]