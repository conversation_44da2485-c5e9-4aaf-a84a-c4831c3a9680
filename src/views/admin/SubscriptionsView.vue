<template>
  <div class="p-6">
    <div class="mb-8 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">订阅套餐管理</h1>
        <p class="mt-1 text-sm text-gray-600">管理系统套餐和用户订阅</p>
      </div>
      <button
        @click="showEditPlanModal = true"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        <PlusIcon class="-ml-1 mr-2 h-5 w-5" />
        编辑套餐
      </button>
    </div>

    <!-- 套餐概览 -->
    <div class="grid grid-cols-1 gap-6 mb-8 lg:grid-cols-4">
      <div v-for="plan in plans" :key="plan.id" class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">{{ plan.name }}</h3>
            <span
              :class="[
                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                plan.popular ? 'bg-indigo-100 text-indigo-800' : ''
              ]"
            >
              {{ plan.popular ? '热门' : '' }}
            </span>
          </div>
          <div class="mt-3">
            <p class="text-3xl font-semibold text-gray-900">
              ¥{{ plan.price }}
              <span class="text-base font-normal text-gray-500">/月</span>
            </p>
            <p class="mt-1 text-sm text-gray-500">年付 ¥{{ plan.yearlyPrice }}/年</p>
          </div>
          <div class="mt-4">
            <p class="text-sm font-medium text-gray-900">{{ plan.subscribers }} 位订阅者</p>
            <p class="text-sm text-gray-500">月收入: ¥{{ (plan.subscribers * plan.price).toLocaleString() }}</p>
          </div>
          <div class="mt-4">
            <button
              @click="editPlan(plan)"
              class="text-sm text-indigo-600 hover:text-indigo-900"
            >
              编辑套餐
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 订阅列表 -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          最近订阅
        </h3>
        <div class="mt-3 grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div>
            <label for="search" class="sr-only">搜索订阅</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="search"
                v-model="searchQuery"
                type="text"
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="搜索用户..."
              >
            </div>
          </div>
          <div>
            <select
              v-model="filterPlan"
              class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="">所有套餐</option>
              <option v-for="plan in plans" :key="plan.id" :value="plan.id">
                {{ plan.name }}
              </option>
            </select>
          </div>
          <div>
            <select
              v-model="filterStatus"
              class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="">所有状态</option>
              <option value="active">活跃</option>
              <option value="canceled">已取消</option>
              <option value="expired">已过期</option>
            </select>
          </div>
        </div>
      </div>
      
      <ul class="divide-y divide-gray-200">
        <li v-for="subscription in filteredSubscriptions" :key="subscription.id">
          <div class="px-4 py-4 sm:px-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <img
                    class="h-10 w-10 rounded-full"
                    :src="subscription.userAvatar || `https://ui-avatars.com/api/?name=${subscription.userName}&background=random`"
                    :alt="subscription.userName"
                  >
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">
                    {{ subscription.userName }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ subscription.userEmail }}
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-6">
                <div>
                  <p class="text-sm font-medium text-gray-900">
                    {{ getPlanById(subscription.planId)?.name }}
                  </p>
                  <p class="text-sm text-gray-500">
                    {{ subscription.billingCycle === 'monthly' ? '月付' : '年付' }}
                  </p>
                </div>
                <div>
                  <span
                    :class="[
                      'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                      subscription.status === 'active' ? 'bg-green-100 text-green-800' :
                      subscription.status === 'canceled' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    ]"
                  >
                    {{ getStatusName(subscription.status) }}
                  </span>
                </div>
                <div class="text-sm text-gray-500">
                  <p>开始: {{ formatDate(subscription.startDate) }}</p>
                  <p>{{ subscription.status === 'active' ? '下次账单' : '结束' }}: {{ formatDate(subscription.endDate) }}</p>
                </div>
                <div>
                  <button
                    @click="viewSubscriptionDetails(subscription)"
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    查看详情
                  </button>
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>

    <!-- 编辑套餐模态框 -->
    <div v-if="showEditPlanModal" class="fixed z-10 inset-0 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeEditPlanModal"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              编辑套餐信息
            </h3>
            
            <div class="space-y-4">
              <div v-for="plan in plans" :key="plan.id" class="border rounded-lg p-4">
                <div class="space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">套餐名称</label>
                    <input
                      v-model="plan.name"
                      type="text"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                  </div>
                  
                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700">月付价格</label>
                      <input
                        v-model.number="plan.price"
                        type="number"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      >
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700">年付价格</label>
                      <input
                        v-model.number="plan.yearlyPrice"
                        type="number"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      >
                    </div>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700">API调用限制/月</label>
                    <input
                      v-model.number="plan.apiLimit"
                      type="number"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                  </div>
                  
                  <div class="flex items-center">
                    <input
                      v-model="plan.popular"
                      type="checkbox"
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    >
                    <label class="ml-2 block text-sm text-gray-900">
                      标记为热门套餐
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="savePlans"
              type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              保存
            </button>
            <button
              @click="closeEditPlanModal"
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  MagnifyingGlassIcon,
  PlusIcon
} from '@/utils/icons'

interface Plan {
  id: string
  name: string
  price: number
  yearlyPrice: number
  subscribers: number
  popular: boolean
  apiLimit: number
}

interface Subscription {
  id: string
  userId: string
  userName: string
  userEmail: string
  userAvatar?: string
  planId: string
  status: 'active' | 'canceled' | 'expired'
  billingCycle: 'monthly' | 'yearly'
  startDate: Date
  endDate: Date
  amount: number
}

// 套餐数据
const plans = ref<Plan[]>([
  {
    id: 'free',
    name: '免费版',
    price: 0,
    yearlyPrice: 0,
    subscribers: 8420,
    popular: false,
    apiLimit: 1000
  },
  {
    id: 'basic',
    name: '基础版',
    price: 99,
    yearlyPrice: 990,
    subscribers: 2156,
    popular: false,
    apiLimit: 10000
  },
  {
    id: 'pro',
    name: '专业版',
    price: 299,
    yearlyPrice: 2990,
    subscribers: 1342,
    popular: true,
    apiLimit: 100000
  },
  {
    id: 'enterprise',
    name: '企业版',
    price: 999,
    yearlyPrice: 9990,
    subscribers: 344,
    popular: false,
    apiLimit: -1 // 无限制
  }
])

// 订阅数据
const subscriptions = ref<Subscription[]>([
  {
    id: '1',
    userId: '1',
    userName: '张三',
    userEmail: '<EMAIL>',
    planId: 'pro',
    status: 'active',
    billingCycle: 'monthly',
    startDate: new Date('2024-12-01'),
    endDate: new Date('2025-02-01'),
    amount: 299
  },
  {
    id: '2',
    userId: '2',
    userName: '李四',
    userEmail: '<EMAIL>',
    planId: 'basic',
    status: 'active',
    billingCycle: 'yearly',
    startDate: new Date('2024-11-15'),
    endDate: new Date('2025-11-15'),
    amount: 990
  },
  {
    id: '3',
    userId: '3',
    userName: '王五',
    userEmail: '<EMAIL>',
    planId: 'pro',
    status: 'canceled',
    billingCycle: 'monthly',
    startDate: new Date('2024-06-01'),
    endDate: new Date('2024-12-01'),
    amount: 299
  },
  {
    id: '4',
    userId: '4',
    userName: '赵六',
    userEmail: '<EMAIL>',
    planId: 'enterprise',
    status: 'active',
    billingCycle: 'yearly',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2025-01-01'),
    amount: 9990
  }
])

const searchQuery = ref('')
const filterPlan = ref('')
const filterStatus = ref('')
const showEditPlanModal = ref(false)

const filteredSubscriptions = computed(() => {
  return subscriptions.value.filter(sub => {
    const matchesSearch = !searchQuery.value || 
      sub.userName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      sub.userEmail.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesPlan = !filterPlan.value || sub.planId === filterPlan.value
    const matchesStatus = !filterStatus.value || sub.status === filterStatus.value
    
    return matchesSearch && matchesPlan && matchesStatus
  })
})

const getPlanById = (planId: string) => {
  return plans.value.find(p => p.id === planId)
}

const getStatusName = (status: string) => {
  const statusNames: Record<string, string> = {
    active: '活跃',
    canceled: '已取消',
    expired: '已过期'
  }
  return statusNames[status] || status
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date)
}

const editPlan = (plan: Plan) => {
  showEditPlanModal.value = true
}

const closeEditPlanModal = () => {
  showEditPlanModal.value = false
}

const savePlans = () => {
  // 这里添加保存套餐的逻辑
  console.log('保存套餐信息')
  closeEditPlanModal()
}

const viewSubscriptionDetails = (subscription: Subscription) => {
  console.log('查看订阅详情:', subscription)
  // 这里可以打开详情模态框或跳转到详情页
}
</script>