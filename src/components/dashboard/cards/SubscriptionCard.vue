<template>
  <div class="subscription-card">
    <div class="subscription-header">
      <div class="plan-info">
        <div class="plan-name">{{ subscription.name }}</div>
        <div class="plan-status" :class="`status--${subscription.status}`">
          <i :class="statusIcon"></i>
          {{ statusText }}
        </div>
      </div>
      <div class="plan-badge" :class="`badge--${subscription.type}`">
        {{ typeText }}
      </div>
    </div>
    
    <div class="subscription-content">
      <div class="usage-progress">
        <div class="progress-header">
          <span>本月使用量</span>
          <span class="progress-text">
            {{ formatNumber(currentUsage) }} / {{ formatNumber(subscription.limits.monthlyRequests) }}
          </span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${usagePercentage}%` }"
            :class="{ 'progress-fill--warning': usagePercentage > 80, 'progress-fill--danger': usagePercentage > 95 }"
          ></div>
        </div>
        <div class="progress-footer">
          <span class="remaining">剩余 {{ formatNumber(remainingRequests) }} 次</span>
          <span class="percentage">{{ usagePercentage.toFixed(1) }}%</span>
        </div>
      </div>
      
      <div class="subscription-details">
        <div class="detail-row">
          <span class="detail-label">速率限制</span>
          <span class="detail-value">{{ subscription.limits.rateLimit }}/分钟</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">存储空间</span>
          <span class="detail-value">{{ subscription.limits.storage }}MB</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">开始日期</span>
          <span class="detail-value">{{ formatDate(subscription.startDate) }}</span>
        </div>
        <div v-if="subscription.endDate" class="detail-row">
          <span class="detail-label">到期日期</span>
          <span class="detail-value" :class="{ 'text-danger': isExpiringSoon }">
            {{ formatDate(subscription.endDate) }}
            <span v-if="isExpiringSoon" class="expiring-warning">
              <i class="ri-alarm-warning-line"></i>
              即将到期
            </span>
          </span>
        </div>
        <div class="detail-row">
          <span class="detail-label">自动续费</span>
          <span class="detail-value">
            <i :class="subscription.autoRenew ? 'ri-check-line text-success' : 'ri-close-line text-danger'"></i>
            {{ subscription.autoRenew ? '已开启' : '已关闭' }}
          </span>
        </div>
      </div>
      
      <div class="pricing-info">
        <div class="pricing-row">
          <span class="pricing-label">月付价格</span>
          <span class="pricing-value">￥{{ subscription.pricing.monthly }}/月</span>
        </div>
        <div class="pricing-row">
          <span class="pricing-label">年付价格</span>
          <span class="pricing-value">
            ￥{{ subscription.pricing.annual }}/年
            <span class="discount">
              省￥{{ (subscription.pricing.monthly * 12 - subscription.pricing.annual).toFixed(0) }}
            </span>
          </span>
        </div>
      </div>
      
      <div class="subscription-features">
        <h4>套餐特性</h4>
        <ul class="features-list">
          <li v-for="feature in subscription.features" :key="feature" class="feature-item">
            <i class="ri-check-line"></i>
            {{ feature }}
          </li>
        </ul>
      </div>
    </div>
    
    <div class="subscription-actions">
      <router-link to="/dashboard/subscription" class="btn btn-primary">
        管理套餐
      </router-link>
      <button v-if="canUpgrade" class="btn btn-outline">
        升级套餐
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { SubscriptionPlan, PlanType, PlanStatus } from '@/types/dashboard'
import { useDashboardStore } from '@/stores/dashboard'

interface Props {
  subscription: SubscriptionPlan
}

const props = defineProps<Props>()

const dashboardStore = useDashboardStore()

const currentUsage = computed(() => 
  dashboardStore.overview?.usage.monthlyUsage || 0
)

const remainingRequests = computed(() => 
  Math.max(0, props.subscription.limits.monthlyRequests - currentUsage.value)
)

const usagePercentage = computed(() => 
  Math.min(100, (currentUsage.value / props.subscription.limits.monthlyRequests) * 100)
)

const statusIcon = computed(() => {
  const icons = {
    active: 'ri-check-line',
    inactive: 'ri-pause-line',
    expired: 'ri-time-line',
    cancelled: 'ri-close-line'
  }
  return icons[props.subscription.status] || 'ri-question-line'
})

const statusText = computed(() => {
  const texts = {
    active: '活跃中',
    inactive: '未激活',
    expired: '已过期',
    cancelled: '已取消'
  }
  return texts[props.subscription.status] || props.subscription.status
})

const typeText = computed(() => {
  const texts = {
    free: '免费版',
    basic: '基础版',
    pro: '专业版',
    enterprise: '企业版'
  }
  return texts[props.subscription.type] || props.subscription.type
})

const isExpiringSoon = computed(() => {
  if (!props.subscription.endDate) return false
  const daysUntilExpiry = Math.ceil(
    (props.subscription.endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  )
  return daysUntilExpiry <= 7 && daysUntilExpiry > 0
})

const canUpgrade = computed(() => {
  return props.subscription.type !== 'enterprise' && props.subscription.status === 'active'
})

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
  return num.toString()
}

const formatDate = (date: Date): string => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}
</script>

<style scoped>
.subscription-card {
  background: white;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  padding: 20px;
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.plan-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.plan-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  gap: 4px;
}

.status--active {
  color: #22c55e;
}

.status--inactive {
  color: #9ca3af;
}

.status--expired,
.status--cancelled {
  color: #ef4444;
}

.plan-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.badge--free {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.badge--basic {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.badge--pro {
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.badge--enterprise {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.subscription-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.usage-progress {
  background: var(--bg-muted-color);
  border-radius: var(--radius);
  padding: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.progress-text {
  font-weight: 600;
  color: var(--color-text-primary);
}

.progress-bar {
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: var(--theme-color);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-fill--warning {
  background: #f59e0b;
}

.progress-fill--danger {
  background: #ef4444;
}

.progress-footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--color-text-secondary);
}

.subscription-details,
.pricing-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row,
.pricing-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.detail-label,
.pricing-label {
  color: var(--color-text-secondary);
}

.detail-value,
.pricing-value {
  color: var(--color-text-primary);
  font-weight: 500;
}

.expiring-warning {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
  font-size: 12px;
  color: #ef4444;
}

.expiring-warning i {
  margin-right: 4px;
}

.text-success {
  color: #22c55e;
}

.text-danger {
  color: #ef4444;
}

.discount {
  font-size: 12px;
  color: #22c55e;
  margin-left: 8px;
}

.subscription-features h4 {
  font-size: 16px;
  color: var(--color-text-primary);
  margin-bottom: 12px;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--color-text-regular);
}

.feature-item i {
  color: #22c55e;
  margin-right: 8px;
  font-size: 16px;
}

.subscription-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color-base);
}

.btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: var(--radius);
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-primary {
  background: var(--theme-color);
  color: white;
}

.btn-primary:hover {
  background: color-mix(in srgb, var(--theme-color) 90%, black);
}

.btn-outline {
  background: white;
  border-color: var(--border-color-base);
  color: var(--color-text-primary);
}

.btn-outline:hover {
  border-color: var(--theme-color);
  color: var(--theme-color);
}
</style>