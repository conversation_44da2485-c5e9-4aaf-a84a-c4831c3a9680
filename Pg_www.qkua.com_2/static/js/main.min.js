axios.defaults.timeout = 300000;
Vue.prototype.$http = axios //需要鉴权
Vue.prototype.$https = axios //不需要鉴权
var qk_rest_url = qk_global.rest_url+'qk/v1/'

//懒加载
var lazyLoadInstance = new LazyLoad({
    elements_selector: ".lazyload",
    threshold: 0,
});

// 使用document.body.clientWidth获取页面宽度
var qkClientWidth = document.body.clientWidth;

const qktoken = qkGetCookie('qk_token')

if(qktoken){
    Vue.prototype.$http.defaults.headers.common['Authorization'] = 'Bearer ' + qktoken
}else{
    let ref = qkGetQueryVariable('ref')
    if(ref){
        qkSetCookie('ref',ref)
    }
}

/*******************消息组件****************************/
const MessageComponent = {
    template: `
    <transition name="qk-message-fade" @after-leave="handleAfterLeave">
        <div role="alert" class="qk-message" v-show="visible" :style="positionStyle">
            <div class="qk-message-wrapper">
                <div :class="type ? \`qk-message-\${ type }\` : ''" @mouseenter="clearTimer" @mouseleave="startTimer">
                    <p v-html="messageWithEmoji" class="qk-message-content"></p>
                </div>
            </div>
        </div>
    </transition>
  `,
    data() {
        return {
            visible: false,
            message: '',
            duration: 3000,
            type: 'info',
            iconClass: '',
            onClose: null,
            closed: false,
            verticalOffset: 20,
            timer: null,
            emoji: {
              success: ["(ﾉ◕ヮ◕)ﾉ", "(ﾉ≧∀≦)ﾉ", "＼(＾O＾)／", "ヽ(＾∇＾)", "٩(๑❛ᴗ❛๑)۶","=‿=✧", "●ω●", "(=・ω・=)", "(●'◡'●)ﾉ♥", "<(▰˘◡˘▰)>", "(⁄ ⁄•⁄ω⁄•⁄ ⁄)", "(ง,,• ᴗ •,,)ง ✧", ">ㅂ<ﾉ ☆"],
              warning: ["(｡•́︿•̀｡)", "(｡•́︵•̀｡)", "(ง'̀-'́)ง", "ヽ(｀Д´)ﾉ", "Σ(ﾟдﾟ;)", "╮(╯_╰)╭"],
              info: ["(ﾟωﾟ)", "(＾◡＾)", "(✿◠‿◠)", "ヾ(＾∇＾)", "(*^▽^*)", "＼(＾O＾)／"],
              error: ["(╥﹏╥)", "o(TヘTo)", "｡･ﾟﾟ･(>д<)･ﾟﾟ･｡", "(ノ_<、)", "(；´ﾟωﾟ｀)"]
            }
        };
    },
    mounted() {
        this.startTimer();
    },
    computed: {
        typeClass() {
            return this.type && !this.iconClass ? `el-message__icon el-icon-${ this.type }` : '';
        },
        positionStyle() {
            return {
                'top': `${ this.verticalOffset }px`
            };
        },
        messageWithEmoji() {
            // if (this.emoji[this.type]) {
            //     const index = Math.floor(Math.random() * this.emoji[this.type].length);
            //     return `${this.message} ~ ${this.emoji[this.type][index]}`;
            // }
            return this.message;
        }
    },
    methods: {
        handleAfterLeave() {
            this.$destroy(true);
            this.$el.parentNode.removeChild(this.$el);
        },
        close() {
            this.closed = true;
            if (typeof this.onClose === 'function') {
                this.onClose(this);
            }
        },
        clearTimer() {
            clearTimeout(this.timer);
        },
        startTimer() {
            if (this.duration > 0) {
                this.timer = setTimeout(() => {
                    if (!this.closed) {
                        this.close();
                    }
                }, this.duration);
            }
        },
    },
    watch: {
        closed(newVal) {
            if (newVal) {
                this.visible = false;
            }
        }
    },
};

let MessageConstructor = Vue.extend(MessageComponent);

let instances = [];
let seed = 1;
function createMessage(options) {
    let instance;
    
    const Message = (options) => {
        options = options || {};
        
        if (typeof options === 'string') {
            options = {
                message: options
            };
        }
    
        let id = 'message_' + seed++;
        options.onClose = function() {
            Message.close(id);
        };
        
        instance = new MessageConstructor({
            data: options
        });
        
        instance.id = id;
        
        instance.$mount();
        document.body.appendChild(instance.$el);
        let verticalOffset = options.offset || 20;
        instances.forEach(item => {
            verticalOffset += item.$el.offsetHeight + 16;
        });
        instance.verticalOffset = verticalOffset;
        instance.visible = true;
        // instance.$el.style.zIndex = 99999;
        instances.push(instance);
        return instance;
    };
    
    Message.close = (id) => {
        let len = instances.length;
        let index = -1;
        let removedHeight;
     
        for (let i = 0; i < len; i++) {
            if (id === instances[i].id) {
                removedHeight = instances[i].$el.offsetHeight;
                index = i;
                instances.splice(i, 1);
                break;
            }
        }
        if (len <= 1 || index === -1 || index > instances.length - 1) return;
        for (let i = index; i < len - 1; i++) {
            let dom = instances[i].$el;
            dom.style['top'] = parseInt(dom.style['top'], 10) - removedHeight - 16 + 'px';
        }
    };
    
    Message.closeAll = () => {
        for (let i = instances.length - 1; i >= 0; i--) {
            instances[i].close();
        }
    };
    
    Message(options);
    
    return Message;
}

Vue.prototype.$message = createMessage;

/*******************抽屉组件****************************/
const DrawerComponent = {
    template: `
    <transition name="qk-drawer-fade" @after-leave="handleAfterLeave">
        <div class="qk-drawer-wrapper" v-show="visible">
            <div class="qk-drawer-container" @click.self="handleWrapperClick" :class="visible && 'qk-drawer-open'">
                <div class="qk-drawer" :class="[direction, customClass]" :style="style + (isHorizontal ? 'max-width: '+ drawerSize : 'max-height:' + drawerSize)" ref="drawer">
                    <div class="touch-close" @touchstart.stop="handlerTouchstart" @touchmove.stop="handlerTouchmove($event)" @touchend.stop="handlerTouchend" v-if="direction == 'bottom'"></div>
                    <div class="close" @click="close"><i class="ri-close-fill"></i></div>
                    <div class="qk-drawer-body">
                        <component :is="componentName" v-bind="componentProps" @close-drawer="handleCloseDrawer" ></component>
                    </div>
                </div>
            </div>
        </div>
    </transition>
  `,
    data() {
        return {
            visible: false,
            onClose: null,
            closed: false,
            size:784,
            direction:'right',
            customClass:'',
            wrapperClosable:true,
            componentName: '', // 设置要加载的组件名称
            componentProps: {}, // 设置要传递的props，默认为空对象
            style:'',
            startY:0,
            endY:0
        };
    },
    mounted() {

    },
    computed: {
        isHorizontal() {
            return this.direction === 'right' || this.direction === 'left';
        },
        drawerSize() {
            return typeof this.size === 'number' ? `${this.size}px` : this.size;
        }
    },
    methods: {
        handleWrapperClick() {
            if (this.wrapperClosable) {
                this.close();
            }
        },
        handleAfterLeave() {
            this.$destroy(true);
            this.$el.parentNode.removeChild(this.$el);
        },
        handleCloseDrawer() {
            this.close();
        },
        close() {
            this.closed = true;
            if (typeof this.onClose === 'function') {
                this.onClose(this);
            }
        },
        handlerTouchstart(event) {
            this.startY = parseInt(event.changedTouches[0].pageY);
        },
        handlerTouchmove(event) {
            this.endY = parseInt(event.changedTouches[0].pageY);
          
            var y = this.endY - this.startY;
          
            if(y > 0){
                this.style = "transform: translate3d(0px,"+ y +"px,0px); transition: -webkit-transform 0s linear 0s;--offset:"+ y +"px;";
            }
            
            event.preventDefault();//阻止屏幕滚动默认行为
        },
        handlerTouchend() {
            this.endY = parseInt(event.changedTouches[0].pageY);
            
            if(this.endY - this.startY > 50){
                this.close();
            }else{
                this.style = '';
            }
        },
    },
    watch: {
        closed(newVal) {
            if (newVal) {
                this.visible = false;
            }
        }
    },
};

let DrawerConstructor = Vue.extend(DrawerComponent);

let drawerInstances = [];
let drawerSeed = 1;
function createDrawer(options) {
    let instance;
    
    const drawer = (options) => {
        options = options || {};
        
        let id = 'drawer_' + drawerSeed++;
        options.onClose = function() {
            drawer.close(id);
        };
        
        instance = new DrawerConstructor({
            data: { 
                ...options,
                componentName: options.componentName, //&& Vue.extend(qkComponent[options.componentName]), // 设置要加载的组件名称
                componentProps: options.componentProps || {} // 设置要传递的props，默认为空对象
            }
        });
        
        instance.id = id;
        
        instance.$mount();
        document.body.appendChild(instance.$el);
        let zindex = options.zindex || 1112;
        drawerInstances.forEach(item => {
            zindex += 1;
        });
        instance.zindex = zindex;
        instance.visible = true;
        instance.$el.style.zIndex = zindex;
        drawerInstances.push(instance);
        return instance;
    };
    
    drawer.close = (id) => {
        let len = drawerInstances.length;
     
        for (let i = 0; i < len; i++) {
            if (id === drawerInstances[i].id) {
                drawerInstances.splice(i, 1);
                break;
            }
        }
    };
    
    drawer.closeAll = () => {
        for (let i = drawerInstances.length - 1; i >= 0; i--) {
            drawerInstances[i].close();
        }
    };
    
    drawer(options)
    
    return drawer;
}

/*******************弹窗组件****************************/
let ModalConstructor = Vue.extend({
    template: `
    <transition name="qk-dialog" @after-leave="handleAfterLeave">
        <div class="qk-modal" v-show="visible && !loading">
            <div class="qk-modal-dialog" @click.self="handleModalClick">
                <div class="qk-modal-content" ref="modal" v-show="!loading" :style="'max-width: '+ modalSize">
                    <div class="close" @click="closeModal"><i class="ri-close-fill"></i></div>
                    <div v-if="loading" class="vs-dialog__loading"><div class="vs-dialog__loading__load"></div></div>
                    <component :is="componentName" v-bind="props" @close-modal="handleCloseModal" @loadinged="loading = false" ref="component"></component>
                </div>
            </div>
        </div>
    </transition>
  `,
    data() {
        return {
            visible: false,
            onClose: null,
            closed: false,
            size:363,
            wrapperClosable:true,
            componentName: '', // 设置要加载的组件名称
            props: {}, // 设置要传递的props，默认为空对象
            keepAlive: true, // 控制是否保持组件不被销毁
            loading:true,
            events: {
                open: null, // 打开模态框时的回调
                close: null // 关闭模态框时的回调
            },
        };
    },
    mounted() {
    },
    computed: {
        modalSize() {
            return typeof this.size === 'number' ? `${this.size}px` : this.size;
        }
    },
    methods: {
        handleModalClick() {
            if (this.wrapperClosable) {
                this.closeModal();
            }
        },
        handleAfterLeave() {
            if (!this.keepAlive) {
                this.$destroy(true);
                this.$el.parentNode.removeChild(this.$el);
            }
        },
        handleCloseModal(shouldClose) {
            this.closeModal();
            
            //直接关闭
            if(shouldClose === true){
                this.handleAfterLeave()
            }
        },
        openModal(){
            //打开模态框时的回调
            if (typeof this.events.open === 'function') {
                this.$nextTick(()=>{
                    this.events.open(this);
                })
            }
        },
        closeModal() {
            this.closed = true;
            if (typeof this.onClose === 'function' && !this.keepAlive) {
                this.onClose(this);
            }
            
            //关闭模态框时的回调
            if (typeof this.events.close === 'function') {
                this.events.close(this);
            }

            if (this.$refs.component && typeof this.$refs.component.destroy === 'function') {
                this.$refs.component.destroy(); // 在关闭Modal时调用子组件的destroy方法
            }
        }
    },
    watch: {
        closed(newVal) {
            if (newVal) {
                this.visible = false;
                this.closed = false;
            }
        },
        visible(newVal) {
            if (newVal) {
                this.openModal()
            }
        },
        // loading(newVal) {
        //     if (!newVal) {
        //         let height = this.$refs.modal.clientHeight
        //         this.$refs.modal.style.height = '0px';
        //         this.$nextTick(()=>{
        //             setTimeout(() =>{
        //                 this.$refs.modal.style.height = height + 'px';
        //             }, 10);
                    
        //             setTimeout(() =>{
        //                 this.$refs.modal.removeAttribute('style'); // 删除style属性
        //             }, 2500);
        //         })
        //     }
        // }
    },
});

let modalInstances = [];

/**
 * 创建模态框函数
 * @param {string} componentName - 要加载的组件名称
 * @param {object} options - 可选参数对象
 * @param {function} options.open - 打开模态框时的回调函数
 * @param {function} options.close - 关闭模态框时的回调函数
 * @param {object} options.props - 要传递给组件的props对象
 * @param {boolean} options.keepAlive - 控制是否保持组件不被销毁
 * @param {boolean} options.wrapperClosable - 控制是否点击模态框外部区域关闭模态框
 * @param {number} options.size - 模态框的大小
 * @param {number} options.zindex - 模态框的层级
 * @returns {function} - 返回一个函数，用于打开模态框
 */
function createModal(componentName,options) {
    let instance;
    
    const modal = (options) => {
        options = options || {};
        
        let id = componentName
        options.onClose = function() {
            modal.close(id);
        };
        
        // 判断是否已存在同名组件实例
        let existingInstance = modalInstances.find(item => item.id === id);
        if (existingInstance) {
            existingInstance.visible = true;
            
            // 变更existingInstance.$data里属性的值
            for (let key in options) {
                if (existingInstance.$data.hasOwnProperty(key)) {
                    existingInstance.$data[key] = options[key];
                }
                
                if (existingInstance.$data.events.hasOwnProperty(key)) {
                    existingInstance.$data.events[key] = options[key];
                }
            }

            return existingInstance;
        }
        
        instance = new ModalConstructor({
            data: { 
                ...options,
                componentName: componentName, // 设置要加载的组件名称
                props: options.props || {}, // 设置要传递的props，默认为空对象
                events: {
                    open: typeof options.open === 'function' ? options.open : null, // 设置打开回调，如果不存在则为 null
                    close: typeof options.close === 'function' ? options.close : null // 设置关闭回调，如果不存在则为 null
                }
            }
        });
        
        instance.id = id;
        
        instance.$mount();
        document.body.appendChild(instance.$el);
        let zindex = options.zindex || 2000;
        modalInstances.forEach(item => {
            zindex += 1;
        });
        instance.zindex = zindex;
        instance.visible = true;
        instance.$el.style.zIndex = zindex;
        modalInstances.push(instance);
        return instance;
    };
    
    modal.close = (id) => {
        let len = modalInstances.length;
     
        for (let i = 0; i < len; i++) {
            if (id === modalInstances[i].id) {
                modalInstances.splice(i, 1);
                break;
            }
        }
    };
    
    modal.closeAll = () => {
        for (let i = modalInstances.length - 1; i >= 0; i--) {
            modalInstances[i].close();
        }
    };
    
    modal(options)
    
    return modal;
}

Vue.prototype.$createModal = createModal; 

//文本复制
Vue.prototype.$copyText = function(text) {
    const textarea = document.createElement('textarea');
    textarea.style.position = 'fixed';
    textarea.style.top = '-9999px';
    textarea.style.left = '-9999px';
    textarea.style.zIndex = "-1000";
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    try {
        document.execCommand('copy');
        return this.$message({ message: '已复制到剪贴板', type: 'success' }); 
    } catch (error) {
        return this.$message({ message: '复制失败，请手动复制', type: 'success' }); 
    }
    
    document.body.removeChild(textarea);
}

/*********************文章弹窗容器*****************************/
function maskPaper(t, e, n) {
    const maskBackdrops = {
        new: "--mask-backdrop",
        default: "--mask-paper"
    };
    const maskBackdrop = maskBackdrops[n.noteMask || 'default'];

    const isMobile = window.innerWidth <= n.value;

    const maskStyle = {
        background: `var(${maskBackdrop})`,
        transition: "background-color .4s"
    };

    const noteContainerStyle = {
        transition: isMobile ? "opacity .2s" : "transform .4s, width .4s",
        transform: isMobile ? "translate(0px, 0px) scale(1)" : "",
        width: "",
        opacity: isMobile ? (e ? "0" : "1") : "",
        overflow: isMobile ? "scroll" : "hidden"
    };

    function setInitialStyle() {
        if (!isMobile) {
            maskStyle.background = 'transparent';
            if (!e) {
                const left = t.position?.left || 0;
                const top = t.position?.top || 0;
                noteContainerStyle.width = `${n.value}px`;
                noteContainerStyle.transform = `translate(${left}px, ${top}px) scale(${_.columnWidth / n.value})`;
            }
        }
    }

    function setActiveStyle() {
        maskStyle.background = `var(${maskBackdrop})`;
        if (isMobile) {
            noteContainerStyle.transform = `translate(0px, ${I.value ? w.L : 0}px) scale(1)`;
            noteContainerStyle.overflow = "scroll";
        } else if (e) {
            noteContainerStyle.opacity = "1";
        } else {
            const left = Math.max((window.innerWidth - n.value - _.interactionWidth) / 2, 0);
            const top = U.value.horizontal + (I.value ? w.L : 0);
            noteContainerStyle.transform = `translate(${left}px, ${top}px) scale(1)`;
            noteContainerStyle.overflow = "visible";
            noteContainerStyle.width = `${n.value + _.interactionWidth}px`;
        }
    }

    function setVideoNoteActiveStyle() {
        maskStyle.background = `var(${maskBackdrop})`;
        if (isMobile) {
            noteContainerStyle.transform = `translate(0px, ${I.value ? w.L : 0}px) scale(1)`;
            noteContainerStyle.overflow = "hidden";
            noteContainerStyle.width = "100%";
        } else {
            noteContainerStyle.opacity = "1";
            const horizontalGap = U.value.horizontal;
            const totalGap = 2 * horizontalGap + 40;
            noteContainerStyle.transform = `translate(${totalGap}px, ${horizontalGap}px) scale(1)`;
        }
    }

    function onExit() {
        k.value = true;
        if (isMobile) {
            t();
        } else {
            if (e) {
                noteContainerStyle.opacity = "0";
            } else {
                const { width = 0, height = 1 } = t[0] || {};
                noteContainerStyle.height = `${y.Dd(width, height) * n.value}px`;
            }
            setTimeout(t, 400);
        }
    }

    function round(t) {
        const rounded = Math.round(t);
        return rounded % 2 === 0 ? rounded : rounded - 1;
    }

    return {
        maskStyle,
        noteContainerStyle,
        setInitialStyle,
        setActiveStyle,
        setVideoActiveStyle,
        onExit,
        inExiting: k.value
    };
} 
const noteComponent = {
    template: `
    <transition name="qk-message-fade" @after-leave="handleAfterLeave">
        <div class="note-detail-mask" v-show="visible" :style="positionStyle">
            <div class="qk-note-wrapper note-container">
                啊实打实大苏打大大大大大大大大大大大大
            </div>
        </div>
    </transition>
  `,
    data() {
        return {
            visible: false,
            onClose: null,
            closed: false,
            maskBackdrops: {
                new: "--mask-backdrop",
                default: "--mask-paper"
            },
            maskBackdrop: '',
            isMobile: false,
            maskStyle: {},
            noteContainerStyle: {}
        };
    },
    mounted() {
        window.addEventListener("resize", handleResize);
    },
    computed: {
        noteContainerStyle() {
            return {
                transition: isMobile ? "opacity .2s" : "transform .4s, width .4s",
                transform: isMobile ? "translate(0px, 0px) scale(1)" : "",
                width: "",
                opacity: isMobile ? (isActive ? "0" : "1") : "",
                overflow: isMobile ? "scroll" : "hidden"
            }
        }
    },
    methods: {
        handleResize() {

        },
        //动画结束后销毁
        handleAfterLeave() {
            this.$destroy(true);
            this.$el.parentNode.removeChild(this.$el);
        },
        //初始化
        setInitialStyle() {
            if (!isMobile) {
                maskStyle.background = "transparent";

                noteContainerStyle.width = `${n.value}px`;
                noteContainerStyle.transform = `translate(${this.position.left}px, ${this.position.top}px) scale(${_.columnWidth / n.value})`;
            }
        },
    
        setActiveStyle() {
            maskStyle.background = `var(${maskBackdrop})`;
            if (isMobile) {
                noteContainerStyle.transform = `translate(0px, ${I.value ? w.L : 0}px) scale(1)`;
                noteContainerStyle.overflow = "scroll";
            } else if (isActive) {
                noteContainerStyle.opacity = "1";
            } else {
                const left = Math.max((window.innerWidth - n.value - _.interactionWidth) / 2, 0);
                const top = U.value.horizontal + (I.value ? w.L : 0);
                noteContainerStyle.transform = `translate(${left}px, ${top}px) scale(1)`;
                noteContainerStyle.overflow = "visible";
                noteContainerStyle.width = `${n.value + _.interactionWidth}px`;
            }
        },
    
        setVideoNoteActiveStyle() {
            maskStyle.background = `var(${maskBackdrop})`;
            if (isMobile) {
                noteContainerStyle.transform = `translate(0px, ${I.value ? w.L : 0}px) scale(1)`;
                noteContainerStyle.overflow = "hidden";
                noteContainerStyle.width = "100%";
            } else {
                noteContainerStyle.opacity = "1";
                const horizontalGap = U.value.horizontal;
                const totalGap = 2 * horizontalGap + 40;
                noteContainerStyle.transform = `translate(${totalGap}px, ${horizontalGap}px) scale(1)`;
            }
        },
        close() {
            this.closed = true;
            if (typeof this.onClose === 'function') {
                this.onClose(this);
            }
            
            window.removeEventListener("resize", handleResize);
        },
        clearTimer() {
            clearTimeout(this.timer);
        },
        startTimer() {
            if (this.duration > 0) {
                this.timer = setTimeout(() => {
                    if (!this.closed) {
                        this.close();
                    }
                }, this.duration);
            }
        },
    },
    watch: {
        closed(newVal) {
            if (newVal) {
                this.visible = false;
            }
        }
    },
};

function maskPaper(options) {
    let instance;
    
    const Note = (options) => {
        options = options || {};
        
        options.onClose = function() {
            Note.close(id);
        };
        
        instance = new noteConstructor({
            data: options
        });

        instance.$mount();
        
        document.body.appendChild(instance.$el);
        
        instance.visible = true;
        // instance.$el.style.zIndex = 99999;
        return instance;
    };
    
    Note.close = () => {
        instance.close()
    };
    
    return Note(options);
}

/**
 * 将组件定位到指定元素旁边，并在显示时自动计算边界
 * @param {string|Element} target - 指定元素的选择器或DOM元素
 * @param {Element} component - 要定位的组件的DOM元素
 */
Vue.prototype.$positionComponent = function(target, component) {
    const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
    const targetRect = targetElement.getBoundingClientRect();
    
    // 计算组件应该显示的位置
    const left = targetRect.left + window.pageXOffset;
    const top = targetRect.top + targetRect.height + window.pageYOffset;
    
    // 获取浏览器窗口的宽度和高度
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    
    // 计算组件的边界
    const componentWidth = component.offsetWidth;
    const componentHeight = component.offsetHeight;
    const maxLeft = windowWidth - componentWidth - 12; // 留有12px的间隙
    const maxTop = windowHeight - componentHeight - 12; // 留有12px的间隙
    
    // 根据浏览器边界调整组件的位置
    const adjustedLeft = Math.min(Math.max(left, 12), maxLeft);
    const adjustedTop = Math.min(Math.max(top, 12), maxTop);
    
    // 设置组件的位置
    component.style.left = adjustedLeft + 'px';
    component.style.top = adjustedTop + 'px';
};
/**
 * 在指定元素旁边显示一个消息提示框
 * @param {Object} options - 选项对象
 * @param {string} options.selector - 元素选择器
 * @param {string} options.content - 提示框内容
 * @param {string} options.type - 提示框类型，可选值为 success、caution、error 和 info，默认为 info
 * @param {boolean} options.isFixed - 是否为固定定位，默认为 false
 * @param {number} options.duration - 提示框显示时间，单位为毫秒，默认为 4000
 */
// function toast(options) {
//     const {
//         selector,
//         message,
//         type = 'info',
//         isFixed = false,
//         duration = 4000
//     } = options;

//     // 获取指定元素
//     const element = document.querySelector(selector);
//     if (!element) {
//         throw new Error('Invalid element selector.');
//     }

//     // 校验提示框类型是否合法
//     const validTypes = ['success', 'caution', 'error', 'info'];
//     if (!validTypes.includes(type)) {
//         throw new Error('Invalid toast type. Valid types are: success, caution, error, info.');
//     }

//     // 创建提示框元素
//     const fragment = document.createDocumentFragment();
//     const newToast = document.createElement('div');
//     newToast.innerHTML = `<span class="toast-text">${message}</span>`;
//     newToast.className = `link-toast ${type} ${isFixed ? 'fixed' : ''}`;

//     // 计算提示框位置
//     const boundingClientRect = element.getBoundingClientRect();
//     const elementLeft = boundingClientRect.left;
//     const elementTop = boundingClientRect.top;
//     const elementWidth = boundingClientRect.width;
//     const elementHeight = boundingClientRect.height;
//     const scrollLeft = document.documentElement ?.scrollLeft || document.body.scrollLeft;
//     const scrollTop = document.documentElement ?.scrollTop || document.body.scrollTop;

//     newToast.style.left = `${elementLeft + elementWidth + scrollLeft}px`;
//     newToast.style.top = `${elementTop + elementHeight + scrollTop}px`;

//     // 自动隐藏提示框
//     setTimeout(() => {
//         newToast.className += ' out';
//         setTimeout(() => {
//             newToast.parentNode.removeChild(newToast);
//         }, 350);
//     }, duration);

//     // 添加提示框到页面中
//     fragment.appendChild(newToast);
//     document.body.appendChild(fragment);

//     // 检查是否需要调整提示框位置
//     const windowWidth = document.body.offsetWidth;
//     const toastLeft = newToast.getBoundingClientRect()
//         .left;
//     const toastWidth = newToast.offsetWidth;

//     if (windowWidth - toastWidth - toastLeft < 0) {
//         newToast.style.left = `${windowWidth - toastWidth - 10 + scrollLeft}px`;
//     }
// }

// Vue.prototype.$toast = toast;
/*******************消息组件结束****************************/
//支付类型组件
Vue.component('qk-pay-type',{
    props: {
        type: {
            type:Boolean,
            default:'money'
        },
        order_type: {
            type:String,
            default:''
        },
        pay_type: {
            type:String,
            default:''
        }
    },
    template: `<div class="pay-type" v-if="type == 'money'">
            <div class="title">请选择付款方式</div>
            <ul class="qk-flex">
                <li v-if="allow.alipay" :class="{'active':pay_type == 'alipay'}" @click="chosePayType('alipay')"><i class="ri-alipay-fill"></i> 支付宝</li>
                <li v-if="allow.wecatpay" :class="{'active':pay_type == 'wecatpay'}" @click="chosePayType('wecatpay')"><i class="ri-wechat-pay-fill"></i> 微信</li>
                <li v-if="allow.balancepay && qktoken" :class="{'active':pay_type == 'balance'}" @click="chosePayType('balance')"><i class="ri-wallet-3-fill"></i> 余额</li>
                <li v-if="allow.cardpay  && qktoken" :class="{'active':pay_type == 'card'}" @click="chosePayType('card')"><i class="ri-wallet-3-fill"></i>卡密</li>
            </ul>
            <div class="balance qk-flex" v-if="pay_type == 'balance'">
                <div class="left">我的余额</div>
                <div class="rigth"><span>￥</span>{{allow.money}}</div>
            </div>
        </div>`,
    data() {
        return {
            allow:[],
        }
    },
    mounted() {
        this.allowPayType()
    },
    methods: {
        allowPayType() {
            this.$http.post(qk_rest_url + 'allowPayType', 'order_type=' + this.order_type).then(res=>{
                this.allow = res.data
                this.$emit('data',res.data);
            })
        },
        chosePayType(type){
            this.pay_type = type
            this.$emit('chosePayType',type);
        },
    },
    watch: {
        order_type(){
            this.allowPayType()
        }
    },
})

//登录与注册
Vue.component('login',{
    props: ['show'],
    template:qk_global.login,
    data() {
        return {
            data:{
                nickname:'',
                username:'',
                password:'',
                code:'', //验证码
                invite_code:'', //邀请码
                confirmPassword:'',
                loginType:'',
                captcha:'' // 图形验证
            },
            loginType:1,
            locked:false,
            oauths:[],
            login:'',
            invitePass:false,
            qrcode:'',
            countdown:60, //倒计时
        }
    },
    mounted() {
        this.$https.get(qk_rest_url+'getLoginSettings').then(res=>{
            this.oauths = res.data.oauths
            this.login = res.data
            this.$nextTick(()=>{
                this.$emit('loadinged')
            })
        })
    },
    computed: {
        loginTitle() {
            if(this.loginType == 1) {
                return '欢迎回来';
            }else if(this.loginType == 2) {
                
                if(this.login.invite_type != 0 && !this.invitePass) {
                    return '填写邀请码';
                }
                
                return '注册新账号';
            }else if(this.loginType == 3) {
                return '找回密码';
            }else if(this.loginType == 4) {
                return '扫码登录';
            }
            
        },
        buttonText() {
            if(this.loginType == 1) {
                return '登录';
            }else if(this.loginType == 2) {
                return '注册';
            }else if(this.loginType == 3) {
                return '下一步';
            }
        }
    },
    methods: {
        loginSubmit(e){
            e.preventDefault()
            
            if(this.login.allow_slider_captcha) {
                return this.$createModal('sliderCaptcha',{
                    size:356,
                    keepAlive:false,
                    props:{
                        callback:(captcha)=>{
                            this.data.captcha = captcha
                            this._submit()
                        }
                    
                }})
            }
            
            this._submit()
        },
        _submit() {
            if(this.locked == true) return
            this.locked = true
            
            if(this.loginType == 2 && this.login.invite_type != 0 && !this.invitePass) {
                this.locked = false;
                this.checkInviteCode();
            }
            
            //登录
            else if(this.loginType == 1){
                
                this.$https.post(qk_global.rest_url+'jwt-auth/v1/token',this.data).then(res=>{
                    if(res.status == 200){

                        Vue.prototype.$http.defaults.headers.common['Authorization'] = 'Bearer ' + qktoken
                    }

                    location.reload()
                    return

                }).catch(err=>{
                    this.$message({ message: err.response.data.message, type: 'warning' });
                    this.locked = false
                })
                
            //注册
            }else if(this.loginType == 2){ //Qs.stringify()
                this.$https.post(qk_rest_url+'regeister',this.data).then(res=>{
                    location.reload();
                    return

                }).catch(err=>{
                    this.$message({ message: err.response.data.message, type: 'warning' });
                    this.locked = false
                })
            }
        },
        //获取验证码
        getCode(){
            this.$https.post(qk_rest_url+'sendCode',this.data).then(res=>{
                this.$message({ message: res.data.msg, type: 'success' });
                this.startCountdown()
            }).catch(err=>{
                this.$message({ message: err.response.data.message, type: 'warning' });
            })
        },
        checkInviteCode() {
            if(!this.data.invite_code.trim()) return this.$message({ message: '请输入邀请码', type: 'error' }); 
            
            if(!(/^[0-9a-zA-Z]{4}(-[0-9a-zA-Z]{4}){4}$/.test(this.data.invite_code.trim()))) return this.$message({ message: '请输入正确格式：xxxx-xxxx-xxxx-xxxx-xxxx', type: 'error' });
            
            this.$https.post(qk_rest_url+'checkInviteCode',{invite_code:this.data.invite_code}).then(res=>{
                this.invitePass = true;
                this.locked = false;
            }).catch(err=>{
                this.locked = false;
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        socialLogin(type) {
            
            //设置来路地址
            qkSetCookie('qk_referer_url', window.location.href);
            
            if(this.oauths[type].url) {
                window.location.href = this.oauths[type].url;
                return
            }
            
            this.$message({ message: '拉取数据中，请稍后...', type: 'success' });
            
            this.$https.post(qk_rest_url+'socialLogin',{type:type}).then(res=>{
                
                if(res.data.qrcode) {
                    this.loginType == 4
                    this.qrcode = res.data.qrcode
                }else{
                    if(res.data.url){
                        window.location.href = res.data.url;
                    }
                }
            }).catch(err=>{
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        startCountdown() {
            let intervalId = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    clearInterval(intervalId);
                    this.countdown = 60;
                }
            }, 1000)
        },
        destroy(){
            this.loginType = 1;
            this.locked = false;
            this.invitePass = false;
            this.data = {
                nickname:'',
                username:'',
                password:'',
                code:'', //验证码
                invite_code:'', //邀请码
                confirmPassword:'',
                loginType:''
            }
        }
    },
    watch: {
        loginType(val) {
            // if(val == true){
                this.$nextTick(()=>{
                    // addFadeIn('.form-item.code')
                    // addFadeIn('.form-item.nickname')
                })
            // }
        },
        // show(newVal, oldVal) {
        //     this.loginType = 1;
        //     this.locked = false;
        //     this.invitePass = false;
        //     this.data = {
        //         nickname:'',
        //         username:'',
        //         password:'',
        //         code:'', //验证码
        //         invite_code:'', //邀请码
        //         confirmPassword:'',
        //         loginType:''
        //     }
        // },
        invitePass(newVal, oldVal) {
            if(this.invitePass && this.login.invite_type == 2) {
                this.data.invite_code = '';
            }
        },
    }
    
})

// 滑块验证
Vue.component('sliderCaptcha', {
    props: ['callback'],
    template:`<div class="captcha-container">
                <slot></slot>
                <div class="slider-captcha">
                    <canvas ref="canvas" class="canvas" :height="options.height" :width="options.width"></canvas>
                    <canvas ref="block" class="block" :height="options.height"></canvas>
                    <div class="refresh-icon" @click="reset">
                        <i class="ri-refresh-line"></i>
                    </div>
                    <div ref="sliderContainer" class="slider-container">
                        <div class="slider-mask" ref="sliderMask"></div>
                        <div ref="slider" class="slider" @mousedown="handleDragStart" @touchstart="handleDragStart">
                            <i class="ri-arrow-right-s-line"></i>
                        </div>
                        <div class="slider-text">{{ sliderText }}</div>
                    </div>
                </div>
            </div>`,
    data() {
        return {
            canvas: null,
            block: null,
            img: null,
            x: 0,
            y: 0,
            sliderText: '向右滑动填充拼图',
            isMouseDown: false,
            originX: 0,
            originY: 0,
            sliderContainer: null,
            slider: null,
            sliderMask: null,
            trail: [],
            options: {
                width: 308,
                height: 170,
                PI: Math.PI,
                sliderL: 42,
                sliderR: 9,
                offset: 5,
            },
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.canvas = this.$refs['canvas']
            this.block = this.$refs['block']
            this.slider = this.$refs['slider']
            this.sliderContainer = this.$refs['sliderContainer']
            this.sliderMask = this.$refs['sliderMask']
            this.init();
            this.$emit('loadinged')
        });
    },
    methods: {
        init() {
            const L = this.options.sliderL + this.options.sliderR * 2 + 3;
            this.img = new Image();
            this.img.crossOrigin = "Anonymous";
            this.img.onload = () => {
                this.x = this.getRandomNumberByRange(L + 10, this.options.width - (L + 10));
                this.y = this.getRandomNumberByRange(10 + this.options.sliderR * 2, this.options.height - (L + 10));
                this.drawImg(this.canvas.getContext('2d', { willReadFrequently: true }), 'fill');
                this.drawImg(this.block.getContext('2d', { willReadFrequently: true }), 'clip');

                const ctx = this.canvas.getContext('2d', { willReadFrequently: true });
                ctx.drawImage(this.img, 0, 0, this.options.width, this.options.height);
                const blockCtx = this.block.getContext('2d', { willReadFrequently: true });
                blockCtx.drawImage(this.img, 0, 0, this.options.width - 2, this.options.height);
                const yValue = this.y - this.options.sliderR * 2 - 1;
                const imageData = blockCtx.getImageData(this.x - 3, yValue, L, L);
                this.block.width = L;
                blockCtx.putImageData(imageData, 0, yValue + 1);
                this.sliderText = '向右滑动填充拼图';
            };

            this.img.onerror = function () {
                this.sliderText = '加载失败，请刷新页面后重试';
            }.bind(this);

            this.sliderText = '正在加载中...';
            this.img.src = this.getImageSrc();

            document.addEventListener('mousemove', this.handleDragMove);
            document.addEventListener('touchmove', this.handleDragMove);
            document.addEventListener('mouseup', this.handleDragEnd);
            document.addEventListener('touchend', this.handleDragEnd);

            document.addEventListener('mousedown', function () { return false; });
            document.addEventListener('touchstart', function () { return false; });
            document.addEventListener('swipe', function () { return false; });
        },

        drawImg(ctx, operation) {
            const l = this.options.sliderL;
            const r = this.options.sliderR;
            const PI = this.options.PI;
            const xValue = this.x;
            const yValue = this.y;
            ctx.beginPath();
            ctx.moveTo(xValue, yValue);
            ctx.arc(xValue + l / 2, yValue - r + 2, r, 0.72 * PI, 2.26 * PI);
            ctx.lineTo(xValue + l, yValue);
            ctx.arc(xValue + l + r - 2, yValue + l / 2, r, 1.21 * PI, 2.78 * PI);
            ctx.lineTo(xValue + l, yValue + l);
            ctx.lineTo(xValue, yValue + l);
            ctx.arc(xValue + r - 2, yValue + l / 2, r + 0.4, 2.76 * PI, 1.24 * PI, true);
            ctx.lineTo(xValue, yValue);
            ctx.lineWidth = 2;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.stroke();
            ctx[operation]();
            ctx.globalCompositeOperation = 'destination-over';
        },

        handleDragStart(e) {
            if (this.sliderText === '验证通过' || this.sliderText.includes('加载失败') !== false) return;
            this.originX = e.clientX || e.touches[0].clientX;
            this.originY = e.clientY || e.touches[0].clientY;
            this.isMouseDown = true;
        },

        handleDragMove(e) {
            if (!this.isMouseDown) return false;
            const eventX = e.clientX || e.touches[0].clientX;
            const eventY = e.clientY || e.touches[0].clientY;
            const moveX = eventX - this.originX;
            const moveY = eventY - this.originY;
            if (moveX < 0 || moveX + 40 > this.options.width) return false;
            this.slider.style.left = (moveX - 1) + 'px';

            const blockLeft = (this.options.width - 40 - 20) / (this.options.width - 40) * moveX;
            this.block.style.left = blockLeft + 'px';
            this.sliderContainer.classList.add('slider-container-active');
            this.sliderMask.style.width = (moveX + 4) + 'px';
            this.trail.push(Math.round(eventY));
        },

        handleDragEnd(e) {
            if (!this.isMouseDown) return false;
            this.isMouseDown = false;
            const eventX = e.clientX || e.changedTouches[0].clientX;
            if (eventX === this.originX) return false;
            this.sliderContainer.classList.remove('slider-container-active');
            const data = this.verify();

            if (data.spliced && data.verified) {
                this.sliderContainer.classList.add('slider-container-success');
                this.sliderText = '验证通过';
                if (typeof this.callback === 'function') {
                    this.callback(this.trail);
                }
                
                this.$emit('close-modal');
            } else {
                this.sliderContainer.classList.add('slider-container-fail');
                this.sliderText = '再试一次';
                setTimeout(() => {
                    this.reset();
                }, 1000);
            }
        },

        verify() {
            const arr = this.trail;
            const left = parseInt(this.block.style.left);
            let verified = false;
            const sum = (x, y) => x + y;
            const square = (x) => x * x;
            const average = arr.reduce(sum) / arr.length;
            const deviations = arr.map((x) => x - average);
            const stddev = Math.sqrt(deviations.map(square).reduce(sum) / arr.length);
            verified = stddev !== 0;
            return {
                spliced: Math.abs(left - this.x) < this.options.offset,
                verified: verified
            };
        },

        clean() {
            this.canvas.getContext('2d').clearRect(0, 0, this.options.width, this.options.height);
            this.block.getContext('2d').clearRect(0, 0, this.options.width, this.options.height);
            this.block.width = this.options.width;
        },

        reset() {
            this.sliderContainer.classList.remove('slider-container-fail');
            this.sliderContainer.classList.remove('slider-container-success');
            this.slider.style.left = 0;
            this.block.style.left = 0;
            this.sliderMask.style.width = 0;
            this.clean();
            this.img.src = this.getImageSrc();
        },
        
        getImageSrc() {
          return qk_global.home_url + '/wp-content/themes/qkua/Assets/fontend/images/captcha/' +(Math.round(Math.random() * 20))+ '.jpg'  
        },

        getRandomNumberByRange(start, end) {
            return Math.round(Math.random() * (end - start) + start);
        },
        
        beforeDestroy() {
            document.removeEventListener('mousemove', this.handleDragMove);
            document.removeEventListener('touchmove', this.handleDragMove);
            document.removeEventListener('mouseup', this.handleDragEnd);
            document.removeEventListener('touchend', this.handleDragEnd);
        },
    }

})

//vip购买
Vue.component('vip',{
    props: ['show'],
    template:`<div class="pay-vip-container box qk-radius" v-if="data.length">
        <slot></slot>
        <div class="pay-vip-header">
            <div class="left qk-flex">
                <div class="user-avatar"><img :src="user_data.avatar" class="avatar-face w-h"></div>
                <div class="user-info">
                    <div class="user-name">{{user_data.name}}</div>
                    <div class="vip-info">{{user_data.vip != false ? user_data.vip.name + '：' + (user_data.vip.date === 0 ? '永久' : user_data.vip.date + ' 到期') :'会员：已过期'}}</div>
                </div>
            </div>
            <div class="rigth">
                <span @click="chosePayType('cardpay')" v-if="pay_type != 'cardpay'">激活码开通</span>
                <span @click="chosePayType()" v-else>返回套餐页面</span>
            </div>
        </div>
        <div class="pay-vip-tabs qk-flex" v-if="pay_type != 'cardpay'">
            <div :class="['tab-item',{'active':index == i}]" v-for="(item,i) in data" v-text="item.name" @click="changeTabs(i)"></div>
        </div>
        <div class="pay-vip-content">
            <div class="vip-member-describe" v-show="pay_type != 'cardpay'">
                <div class="title"><h2>{{data[index].name}}专属权益</h2></div>
                <ul>
                    <li>全站资源折扣购买</li>
                    <li v-if="data[index].free_read === '1'">隐藏内容免费查看</li>
                    <li v-if="data[index].free_download === '1'">全站资源免费下载</li>
                    <li v-if="data[index].free_video === '1'">付费视频免费观看</li>
                    <li v-if="data[index].signin_bonus.credit || data[index].signin_bonus.exp">签到额外奖励</li>
                </ul>
                <a href="/vip" class="qk-flex">
                    <span>更多会员权益</span>
                    <i class="ri-arrow-right-s-line"></i>
                </a>
            </div>
            <div class="vip-product-warp" style="overflow: hidden; flex:1;" v-show="data[index].vip_group.length && pay_type != 'cardpay'">
                <div id="bs-scroll" class="pay-vip-price-list" ref="scroll">
                    <ul>
                        <li :class="['list-item',{ 'active':time_index == i }]" v-for="(item,i) in data[index].vip_group" @click="time_index = i">
                            <div class="price-icon" v-if="item.discount < 100">{{item.discount/10}}折</div>
                            <div class="price-name" v-if="item.name">{{item.name}}</div>
                            <div class="price-name" v-else-if="item.time > 0">{{item.time}}天</div>
                            <div class="price-name" v-else>永久</div>
                            <div class="price-num">￥{{Math.ceil(item.price * (item.discount/100))}}</div>
                            <div class="price-og-num" v-if="item.discount < 100">￥{{item.price}}</div>
                        </li>
                    </ul>
                </div>
                <qk-pay-type :pay_type="pay_type" :order_type="'vip_goumai'" @chosePayType="chosePayType"></qk-pay-type>
                <div class="pay-button"><button @click="pay()"><span>立即支付 {{discountedPrice ? discountedPrice + '元': ''}}</span></button></div>
            </div>
            <div class="card-code-warp" v-if="pay_type == 'cardpay'">
                <div class="card-code-input">
                    <p>请输入激活码：</p>
                    <input type="text" placeholder="xxxx-xxxx-xxxx-xxxx-xxxx" v-model="code"/>
                </div>
                <button class="card-code-btn" @click="cardPay"><span>立即激活</span></button>
                <div class="card-code-bottom">
                    <a href="#" class="acc-btn-bottom-msg">如何获取卡密？快速点击这里查看</a>
                    <div class="acc-btn-bottom-msg">点击“立即激活”将直接为当前用户使用</div>
                    <div class="acc-btn-bottom-msg">激活码可以用于开通会员，充值余额，充值积分等服务，具体兑换服务参考相关说明或以激活后的提示为准。</div>
                </div>
            </div>
        </div>
    </div>`,
    data() {
        return {
            data:[],
            user_data:null,
            money:0,
            index:0,
            pay_type:'',
            price:0,
            time_index:0,
            time:'',
            code:'',
            locked:false
        }
    },
    mounted() {
        if(!qktoken) return this.$createModal('login');
        this.$http.post(qk_rest_url+'getVipInfo').then(res=>{
            this.data = res.data.data;
            this.money = res.data.money;
            this.user_data = res.data.user_data;
            this.time_index = 0;
            this.$emit('loadinged')
            this.$nextTick(()=>{
                if(this.$refs.scroll) {
                    this.scroll = new BScroll(this.$refs.scroll, {
                        scrollX: true,
                        //probeType: 3, // listening scroll event
                        click:true    
                    })
                }
            })
        })
        
    },
    computed: {
        discountedPrice() {
            let val = this.data[this.index].vip_group[this.time_index];
            
            return Math.ceil(val.price * (val.discount/100))
        }
    },
    methods: {
        changeTabs(index) {
            this.index = index;
            this.time_index = 0;
        },
        chosePayType(val){
            this.pay_type = val;
        },
        pay(){
            let error = '';
            !this.pay_type && (error = '请选择付款方式'),
            (this.pay_type == 'balance'  && this.money < this.discountedPrice) && (error = '余额不足');
            
            if(error) {
                return this.$message({ message: error, type: 'error' }); 
            }
            
            let time = this.data[this.index].vip_group[this.time_index].time;
            let name = this.data[this.index].vip_group[this.time_index].name;
            
            var data = {
                'title':'开通'+ (name || (time > 0 ? time + '天' : '永久')) + this.data[this.index].name,
                'order_type':'vip_goumai',
                'order_key':'vip' + this.index,
                'order_value':time,
                'order_price':this.discountedPrice,
                'pay_type':this.pay_type
            }
            
            this.$createModal('pay',{size:312,keepAlive:false,props:{
                data:data
            }})
            
            this.$emit('close-modal');
        },
        cardPay() {
            if(!this.code.trim()) return this.$message({ message: '请输入激活码', type: 'error' }); 
            
            if(!(/^[0-9a-zA-Z]{4}(-[0-9a-zA-Z]{4}){4}$/.test(this.code.trim()))) return this.$message({ message: '请输入正确格式：xxxx-xxxx-xxxx-xxxx-xxxx', type: 'error' });
            
            if(this.locked == true) return
            this.locked = true
            
            this.$http.post(qk_rest_url+'cardPay',{code:this.code}).then(res=>{
                this.locked = false
                this.$message({message: res.data.msg,type: 'success'});
                //刷新当前页面
                setTimeout(()=>{
                    var url = new URL(window.location.href);
                    url.searchParams.delete('qkpaystatus');
                    window.location.href = url.href
                }, 3000)
            }).catch(err=>{
                this.locked = false
                this.$message({ message: err.response.data.message, type: 'error' });
            })
            
        },
        destroy() {
            this.time_index = 0;
            this.pay_type = '';
        }
    },
    watch: {
        // show(newVal, oldVal) {
        //     this.time_index = 0;
        //     this.pay_type = '';
        // }
    }
})

//支付
Vue.component('pay',{
    props: ['data','show'],
    template:`<div class="qrcode-pay-container box qk-radius" v-if="qrcode">
        <slot></slot>
       <div :class="[\'qrcode-pay\',data.pay_type == \'alipay\' ? \'alipay\' : \'wecatpay\' ]">
            <div class="pay-logo qk-flex">
                <img class="wecatpay-logo" src="https://td.cdn-go.cn/enterprise_payment/v0.0.9/logo.png">
                <img class="alipay-logo" src="https://gw.alipayobjects.com/mdn/member_frontWeb/afts/img/A*oRlnSYAsgYQAAAAAAAAAAABkARQnAQ">
            </div>
            <div class="pay-title">{{data.title}}</div>
            <div class="pay-price">￥{{data.order_price}}</div>
            <div class="pay-qrcode"><img :src="\'https://seven.7b2.com/wp-content/themes/seven/inc/qrcode/index.php?c=\'+qrcode"></div>
            <div class="pay-countdown">剩余支付时间 {{countdown}}</div>
       </div>
       <div class="qrcode-pay-bottom"> 请使用{{data.pay_type == \'alipay\' ? \'支付宝\' : \'微信\'}}APP扫一扫<br>扫描二维码支付</div>
    </div>`,
    data() {
        return {
            qrcode:'',
            timer: null, //定时器
              countdown:'4分59秒', //倒计时
            success:false, //是否支付完毕
            checkTime:'',
            order_id:''
        }
    },
    mounted() {
        var url = new URL(window.location.href);
        url.searchParams.set('qkpaystatus', 'check');
        this.data['redirect_url'] = url.href;
        this.buildOrder()
    },
    methods: {
        buildOrder() {
            this.$http.post(qk_rest_url + 'buildOrder', this.removeEmptyProperties(this.data)).then(res=>{
                
                qkSetCookie('order_id', res.data.order_id);
                this.order_id = res.data.order_id;
                
                if(this.data.pay_type == "balance" && this.data.pay_type == res.data.pay_type){
                    this.$emit('close-modal',true);
                    this.balancePay(res.data.order_id)
                }else if(this.data.pay_type == "credit" && this.data.pay_type == res.data.pay_type){
                    this.$emit('close-modal',true);
                    this.creditPay(res.data.order_id)
                }else{
                    if(res.data.qrcode) {
                        this.qrcode = res.data.qrcode;
                        this.payCheck();
                        this.startTime(300)
                    }else{
                        
                        if(res.data.url){
                            window.location.href = res.data.url;
                        }
                        
                        this.$emit('close-modal',true);
                        this.$createModal('payCheck',{keepAlive:false});
                    }
                }
                
                this.$nextTick(()=>{
                    this.$emit('loadinged')
                })
            }).catch(err=>{
                this.$emit('close-modal',true);
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        balancePay(order_id) {
            this.$http.post(qk_rest_url + 'balancePay', 'order_id=' + order_id).then(res=>{
                this.$createModal('payCheck',{keepAlive:false});
            }).catch(err=>{
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        creditPay(order_id){
            this.$http.post(qk_rest_url+'creditPay','order_id='+order_id).then(res=>{
                this.$createModal('payCheck',{keepAlive:false});
            }).catch(err=>{
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        startTime(end) { //轮询
            this.timer = window.setInterval(() => {
                if (end < 1) {
                    clearInterval(this.timer)
                    this.success = 'fail'
                    return false
                }
                
                end--
                let minute = Math.floor((end / 60) % 60)
                let second = Math.floor(end % 60)
                this.countdown = `${minute> 0 ? minute + '分' : ''}${second}秒`
            }, 1000)
        },
        payCheck() {
            
            if(this.success == 'fail' || this.checkTime === null || this.success === true){
                this.checkTime = null
                return
            }
            
            let order_id = this.order_id || qkGetCookie('order_id')
            this.$http.post(qk_rest_url + 'payCheck', 'order_id=' + order_id).then(res=>{
                if (res.data.status === 'success') {
                    
                    if(!qktoken){

                        let list = qkGetCookie('qk_not_login_buy_'+res.data.post_id+'_'+res.data.type)
                        if(!list){
                            list = new Object()
                        }else{
                            list = JSON.parse(list)
                        }

                        Reflect.set(list,res.data.index,{
                            'post_id':res.data.post_id,
                            'order_id':order_id,
                            'type':res.data.type,
                            'index':res.data.index
                        })

                        qkSetCookie('qk_not_login_buy_'+res.data.post_id+'_'+res.data.type,JSON.stringify(list),3650)
                    }
                    
                    clearInterval(this.timer)
                    this.success = true;
                    this.checkTime = null;
                    
                    this.$message({ message: '支付成功，将在3秒后跳转...', type: 'success' });
                    
                    setTimeout(()=>{
                        var url = new URL(window.location.href);
                        url.searchParams.delete('qkpaystatus');
                        window.location.href = url.href
                    }, 3000)
                } else {
                    this.checkTime = setTimeout(()=>{
                        this.payCheck()
                    }, 1000)
                }
            })
        },
        removeEmptyProperties(obj) {
            for (let key in obj) {
                if (obj[key] === null || obj[key] === undefined || obj[key] === '') {
                    delete obj[key];
                }
            }
            return obj;
        },
        destroy(){
            clearInterval(this.timer)
            this.success = false
            this.checkTime = null
        }
    },
    watch: {
        // show(newVal, oldVal) {
        //     clearInterval(this.timer)
        //     this.success = false
        //     this.checkTime = null
        // }
    }
})

//充值
Vue.component('recharge', {
    props: ['type','show'],
    template:`<div class="recharge-container" v-if="data">
        <slot></slot>
        <div class="title">{{title}}</div>
        <div class="fast-amount-list" v-if="pay_type != 'card'">
            <ul class="list">
                <li :class="['item',{ 'active':index == i }]" v-for="(item,i) in data[type+'_data']" @click="index = i">
                    <div class="price-icon" v-if="item.discount < 100">{{item.discount/10}}折</div>
                    <div class="price-name">{{type == 'credit'? item.credit :item.price}}<span class="currency">{{type == 'credit'?'积分':'元'}}</span></div>
                    <div class="price-num">￥{{Math.ceil(item.price * (item.discount/100))}}<span class="price-og-num" v-if="item.discount < 100">￥{{item.price}}</span></div>
                </li>
                <li @click="index = 'custom'" :class="['item',{ 'active':index == 'custom' }]">\
                    <div class="price-name">{{addCustomPrice ? addCustomPrice :'--'}}<span class="currency">{{type == 'credit'?'积分':'元'}}</span></div>
                    <label class="custom" v-if="index == 'custom'">
                        <span>￥</span>
                        <input type="number" max="5" v-model="customPrice" oninput="value=value.replace(/[^\\d]/g,'')">\
                    </label>
                    <label class="custom-text" v-else for="dsinput">自定义金额</label>
                </li>
            </ul>
        </div>
        <div class="card-code-warp" v-else>
            <div class="card-code-input">
                <p>请输入激活码：</p>
                <input type="text" placeholder="xxxx-xxxx-xxxx-xxxx-xxxx" v-model="code"/>
            </div>
            <div class="card-code-bottom">
                <a href="#" class="acc-btn-bottom-msg">如何获取卡密？快速点击这里查看</a>
                <div class="acc-btn-bottom-msg">点击“立即激活”将直接为当前用户使用</div>
                <div class="acc-btn-bottom-msg">激活码可以用于开通会员，充值余额，充值积分等服务，具体兑换服务参考相关说明或以激活后的提示为准。</div>
            </div>
        </div>
        <qk-pay-type :pay_type="pay_type" :order_type="order_type" @chosePayType="chosePayType"></qk-pay-type>
        <div class="pay-button">
            <button @click="pay()" v-if="pay_type != 'card'"><span>立即支付 {{discountedPrice ? discountedPrice + '元': ''}}</span></button>
            <button @click="cardPay" v-else><span>立即激活</span></button>
        </div>
    </div>`,
    data() {
        return {
            data:null,
            index:0,
            pay_type:'',
            value:'',
            customPrice:'',
            code:'',
            locked:false,
        }
    },
    mounted() {
        if(!qktoken) return this.$createModal('login');
        this.$http.post(qk_rest_url+'getRechargeInfo').then(res=>{
            this.$nextTick(()=>{
                this.data = res.data
                this.$emit('loadinged')
            })
        })
    },
    computed: {
        // type() {
        //     return this.data.type;
        // },
        addCustomPrice() {
            if(this.type == 'balance') {
                return this.customPrice;
            }else{
                return parseInt(this.customPrice*this.data.ratio);
            }
        },
        discountedPrice() {
            if(this.index == 'custom'){
                return this.customPrice;
            }else{
                let val = this.data[this.type+'_data'];
                return Math.ceil(val[this.index].price * (val[this.index].discount/100))
            }
        },
        title(){
            return this.type == 'credit' ? '积分充值' :'余额充值';
        },
        order_type() {
            return this.type == 'credit' ? 'credit_chongzhi' :'money_chongzhi';
        }
    },
    methods: {
        chosePayType(val){
            this.pay_type = val;
        },
        pay(){
            let error = '';
            !this.pay_type && (error = '请选择付款方式');
           
            if(this.pay_type != 'card'){
               !this.discountedPrice && (error = '请输入金额'),
               (this.index == 'custom' && this.customPrice < this.data[this.type+'_custom_limit']) && (error = '最低充值' + this.data[this.type+'_custom_limit'] + '元'),
               (this.type == 'credit' && this.pay_type == 'balance'  && parseInt(this.data.money) < this.discountedPrice) && (error = '余额不足');
            }
            
            if(error) {
                return this.$message({ message: error, type: 'error' }); 
            }
            
            var data = {
                'title':this.type == 'credit' ? '积分充值' :'余额充值',
                'order_type':this.order_type,
                'order_price':parseInt(this.discountedPrice),
                'pay_type':this.pay_type
            }
            
            if(this.index != 'custom'){
                data['order_key'] = this.index;
                data['order_value'] = this.type == 'credit' ? this.data[this.type+'_data'][this.index].credit : this.data[this.type+'_data'][this.index].price;
            }

            this.$createModal('pay',{size:312,keepAlive:false,props:{
                data:data
            }})
            
            this.$emit('close-modal');
        },
        cardPay() {
            if(!this.code.trim()) return this.$message({ message: '请输入激活码', type: 'error' }); 
            
            if(!(/^[0-9a-zA-Z]{4}(-[0-9a-zA-Z]{4}){4}$/.test(this.code.trim()))) return this.$message({ message: '请输入正确格式：xxxx-xxxx-xxxx-xxxx-xxxx', type: 'error' });
            
            if(this.locked == true) return
            this.locked = true
            
            this.$http.post(qk_rest_url+'cardPay',{code:this.code}).then(res=>{
                this.locked = false
                this.$message({message: res.data.msg,type: 'success'});
                //刷新当前页面
                setTimeout(()=>{
                    var url = new URL(window.location.href);
                    url.searchParams.delete('qkpaystatus');
                    window.location.href = url.href
                }, 3000)
            }).catch(err=>{
                this.locked = false
                this.$message({ message: err.response.data.message, type: 'error' });
            })
            
        },
        destroy(){
            this.index = 0;
            this.pay_type = '';
            this.value = '';
            this.customPrice = '';
            this.code = '';
            this.locked = false;
        }
    },
    watch: {
        // show(newVal, oldVal) {
        //     if(newVal == false){
        //         this.index = 0;
        //         this.pay_type = '';
        //         this.value = '';
        //         this.customPrice = '';
        //         this.code = '';
        //         this.locked = false;
        //     }
        // }
    }
})

//支付检查
Vue.component('payCheck', {
    props: ['show'],
    template:`<div class="pay-check-container">
        <slot></slot>
       <div class="success" v-if="success == true">
            <div class="icon"><i class="ri-check-line"></i></div>
            <p>支付成功</p>
       </div>
       <div class="fail" v-else-if="success == \'fail\'">
            <div class="icon" style=" background: #f35; "><i class="ri-close-line"></i></div>
            <p>支付失败</p>
       </div>
       <div class="check" v-else>
            <div class="icon"><i class="ri-refresh-line"></i></div>
            <p>支付检查中....{{countdown}}</p>
       </div>
    </div>`,
    data() {
        return {
            timer: null, //定时器
              countdown:'4分59秒', //倒计时
            success:false, //是否支付完毕
            checkTime:'',
            order_id:''
        }
    },
    mounted() {
        if(this.success == false) {
            this.payCheck();
            this.startTime(300)
            this.order_id = qkGetCookie('order_id');
        }
        this.$emit('loadinged')
    },
    methods: {
        startTime(end) { //轮询
            this.timer = window.setInterval(() => {
                if (end < 1) {
                    clearInterval(this.timer)
                    this.success = 'fail'
                    return false
                }
                
                end--
                let minute = Math.floor((end / 60) % 60)
                let second = Math.floor(end % 60)
                this.countdown = `${minute> 0 ? minute + '分' : ''}${second}秒`
            }, 1000)
        },
        payCheck() {
            
            if(this.success == 'fail' || this.checkTime === null || this.success === true){
                this.checkTime = null
                return
            }
            
            let order_id = this.order_id || qkGetCookie('order_id')
            this.$http.post(qk_rest_url + 'payCheck', 'order_id=' + order_id).then(res=>{
                if (res.data.status === 'success') {
                    
                    if(!qktoken){

                        let list = qkGetCookie('qk_not_login_buy_'+res.data.post_id+'_'+res.data.type)
                        if(!list){
                            list = new Object()
                        }else{
                            list = JSON.parse(list)
                        }

                        Reflect.set(list,res.data.index,{
                            'post_id':res.data.post_id,
                            'order_id':order_id,
                            'type':res.data.type,
                            'index':res.data.index
                        })

                        qkSetCookie('qk_not_login_buy_'+res.data.post_id+'_'+res.data.type,JSON.stringify(list),3650)

                    }
                    
                    clearInterval(this.timer)
                    this.success = true;
                    this.checkTime = null;
                    
                    setTimeout(()=>{
                        var url = new URL(window.location.href);
                        url.searchParams.delete('qkpaystatus');
                        window.location.href = url.href
                    }, 3000)
                } else {
                    this.checkTime = setTimeout(()=>{
                        this.payCheck()
                    }, 1000)
                }
            })
        }
    },
    watch: {
        show(newVal, oldVal) {
            clearInterval(this.timer)
            this.success = false
            this.checkTime = null
        }
    }
})

//确认支付
Vue.component('confirmPay', {
    props: ['data','show'],
    template:`
    <div class="confirm-pay-container">
        <slot></slot>
        <div class="colorful-header qk-flex">
            <div class="title">
                <i class="ri-shopping-cart-fill"></i>
                <div>确认购买</div>
            </div>
        </div>
        <div class="confirm-pay-content">
            <div class="pay-info">
                <div class="pay-info-title">{{data.title}}</div>
                <div class="pay-info-price qk-flex">
                    <span class="left pay-tag">{{data.tag}}</span>
                    <span class="rigth">
                        <span>价格：</span>
                        <span><span><i class="ri-coins-line" v-if="type == 'credit'"></i>{{type == 'money' ? '￥':''}}</span>{{data.order_price}}</span>
                    </span>
                </div>
            </div>
            <qk-pay-type :pay_type="pay_type" :type="type" @data="setData" @chosePayType="chosePayType"></qk-pay-type>
            <div class="pay-button">
                <button @click="pay()">
                    <span>立即支付 <i class="ri-coins-line" v-if="type == 'credit'"></i>{{type == 'money' ? '￥':''}}{{discountedPrice}}</span>
                </button>
            </div>
        </div>
    </div>`,
    data() {
        return {
            pay_type:'credit',
            money:0,
            credit:0,
        }
    },
    // mounted() {
    //     if(!qktoken && this.type == 'credit') return this.$createModal('login');
    // },
    computed: {
        type() {
            return this.data.type;
        },
        discountedPrice() {
            return this.data.order_price;
        },
    },
    methods: {
        setData(data) {
            this.credit = data.credit
            this.money = data.money
        },
        chosePayType(val){
            this.pay_type = val;
        },
        pay(){
            let error = '';
            (!this.pay_type || this.pay_type == 'credit') && this.type == 'money' && (error = '请选择付款方式');
            
            (this.type == 'money' && this.pay_type == 'balance'  && parseInt(this.money) < this.discountedPrice) && (error = '余额不足');
            (this.type == 'credit' && this.credit < this.discountedPrice) && (error = '积分不足');
            
            if(error) {
                return this.$message({ message: error, type: 'error' }); 
            }
            
            var data = {
                'title':this.data.title,
                'order_type':this.data.order_type,
                'order_price':parseInt(this.discountedPrice),
                'pay_type':this.pay_type,
                'post_id':this.data.post_id,//qk_global.post_id,
                'chapter_id':this.data.chapter_id,
                'order_key':this.data.order_key,
                'order_value':this.data.order_value,
            }

            this.$createModal('pay',{size:312,keepAlive:false,props:{
                data:data
            }})
            
            this.$emit('close-modal');
        },
        destroy(){
            this.pay_type = 'credit';
            this.data = []
        }
    },
    watch: {
        // show(newVal, oldVal) {
        //     if(newVal == false){
        //         this.pay_type = 'credit';
        //         this.data = []
        //     }
        // }
    }
})

//密码验证
Vue.component('passwordVerify',{
    props: ['data','show'],
    template:`
    <div class="password-pay-container">
        <slot></slot>
        <div class="colorful-header qk-flex">
            <div class="title">
                <i class="ri-lock-2-line"></i>
                <div>密码验证</div>
            </div>
        </div>
        <div class="form-container">
            <div class="scan-info">
                <div class="qrcode-img qk-radius" v-if="data.qrcode_img">
                    <img :src="data.qrcode_img" class="w-h">
                </div>
                <div class="text" v-if="data.tips_text" v-html="data.tips_text"></div>
            </div>
            <form @submit.stop.prevent="passwordVerify">
                <password-input v-model="code" :length="data.length" :mask="false" :focused="true"></password-input>
                <div class="form-button">
                    <button>确认</button>
                </div>
            </form>
        </div>
    </div>`,
    data() {
        return {
            code:'',
        }
    },
    methods: {
        passwordVerify() {
            if(!this.code.trim()) return this.$message({ message: '请输入密码', type: 'error' }); 
            
            if(this.locked == true) return
            this.locked = true
            
            let params = {
                'post_id':this.data.post_id ? this.data.post_id : qk_global.post_id,
                'code':this.code,
                'type':this.data.type ? this.data.type : 'post',
            };

            this.$http.post(qk_rest_url+'passwordVerify',params).then(res=>{
                this.locked = false
                this.$emit('close-modal')
                
                //回调
                if (typeof this.data.confirm === 'function') {
                    return this.data.confirm(params);
                }
                
                qkSetCookie('password_verify',JSON.stringify(params),this.data.day);
                this.$message({message: res.data.msg,type: 'success'});
                
                //刷新当前页面
                setTimeout(()=>{
                    qkCurrentPageReload()
                }, 2000)
            }).catch(err=>{
                this.locked = false
                this.$message({ message: err.response.data.message, type: 'error' });
            })
            
        },
        destroy(){
            this.code = ''
        }
    },
    watch: {
        // show(newVal, oldVal) {
        //     if(newVal == false){
        //         this.code = ''
        //     }
        // },
    }
})

//绑定
Vue.component('binding',{
    props: ['data', 'show'],
    template: `
    <div class="binding-container">
        <slot></slot>
        <div class="colorful-header qk-flex">
            <div class="title">
                <i :class="type.icon"></i>
                <div>{{ type.name }}</div>
            </div>
        </div>
        <div class="form-container">
            <form @submit.stop.prevent="changeData">
                <label class="form-item" v-if="type.key !== 'password'">
                    <input type="text" name="username" v-model="param.username" spellcheck="false" autocomplete="off" :placeholder="type.placeholder">
                </label>
                <label class="form-item" v-if="type.key !== 'password'">
                    <input type="text" name="code" v-model="param.code" spellcheck="false" autocomplete="off" placeholder="请输入验证码">
                    <div class="login-eye text" @click.stop.prevent="countdown == 60 ? getCode() : null">{{countdown < 60 ? countdown+\'秒后可重发\' : \'发送验证码\'}}</div>
                </label>
                <label class="form-item" v-if="type.key === 'password'">
                    <input type="password" name="password" v-model="param.password" autocomplete="off" spellcheck="false" placeholder="新密码">
                </label>
                <label class="form-item" v-if="type.key === 'password'">
                    <input type="password" name="confirm_password" v-model="param.confirm_password" autocomplete="off" spellcheck="false" placeholder="确认密码">
                </label>
                <div class="form-button">
                    <button>确认</button>
                </div>
            </form>
        </div>
    </div>`,
    data() {
        return {
            param: {
                username: '',
                password: '',
                code: '',
                confirm_password: '',
                type: '',
            },
            locked: false,
            typeMap: {
                email: {
                    key: 'email',
                    name: '绑定邮箱',
                    placeholder: '邮箱',
                    icon: 'ri-mail-settings-line',
                },
                phone: {
                    key: 'phone',
                    name: '绑定手机号',
                    placeholder: '手机号',
                    icon: 'ri-phone-line',
                },
                password: {
                    key: 'password',
                    name: '修改密码',
                    placeholder: '手机号或邮箱',
                    icon: 'ri-lock-password-line',
                },
            },
            countdown:60
        };
    },
    computed: {
        type() {
            return this.typeMap[this.data.type];
        },
    },
    methods: {
        changeData() {
            const api = this.type.key === 'password' ? 'changePassword' : 'changeEmailOrPhone';
            if (this.locked) return;
            this.locked = true;
            this.param.type = this.type.key;
            this.$https.post(qk_rest_url + api, this.param).then((res) => {
                this.locked = false;
                this.$message({message: res.data.msg,type: 'success'});
                //刷新当前页面
                qkCurrentPageReload();
            }).catch((err) => {
                this.locked = false;
                this.$message({
                    message: err.response.data.message,
                    type: 'warning'
                });
            });
        },
        getCode() {
            this.$https.post(qk_rest_url + 'sendCode', this.param).then((res) => {
                    this.$message({
                        message: res.data.msg,
                        type: 'success'
                    });
                    this.startCountdown()
                }).catch((err) => {
                    this.$message({
                        message: err.response.data.message,
                        type: 'warning'
                    });
                });
        },
        startCountdown() {
            let intervalId = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    clearInterval(intervalId);
                    this.countdown = 60;
                }
            }, 1000)
        },
        destroy(){
            Object.assign(this.$data, this.$options.data.bind(this)());
        }
    },
    watch: {
        // show(newVal, oldVal) {
        //     if (newVal === false) {
        //         Object.assign(this.$data, this.$options.data.bind(this)());
        //     }
        // },
    },
})

addLoadEvent(()=>{
    if (qkGetQueryVariable('qkpaystatus') == 'check') {
        createModal('payCheck');
    }
});

//图片上传
Vue.component('imageUploadBox', {
    props: ['data','show'],
    template:`<div class="image-upload-box" @click.stop="">
                <slot></slot>
                <div class="container-top">
                    <span :class="{'active':tabType == 1}" @click="changeTab(1)">我的图片</span>
                    <span :class="{'active':tabType == 2}" @click="changeTab(2)" v-if="computedData.showTabType == 2">外链图片</span>
                </div>
                <div class="container-content">
                    <div class="images-list" v-if="tabType == 1" ref="imageList">
                        <label class="list-item">
                            <input accept="image/jpg,image/jpeg,image/png,image/gif" type="file" @change="fileUpload($event)" ref="fileInput" multiple="multiple" style=" display: none;" :disabled="locked">
                            <div class="img upload-btn"></div>
                        </label>
                        <div :class="['list-item',{'active':imagePicked.findIndex(obj => obj.url == item.url) !== -1}]" v-for="(item,index) in imageList" @click="pickedImg(item)">
                            <div class="img">
                                <img class="w-h" :src="item.thumb">
                            </div>
                            <div class="progress-text" v-if="item.progress || item.success">
                                <span v-text="item.progress >= 100 ? (item.success ? '完成' : '处理中 ...' ) : item.progress + '%'"></span>
                            </div>
                            <div class="progress progress-striped" v-if="item.progress || item.success">\
                                <div class="progress-bar progress-bar-success" :style="'width: ' + item.progress + '%;'"></div>
                            </div>
                            <div class="active-index" v-if="imagePicked.findIndex(obj => obj.url == item.url) !== -1">{{imagePicked.findIndex(obj => obj.url == item.url)+1}}</div>
                        </div>
                    </div>
                    <div v-if="tabType == 2 && computedData.showTabType == 2" class="images-link" style=" padding: 16px; "><p style=" color: var(--color-text-secondary); font-size: 15px; padding-bottom: var(--gap); ">请填写图片地址，支持批量输入，一行一个链接</p><textarea type="text" name="imageLink" id="imageLink" class="trix-input trix-input--dialog" style=" border: 0; width: 100%; min-height: 31vh; background: var(--bg-body-color); color: var(--color-text-secondary); padding: 12px; box-sizing: border-box; line-height: 1.5; "></textarea></div>
                </div>
                <div class="insert-img-button"><button @click="insertImg()">确认</button></div>
            </div>`,
    data() {
        return {
            imageList:[],
            imagePicked:[],
            pages:0,
            paged:0,
            locked:false,
            tabType:1,
            uploadCount:1,// 已上传张数
        }
    },
    mounted(){
        if(!qktoken) return this.$createModal('login');
        this.getAttachments()
        this.$emit('loadinged')
    },
    computed: {
        computedData() {
            const defaultData = {
                showTabType:2,
                maxPicked:20,
                postType:'post',
                size:5,
                callback:(data,type) => {}
            };

            if (this.data) {
              return {
                ...defaultData,
                ...this.data,
              };
            }
            
            return defaultData;
        }
    },
    methods: {
        changeTab(type){
            this.tabType = type
        },
        getAttachments(){
            if(this.paged >= this.pages && this.paged != 0) return;
            if(this.locked == true) return
            this.locked = true
            this.paged++

            
            this.$http.post(qk_rest_url+'getCurrentUserAttachments','paged='+this.paged+'&type=image').then(res=>{
                this.imageList.push(...res.data.data)
                this.pages = res.data.pages
                this.locked = false
                
                this.$nextTick(() => {
                    this.$refs.imageList.addEventListener('scroll', _debounce(this.handleScroll));
                });
                
            }).catch(err=>{
                this.$message({ message: err.response.data.message, type: 'warning' });
                this.locked = false
            })
        },
        handleScroll() {
            var imageList = this.$refs.imageList;
            if ((imageList.scrollTop + imageList.offsetHeight) >= (imageList.scrollHeight - 20)) {
                this.getAttachments();
            }
        },
        fileUpload(event) {
            if (event.target.files.length <= 0) return
            if (event.target.files.length > 9) {
                this.$message({ message: `一次性最多只能上传9张图片`, type: 'warning' });
                return
            }
            
            if(this.locked == true) return
            this.locked = true
            
            const maxUploadCount = 9 // 最大上传张数

            for (let i = 0; i < event.target.files.length; i++) {
                let file = event.target.files[i]
                let url = URL.createObjectURL(file)
                
                if(file.size <= this.computedData.size * 1024000) {
                    if (this.uploadCount >= maxUploadCount) {
                        this.$message({ message: `最多只能上传${maxUploadCount}张图片`, type: 'warning' });
                        break
                    }
            
                    // 检查是否已经上传过该图片
                    if(this.imageList.findIndex(item => item.name === file.name && item.size === file.size) !== -1) {
                        setTimeout(()=> {
                            this.$message({ message: `文件[${file.name}]已经上传过，请选择其他文件`, type: 'warning' });
                        }, 300);
                        
                        this.locked = false
                        continue
                    }
                    
                    this.imageList.splice(i,0,{
                        'id':'',
                        'url':'',
                        'thumb':url,
                        'progress':0,
                        'success':false,
                        'name': file.name, // 记录文件名和大小
                        'size': file.size
                    })
                    
                    let formData = new FormData()
                    formData.append('file', event.target.files[i], event.target.files[i].name)
                    formData.append("post_id", 1)
                    formData.append("type", this.computedData.postType)
                    
                    const config = {
                        onUploadProgress: (progress) => {
                            // 格式化成百分数
                            this.imageList[i].progress = Math.floor(progress.loaded/progress.total*100)
                        }
                    }
                    
                    this.$http.post(qk_rest_url + 'fileUpload',formData,config).then(res=>{
                        
                        this.imageList[i].url = res.data.url
                        this.imageList[i].id = res.data.id
                        this.imageList[i].success = true
                        this.imageList[i].thumb = res.data.url
                        this.pickedImg({url:res.data.url,id:res.data.id})
                        this.locked = false
                        this.uploadCount++;
                        
                        setTimeout(()=> {
                            this.imageList[i].success = false
                            this.imageList[i].progress = 0
                        }, 300);
                        
                    }).catch(err=>{
                        let msg = err.response.data
                        this.$message({ message: msg ? msg.message : '网络原因上传失败，请重新上传', type: 'warning' });
                        //上传失败，删除图片
                        this.imageList.splice(i, 1);
                        
                        this.locked = false
                    })
                    
                }else {
                    setTimeout(()=> {
                        this.$message({ message: "文件[" + file.name + "]大小超过限制，最大"+this.computedData.size+"M，请重新选择", type: 'error' });
                        this.locked = false
                    }, 300);
                }
            }
            
            this.$refs.fileInput.value = "" // 清空选择的文件
        },
        pickedImg(srcData){
            if(!srcData) return;

            var index = this.imagePicked.findIndex(item => item.url === srcData.url);//indexOf(src)

            if(!this.computedData.maxPicked || this.computedData.maxPicked == 1){
                this.imagePicked = []
            }
            if (index == -1) {
                if (this.imagePicked.length < this.computedData.maxPicked) {
                    this.imagePicked.push(srcData)
                }
            } else {
                this.imagePicked.splice(index, 1);
            }

        },
        insertImg(){
            
            let imagePicked = [];
            
            if(this.tabType == 2){
                let imageLink = document.querySelector('#imageLink').value
                
                if(imageLink) {
                    imagePicked = imageLink.split("\n").map(url => ({ url: url.trim() }));
                }
            }else{
                imagePicked = this.imagePicked
            }
            
            this.tabType == 1
            
            if(!imagePicked.length) return
            
            //回调
            this.$nextTick(() => {
                if (typeof this.computedData.callback === 'function') {
                  this.computedData.callback(imagePicked,this.tabType);
                }
            });
            
            this.$emit('close-modal');
            return
        },
        destroy(){
            this.tabType = 1;
            this.imagePicked = [];
        }
    },
    watch: {
        // show(newVal, oldVal) {
        //     if (newVal === false) {
        //         this.tabType = 1;
        //         this.imagePicked = [];
        //     }
        // },
    },
})

//举报
Vue.component('report',{
    props:['post_id'],
    template: `
    <div class="report-container">
        <div class="colorful-header qk-flex">
            <div class="title">投诉举报</div>
        </div>
        <div class="content-wrap">
            <div class="form-container">
                <form @submit.stop.prevent="report">
                    <div class="report-types">
                        <label class="type-item" v-for="(item,index) in types" :key="index">
                            <input type="radio" :value="index" v-model="selectedType">
                            <div>{{item}}</div>
                        </label>
                    </div>
                    <label class="form-item">
                        <textarea placeholder="请详细描述举报原因，便于平台判断违规情况" v-model="content"></textarea>
                    </label>
                    <div class="form-button">
                        <button>提交</button>
                    </div>
                </form>
            </div>
        </div>
    </div>`,
    data() {
        return {
            locked:false,
            types:[],
            selectedType:null,
            content:''
        };
    },
    mounted(){
        this.$http.get(qk_rest_url+'getReportTypes').then(res=>{
            this.types = res.data
            this.$nextTick(()=>{
                this.$emit('loadinged')
            })
        })
    },
    methods: {
        report() {
            if(this.locked) return
            this.locked = true
            
            this.$http.post(qk_rest_url+'postReport',{
                type:this.selectedType,
                reported_id:this.post_id,
                content:this.content
            }).then(res=>{
                this.locked = false
                this.$message({ message: '您的反馈已收到，我们将尽快受理', type: 'success' });
                this.$emit('close-modal');
            }).catch(err=>{
                this.locked = false
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        destroy(){
            //Object.assign(this.$data, this.$options.data.bind(this)());
            this.selectedType = null;
            this.content = ''
        }
    },
})

//QK主题支付
function qkpay(data,type = 'credit'){
    if(!qktoken && type == 'credit'){
        createModal('login');
    }else{
        //let data = JSON.parse(data.getAttribute('data-pay'));
        
        if(!data)  return Vue.prototype.$message({message: '参数错误',type: 'warning'});
        
        createModal('confirmPay',{
            size:356,
            loading:false,
            keepAlive:false,
            props:{
                data:data
            },
        })
    }
}

//密码验证
function passwordVerify(post_id){
    qk_global.post_id = post_id;
    createModal('passwordVerify',{
        size:312,
        loading:false,
        props:{
            data:qk_global.password_verify
        },
    })
}


var menuUserBox = new Vue({
    el: '.menu-user-box',
    data:{
    },
    methods: {
        loginOut() {
            this.$http.get(qk_rest_url + 'loginOut').then(res=>{
                qkDelCookie('qk_token')
                qkCurrentPageReload()
            }).catch(err=>{
                qkDelCookie('qk_token')
                qkCurrentPageReload()
            })
        },
        recharge(type){
            this.$createModal('recharge',{
                props:{
                    type:type
                }
            })
        },
        payVip() {
            this.$createModal('vip',{size:720})
        }
    }
})


/****
 * 1.把现有的 window.onload 事件处理函数的值存入变量 oldonload;
 * 2.如果在这个处理函数上还没有绑定任何函数，就像平时那样把新函数添加给它；
 * 3.如果在这个处理函数上已经绑定了一些函数，就把新函数追加到现有指令的末尾。
 * */
function addLoadEvent(func) {
    var oldonload = window.onload;
    if(typeof window.onload != 'function'){
        window.onload = func;
    }else{
        window.onload = function(){
            oldonload();
            func();
        }
    }
}

//判断是否是微信
function qkisWeixin() { 
    var ua = navigator.userAgent.toLowerCase();
    return ua.match(/MicroMessenger/i) == "micromessenger";
};

//获取url查询字段
function qkGetQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return decodeURIComponent(pair[1]);
        }
    }
    return (false);
}

function getQueryVariable(variable) {
    const url = window.location.href;
    const params = new URLSearchParams(new URL(url).search);
    const paramValue = params.get(variable);
    return paramValue;
}

/**
 * 设置浏览器地址参数
 * @param {Object} params - 要设置的参数对象，键值对形式
 * @returns {void}
 */
function qkSetQueryParams(params) {
    // 获取当前URL的查询字符串部分
    //const queryString = window.location.search;

    // 使用URLSearchParams解析查询字符串
    const urlParams = new URLSearchParams();//queryString

    // 遍历参数对象，设置新的参数值或更改现有的参数值
    for (const [paramName, paramValue] of Object.entries(params)) {
        urlParams.set(paramName, paramValue);
    }

    // 获取更新后的查询字符串
    const newQueryString = urlParams.toString();

    // 获取当前URL的基本部分
    const baseUrl = window.location.href.split('?')[0];

    // 构建新的URL
    const newUrl = `${baseUrl}?${newQueryString}`;

    // 使用history.pushState()方法更改浏览器地址参数
    history.pushState(null, null, newUrl);
}

//添加过渡动画
function addFadeIn(dom,c='fade-in',time = 300){
    if(typeof dom == 'string'){
        dom = document.querySelector(dom); //All
    }
    
    if(typeof dom == 'object'){
        if(dom.className.indexOf(c) === -1){
            dom.style.display = 'block';
            setTimeout(()=>{
                dom.classList.add(c)
            }, 1 )
        }else{
            //退出
            dom.classList.remove(c);
            //dom.classList.add("fade-out");
            setTimeout(()=>{
                //dom.classList.remove("fade-out");
                dom.style.display = 'none';
            }, time )
        }
    }
}

//添加过渡动画
function addOpacityIn(dom,c='opacity',time = 300){
    if(typeof dom == 'string'){
        dom = document.querySelector(c); //All
    }

    if(typeof dom == 'object'){
        dom.classList.remove(c);
        setTimeout(()=>{
            dom.classList.add(c)
            
        }, time )
    }
}

/**图片灯箱**/
function imgFancybox(selector, customOptions) {
    const defaultOptions = {
        groupAll: true,
        //dragToClose: false, //启用拖动关闭手势-向上/向下拖动内容以关闭实例
        //Toolbar: false,
        //closeButton: "top",
        hideScrollbar: false,
        preload: true, //预加载功能
        Slideshow: {
            playOnStart: false,
        },
        // Image: {
        //     zoom: false,
        // },
        // on: {
        //     initCarousel: (fancybox) => {
        //         const slide = fancybox.Carousel.slides[fancybox.Carousel.page];

        //         fancybox.$container.style.setProperty(
        //             "--bg-image",
        //             `url("${slide.$thumb.src}")`
        //         );
        //     },
        //     "Carousel.change": (fancybox, carousel, to, from) => {
        //         const slide = carousel.slides[to];

        //         fancybox.$container.style.setProperty(
        //             "--bg-image",
        //             `url("${slide.$thumb.src}")`
        //         );
        //     },
        // },
    };

    // 合并默认设置和自定义设置
    const options = Object.assign({}, defaultOptions, customOptions);

    Fancybox.bind(selector, options);
}
imgFancybox('.article-content img')
//addLoadEvent(imgFancybox)

//幻灯
function sliderCarousel() {
    
    var slider = document.querySelectorAll(".carousel-area")
    // Carousel.Plugins.Autoplay = Autoplay;
    if(slider) {
        var mainCarousel = []
        for (let i = 0; i < slider.length; i++) {
            mainCarousel[i] = new Carousel(slider[i], {
            //preload:3,
                Navigation:false,
                center : false,
                Autoplay: {
                    timeout: 4000,
                },
                on: {
                    init: (carousel) => {
                        carousel.$tool = slider[i].parentNode.querySelector('.carousel-tool')
                        var imageList = slider[i].querySelectorAll('.slider-img')
                        
                        if(!imageList.length) return;
                        
                        carousel.$agba = []
                        
                        if(slider[i].parentNode.querySelector('.carousel-mask')) {
                            Array.from(imageList).forEach((e,i)=>{
                                carousel.$agba[i] = imageRgba(e)
                            })
                        }
                        
                        //setInterval(() => {carousel.slideNext()},6000)
                    },
                    refresh: (carousel) => {
                      // Update total count
                        if (carousel.$tool) {
                            
                            if(carousel.$tool.children.length) {
                                carousel.$tool.children[carousel.page].style.display = 'block'
                            }
                            
                            if(carousel.$agba.length) {
                                var color = slider[i].parentNode.querySelector('.carousel-mask')
                                color.style.background = carousel.$agba[0]
                            }
                        }
                    },
                    change: (carousel) => {
                      // Update index of the current page
                        if (carousel.$tool) {
                            if(carousel.$tool.children.length) {
                                Array.from(carousel.$tool.children).forEach((e,i)=>{
                                    e.removeAttribute('style')
                                })
                                
                                carousel.$tool.children[carousel.page].style.display = 'block'
                            }
                            
                            if(carousel.$agba.length) {
                                var color = slider[i].parentNode.querySelector('.carousel-mask')
                                color.style.background = carousel.$agba[carousel.page]
                            }
                        }
                    }
                }
            }, { Autoplay });
        }
    }
}

addLoadEvent(sliderCarousel)

//获取图片主色调算法 https://blog.csdn.net/qq_42460209/article/details/124716535
function imageRgba(imgDom) {
    // 先从缓存里获取
    let cache = JSON.parse(localStorage.getItem('imageRgbaCache')) || {};
    let key = imgDom.src;
    if (cache[key]) {
        return cache[key];
    }

    let canvas = document.getElementById('canvas');

    if (canvas == null) {
        canvas = document.createElement("canvas");
        document.body.appendChild(canvas);
        canvas.width = imgDom.width;
        canvas.height = imgDom.height;
        canvas.id = "canvas";
        canvas.style.display = 'none';
    }

    var ctx = canvas.getContext("2d");

    try {
        ctx.drawImage(imgDom, 0, 0, imgDom.width, imgDom.height);
        var imgData = ctx.getImageData(0, 0, imgDom.width, imgDom.height);

        let r = 0;
        let g = 0;
        let b = 0;
        let a = 0;
        for (let row = 0; row < imgDom.height; row++) {
            for (let col = 0; col < imgDom.width; col++) {
                r += imgData.data[((imgDom.width * row) + col) * 4];
                g += imgData.data[((imgDom.width * row) + col) * 4 + 1];
                b += imgData.data[((imgDom.width * row) + col) * 4 + 2];
                a += imgData.data[((imgDom.width * row) + col) * 4 + 3];
            }
        }

        r /= (imgDom.width * imgDom.height);
        g /= (imgDom.width * imgDom.height);
        b /= (imgDom.width * imgDom.height);
        a /= (imgDom.width * imgDom.height);

        r = Math.round(r);
        g = Math.round(g);
        b = Math.round(b);
        a = Math.round(a);

        let rgba = {
            r,
            g,
            b,
            a,
        }

        let result = 'rgba(' + r + ',' + g + ',' + b + ',0.98)';
        cache[key] = result;
        localStorage.setItem('imageRgbaCache', JSON.stringify(cache));
        return result;
    } catch (error) {
        // 图片跨域，返回默认值
        return 'rgba(0, 0, 0, 0)';
    }
}

//横向滚动
function swiperScroll() {
    
    var slider = document.querySelectorAll("#swiper-scroll")
    
    if(slider) {
        for (let i = 0; i < slider.length; i++) {
            new Carousel(slider[i], {
            //preload:3,
                infinite:false,
                Navigation:false,
                center : false,
                Dots: false,
                dragFree: true,
            });
            
            // new BScroll(slider[i], {
            //     scrollX: true,
            //     probeType: 3, // listening scroll event
            //     click:true    
            // })
        }
    }
    
    
}

addLoadEvent(swiperScroll);


var passiveSupported = false;

try {
  var options = Object.defineProperty({}, "passive", {
    get: function() {
      passiveSupported = true;
    }
  });

  window.addEventListener("test", null, options);
} catch(err) {}

//滚动条事件
function qkScroll( fn ) {
    var beforeScrollTop = document.documentElement.scrollTop,
      fn = fn || function() {};

    window.bodyScrool = function() {
        var afterScrollTop = document.documentElement.scrollTop || document.body.scrollTop,
          delta = afterScrollTop - beforeScrollTop;
  
        if( delta === 0 ) return false;
          
          fn( delta > 0 ? "down" : "up" ,afterScrollTop);
  
        beforeScrollTop = afterScrollTop;
    }

    window.addEventListener("scroll",_debounce(window.bodyScrool) , passiveSupported ? { passive: true } : false);
}

function qkHeaderTop(){

    // const banner = document.querySelector('.header .header-banner');
    const header = document.querySelector('.header.transparent');
    const site = document.querySelector('.site');
    const aside = document.querySelector('.bar-user-info');
    // const socialTop = document.querySelector('.social-top');
    // const nosub = document.querySelector('.social-no-sub');
    // const footer = document.querySelector('.mobile-footer-menu');
    if(!header) return
    
    
    
    let h = 1//banner.clientHeight - 64//parseInt(getComputedStyle(document.documentElement).getPropertyValue('--top_menu_height'))
    // if(B2ClientWidth < 768){
    //     h = 77
    // }

    // if(socialTop){
    //     h = 113
    // }

    // if(nosub){
    //     h = 58
    // }
    //up 下滑 down 上化
    qkScroll((direction,top)=>{

        if(top > h){
            if(direction === 'down'){
                if(site.className.indexOf(' up') === -1){
                    site.className += ' up'
                }
                
            }else{
                site.className = site.className.replace(' up','')
            }
            
            if(site.className.indexOf(' action') === -1){
                site.className += ' action'
                header.classList.remove("transparent")
            } 
        }else{
            header.classList.add("transparent")
            site.className = site.className.replace(' action','')
            site.className = site.className.replace(' up','')
        }
    });
}

qkHeaderTop()

//防抖节流
function _debounce(fn, delay) {
 
    var delay = delay || 200;
    var timer;
    return function () {
        var th = this;
        var args = arguments;
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(function () {
            timer = null;
            fn.apply(th, args);
        }, delay);
    };
}

/**
 * @description: 设置浏览器网址
 * @param {*} url
 * @param {*} title
 * @return {*}
 */
function routeGo(url, data, title) {
    title = title || document.title;
    if (url) {
        history.pushState(data, title, url);
    }
}

// document.open();
// document.write('1');
// document.close();
// 阻止超时导致链接跳转事件发生
//event.preventDefault();

function qkGetCookie(name){
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for(var i=0;i < ca.length;i++) {
        var c = ca[i];
        while (c.charAt(0)==' ') c = c.substring(1,c.length);
        if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
    }
    return null;
}

function qkSetCookie(name,value,days = 7){

    //let days = 7;//b2_global.login_keep_days
    var expires = "";
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + (days*24*60*60*1000));
        expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + "=" + (value || "")  + expires + "; path=/";
}

function qkDelCookie(name){
    document.cookie = name +'=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}


//分页代码
Vue.component('page-nav',{
    props: ['paged','pages','navtype','type','selector','param','api'],
    template: `
        <div class="qk-ajax-pager" v-show="pages > 1 && pageCount > 1">
            <div class="ajax-pagination"  v-if="type === 'page'">
                <button type="button" class="btn-prev" :disabled="currentPage <= 1" @click.stop.self="load(currentPage-1,'next')">❮</button>
                <ul class="qk-pager">
                    <li v-for="page in pagers" :class="['number',{ active: currentPage === page }]" @click.stop.self="load(page)" :disabled="disabled(page) || page == 0 || currentPage == page">{{page != 0 ? page : '...'}}</li>
                </ul>
                <button type="button" class="btn-prev" :disabled="currentPage >= pageCount" @click.stop.self="load(currentPage+1,'per')">❯</button>
            </div>
            <div class="ajax-auto" v-else>
                <p v-if="loading">加载中...</p>
                <p v-else-if="noMore">没有更多了</p>
                <p @click.stop.self="load(currentPage+1,'per')" v-else>❯ 加载更多</p>
            </div>
        </div>`,
    data(){
        return {
            locked:false,
            currentPage:1, //当前页数
            pageCount:0, //总页数
            mobile:false
        }
    },
    created(){
        window.addEventListener('scroll', this.autoLoadMore)

        this.currentPage = parseInt(this.paged)
        this.pageCount = parseInt(this.pages)

        //监听前进与后退
        window.addEventListener("popstate", ()=>{
            let state = history.state;
            if(state && state.page && this.type == 'page'){
                this.load(state.page)
            }
        });
        this.mobile =false
    },
    computed: {
        pagers() {
            //页码按钮的数量，当总页数超过该值时会折叠
            const pagerCount = 7//this.pagerCount;
            
            const array = [];
            
            if(this.pageCount > pagerCount){
                if(this.currentPage < 5){
                    for (let i = 1; i <= this.pageCount; i++) {
                        if(i >= 6) break
                        array.push(i)
                    }
                    array.push(0,this.pageCount)
                }else if(this.currentPage >= 5 && this.pageCount - 3 > this.currentPage){
                    array.push(1,0)
                    for (let i = this.currentPage - 2; i <= this.currentPage + 2; i++) {
                        array.push(i)
                    }
                    array.push(0,this.pageCount)
                }else if(this.pageCount - 3 <= this.currentPage){
                    array.push(1,0)
                    for (let i = this.currentPage - 3; i <= this.pageCount; i++) {
                        array.push(i)
                    }
                }
            }else{
                for (let i = 1; i <= this.pageCount; i++) {
                    array.push(i);
                }
            }
            return array;
        },
        noMore() {
            return this.currentPage >= this.pageCount;
        },
        loading() {
            return this.locked
        },
    },
    methods:{
        disabled(page) {
            return page == this.currentPage && this.locked == true
        },
        autoLoadMore(){
            setTimeout(() => {
                if(this.type == 'page') return
                let scrollTop = document.documentElement.scrollTop;
                if(scrollTop + window.innerHeight >= document.body.clientHeight - 50) {
                    this.load(this.currentPage+1)
                }
            }, 300);
        },
        load(page,type,action){
            if(this.locked == true) return;
            
            if(this.currentPage == page && !action) return;
            
            if(this.type === 'auto' && this.currentPage >= this.pageCount && page != 1) return;
            
            if(page == 0) return;
            
            this.locked = true;
            page = parseInt(page)
            this.currentPage = page;
            this.param['paged'] = page;
            
            //文章翻页
            this.param['post_paged'] = page;
            
            this.$http.post(qk_rest_url + this.api,this.param).then(res=>{
                
                this.locked = false;
                //this.pageCount = this.pages = res.data.pages;
                
                this.$nextTick(() => {
                    this.pageCount = this.pages = res.data.pages;
                });
                
                //如果返回的是json数据
                if(this.navtype === 'json'){
                    this.$emit('change',res.data)
                }else{
                    let dom = document.querySelector(this.selector);
                    //如果是分页加载
                    if(this.type === 'page' || action){
                        dom.innerHTML = res.data.data
                        // this.$scrollTo('.site', 300, {offset: 0})
                    }else{
                        dom.insertAdjacentHTML('beforeend', res.data.data)
                    }
                    
                    this.$emit('change',res.data)
                }
                
                if(this.navtype === 'post'){
                    qkPackeryLoad()
                    //渐显
                    setTimeout(()=>{
                        listFadein(document.querySelectorAll(this.box+' > li'),20)
                    },500)
                }
                
                this.$nextTick(()=>{
                    lazyLoadInstance.update()
                })
                
           }).catch(err => {
                this.locked = false
                this.$message({ message: err.response.data.message , type: 'error' });
            })
        },
        //跳转
        // jump:function(event){
        //     var val = event.target.value || event.target.previousElementSibling.value
        //     if(val > this.pages) return
        //     this.go(parseInt(val))
        // }
    },
})

var pageNavBox = new Vue({
    el:'.post-nav',
    data:{
        selector:'#post-list .qk-grid', //填充数据元素
        api:'getPostList',
        param:{}
    },
    mounted(){
        if(typeof qk_cat !== 'undefined'){
            this.param = qk_cat.param
        }
    },
    methods:{
        
    }
})

//遍历dom树，依次添加 is-visible 类，使其支持渐变显示
function listFadein(dom,time){
    var i = 0
    dom.forEach(e=>{
        if(e.className.indexOf('is-visible') === -1){
            i++
            if(i== 1){
                e.className += ' is-visible'
            }else{
                setTimeout(()=>{
                    e.className += ' is-visible'
                }, i*time )
            }
        }
    })
}

//表情组件
Vue.component('qk-emoji',{
    model: {
        prop: 'show',
        event: 'change'
    },
    props: ['show','target'],
    template: '<div class="qk-emoji box" v-if="me_show  && data.length" @click.stop="">\
                <div class="emoji-title"><span>{{data[index].name}}</span></div>\
                <ul class="emoji-wrap-list" :class="\'emoji-\'+data[index].size">\
                    <li class="emoji-item" v-for="(item,i) in data[index].list" @click.stop="emojiClick(item)">\
                        <img :src="item.icon" :title="item.name">\
                    </li>\
                </ul>\
                <div class="emoji-tabs">\
                    <ul class="emoji-tabs-list">\
                        <li :class="[\'tabs-item\',{active:i == index - (page - 1) * 5}]" v-for="(item,i) in data.slice((page - 1) * 5,page * 5)" @click.stop="changeTabs(i)" v-if="item.list">\
                            <img :src="item.list[0].icon" :title="item.name">\
                        </li>\
                    </ul>\
                    <div class="emoji-pager" v-if="pages > 1">\
                        <div :class="{disabled:page == 1}" @click.stop="prev">❮</div>\
                        <div :class="{disabled:page == pages}" @click.stop="next">❯</div>\
                    </div>\
                </div>\
        </div>',
    data() {
        return {
            me_show: this.show,
            index:0,
            page:1,
            pages:1,
            data:[]
        }
    },
    mounted() {
        this.$http.post(qk_rest_url+'getEmojiList').then(res => {
            this.data = res.data.list
            this.pages = Math.ceil(this.data.length / 5)
        }).catch(err => {
            this.$message({ message: err.response.data.message , type: 'error' });
        })
        
        // 监听窗口大小变化事件
        //window.addEventListener('resize', this.calculatePosition);
    },

    methods: {
        calculatePosition() {
            this.$nextTick(() => {
            // 获取组件的位置和大小信息
                // const container = this.$el.getBoundingClientRect();
                // const screenWidth = window.innerWidth || document.documentElement.clientWidth;
                // const screenHeight = window.innerHeight || document.documentElement.clientHeight;
            
                // // 根据组件的位置和大小信息计算边界
                // const left = container.left + container.width > screenWidth ? screenWidth - container.width : container.left;
                // const top = container.top + container.height > screenHeight ? screenHeight - container.height : container.top;
            
                // // 设置组件的位置
                // this.$el.style.left = left + 'px';
                // this.$el.style.top = top + 'px';
                if(!this.target) return
                
                const targetElement = document.querySelector(this.target)
                const targetRect = targetElement.getBoundingClientRect();
                
                //                   // 计算组件应该显示的位置
                // 获取父元素的位置和大小信息
                const parentElement = targetElement.parentElement.parentElement;
                const parentRect = parentElement.getBoundingClientRect();
                
                // 计算组件应该显示的位置，在下方显示
                const left = targetRect.left - parentRect.left + window.pageXOffset;
                const top = targetRect.top - parentRect.top + targetRect.height + window.pageYOffset;
                
                
                //console.log('left：'+left);console.log('top：'+top);
                // 获取浏览器窗口的宽度和高度
                const windowWidth = window.innerWidth || document.documentElement.clientWidth; //parentElement.offsetWidth//
                const windowHeight = window.innerHeight || document.documentElement.clientHeight; //parentElement.offsetHeight
                
                //console.log('windowWidth：'+windowWidth);console.log('windowWidth：'+windowHeight);
                // 计算组件的边界，考虑到留有间隙
                const componentWidth = this.$el.offsetWidth;
                const componentHeight = this.$el.offsetHeight;
                
                //   windowHeight = windowHeight - top < componentHeight ? parentElement.offsetHeight : windowHeight
                //   console.log('componentWidth：'+componentWidth);console.log('componentHeight：'+componentHeight);
                
                const maxLeft = windowWidth - componentWidth; //-12 留有12px的间隙
                const maxTop = windowHeight - componentHeight; //-12 留有12px的间隙
                //console.log('maxLeft：'+maxLeft);console.log('maxTop：'+maxTop);
                // 根据浏览器边界调整组件的位置
                const adjustedLeft = Math.min(Math.max(left, 12), maxLeft);
                const adjustedTop = Math.min(Math.max(top, 12), maxTop);
                
                // 设置组件的位置
                this.$el.style.left = adjustedLeft + 'px';
                this.$el.style.top = adjustedTop + 'px';

            });
        },
        changeTabs(index) {
            this.index = (this.page - 1) * 5 + index;
        },
        emojiClick(emoji){
            emoji.size = this.data[this.index].size
            this.$emit('emoji-click',emoji);
        },
        prev(){
            this.page === 1 || (this.page -= 1)
        },
        next(){
            this.page === this.pages || (this.page += 1)
        }
    },
    watch: {
        show(val) {
            this.me_show = this.show
            
            if(this.me_show) {
                //this.calculatePosition(); // 计算并设置组件的初始位置
            }
        },
        me_show(val) {
            val == false && this.$emit('change', val)
        }
    },
})

/**
 * Tabs组件
 */
function Tabs() {
    const tabs = document.querySelector('#tabs'); // 获取Tabs组件元素
    if (!tabs) return;
    const navItems = tabs.querySelectorAll('.tabs-nav li'); // 获取选项卡导航元素
    const panes = tabs.querySelectorAll('.tabs-content .tab-pane'); // 获取选项卡内容元素
    const activeBar = tabs.querySelector('.tabs-nav .active-bar'); // 获取选项卡激活条元素

    /**
     * 设置选项卡激活条位置
     */
    const setActiveBarPosition = () => {
        const activeTab = tabs.querySelector('.tabs-nav li.active'); // 获取当前激活的选项卡
        const activeTabWidth = activeTab.offsetWidth; // 获取当前选项卡的宽度
        const activeTabLeft = activeTab.offsetLeft; // 获取当前选项卡距离左侧的距离
        const barWidth = activeBar.offsetWidth; // 获取选项卡激活条的宽度
        activeBar.style.width = `${32 / 2}px`;//`${activeTabWidth}px`;
        //activeBar.style.transform = `translateX(${tabLeft + (tabWidth - barWidth) / 2}px)`; // 设置选项卡激活条的位置
        activeBar.style.transform = `translateX(${activeTabLeft + (activeTabWidth / 2 - 8)}px)`
    };

    /**
     * 处理选项卡点击事件
     * @param {HTMLElement} item - 选项卡导航项元素
     * @param {number} index - 选项卡索引
     */
    const handleTabClick = (item, index) => {
        if (item.classList.contains('active')) { // 如果当前选项卡已激活，则不做处理
            return;
        }
        
        navItems.forEach((item) => { // 移除所有选项卡导航项的激活状态
            item.classList.remove('active');
        });
        item.classList.add('active'); // 给当前选项卡导航项添加激活状态
        
        if(panes.length) {
            panes.forEach((pane) => { // 隐藏所有选项卡内容
                pane.classList.remove('active');
            });
            panes[index].classList.add('active'); // 显示当前选项卡内容
        }
        
        setActiveBarPosition(); // 设置选项卡激活条位置
        
        const e = new CustomEvent('tabChange', { // 创建一个自定义事件
            detail: {
                index: index, // 传递当前选项卡的索引
                element: item // 传递当前选项卡元素
            }
        });
        
        window.dispatchEvent(e); // 触发自定义事件
        
        tabs.dispatchEvent(e); // 触发自定义事件
    };

    navItems.forEach((item, index) => { // 给每个选项卡导航项添加点击事件
        item.addEventListener('click', () => {
            handleTabClick(item, index);
        });
    });

    setActiveBarPosition(); // 初始化选项卡激活条位置

    // tabs.addEventListener('tabChange', function(event) {
    //     console.log('当前选项卡的索引为：', event.detail.index);
    // });
}

Tabs();

function scrollTabs() {
    const tabs = document.querySelector('.scroll-tabs'); // 获取Tabs组件元素
    if (!tabs) return;
    
    const tabScroll = new BScroll('.scroll-tabs', {
        scrollX: true,
        click: true
    });
    
     // 监听tabChange事件，实现选项卡自动滚动居中
    tabs.addEventListener('tabChange', (event) => {
        tabScroll.scrollToElement(event.detail.element, 300, true, true); // 点击选项卡自动滚动居中
    });
}

addLoadEvent(scrollTabs)

//刷新页面
function qkCurrentPageReload(url){
    if(!url){
        url = location.href
    }
    setTimeout(() => {
        location.replace(url)
    }, 200);
    
}

//加载瀑布流
function qkPackeryLoad(){
    var grid = document.querySelectorAll('.qk-waterfall:not(.circle-moment-list)')
    if(grid.length > 0){
        for (let index = 0; index < grid.length; index++) {
            let pack = new Packery( grid[index])
            // 确保所有图片都已加载完成
            // let imgLoad = imagesLoaded( grid[index]);
            // imgLoad.on( 'progress', function( instance, image ) {
            //   var result = image.isLoaded ? 'loaded' : 'broken';
            //   console.log( 'image is ' + result + ' for ' + image.img.src );
            //   pack.layout();
            // });
        }
    }
}
qkPackeryLoad();

/**
 * toggleDarkTheme - 切换暗黑模式
 *
 * @return {void}
 */
function toggleDarkTheme() {
    const themeSwitch = document.querySelector('.theme-toggle input');
    const body = document.body;
    
    // 如果没有找到对应的元素，则输出错误信息并退出函数
    if (!themeSwitch || !body) return;
    
    // 监听 .theme-toggle input 元素的 change 事件
    themeSwitch.addEventListener('change', function() {
        
        //addFadeIn(body,'fade-in',1000)
        
        if(this.checked) {
            body.classList.add('dark-theme');
            qkSetCookie('theme_mode', 'dark-theme', 3650); // 设置cookie时间为永久
        } else {
            body.classList.remove('dark-theme');
            //qkDelCookie('theme_mode');
            qkSetCookie('theme_mode', 'white-theme', 3650); // 设置cookie时间为永久
        }
    });
}

addLoadEvent(toggleDarkTheme);

addLoadEvent(()=>{
    document.body.onclick = (e)=>{
        if(typeof qkComments !== 'undefined'){
            qkComments.showEmoji = false
        }
        
        if(typeof qkMessagePage !== 'undefined'){
            qkMessagePage.showEmoji = false
        }
        
        if(typeof qkMomentEditor !== 'undefined'){
            qkMomentEditor.showEmoji = false
            qkMomentEditor.toolType = ''
        }
        
        if(typeof menuMore !== 'undefined'){
            menuMore.show = false
        }
    }
})

function indexPostModules(){
    let list = document.querySelectorAll('.module-posts .post-list');
    if(list.length > 0){
        for (let i = 0; i < list.length; i++) {
            let key = list[i].getAttribute('data-key')

            if(list[i].id != 'undefined'){
                new Vue({
                    el:'#post-item-'+key,
                    data:{
                        key:0,
                        index:0,
                        box:'',
                        paged:1,
                        pages:0,
                        locked:false,
                        noMore:false,
                        id:0
                    },
                    mounted(){
                        lazyLoadInstance.update()
                        qkPackeryLoad()
                        //if(this.$el.querySelector('.load-more')){
                            this.key = key
                            this.index = this.$el.getAttribute('data-i')
                            this.box = this.$el.querySelectorAll('.qk-grid')[0]
                           // this.showButton = this.$el.querySelector('.load-more').getAttribute('data-showButton')
                        //}
                    },
                    methods:{
                        getList(id,type = ''){
                            
                            if(this.noMore && type == 'more'){
                                return
                            }else{
                                this.noMore = false
                            }
                            
                            if(this.id == id && !type) return;
                            this.id = id;
                            
                            if(this.locked == true) return;
                            this.locked = true;
                            
                            if(type != 'more'){
                                this.paged = 1
                            }else {
                                this.paged = this.paged + 1
                            }
                            
                            let data = {
                                'index':this.index,
                                'id':this.id,
                                'paged':this.paged,
                            }
                            
                            if(type == 'change') {
                                data['orderby'] = 'random';
                            }
                            
                            this.$https.post(qk_rest_url+'getModulePostList',data).then(res=>{
                                if(res.data.count > 0){
                                    //总页数
                                    this.pages = res.data.pages
                                    if(type != 'more'){
                                        this.box.innerHTML = res.data.data
                                    }else {
                                        this.box.insertAdjacentHTML('beforeend', res.data.data)
                                    }
                                    
                                    //已全部加载完成
                                    if(this.paged >= this.pages ){
                                        this.noMore = true
                                    }
                                }else{
                                    this.box.innerHTML = res.data.data
                                }
                                
                                this.locked = false
                                qkPackeryLoad()
                                this.$nextTick(()=>{
                                    lazyLoadInstance.update()
                                })
                            })
                        },
                        loadMore() {
                            this.getList(this.id,'more')
                        },
                        exchange() {
                            this.getList(this.id,'change')
                        }
                        
                    }
                })
            }
        }
    }
}

indexPostModules()

/**
 * 实现手风琴式菜单效果
 * @param {string} selector - 菜单的CSS选择器
 */
function accordionMenu(selector) {
    // 获取所有的菜单项
    var menuItems = document.querySelectorAll(selector + ' > li');

    // 遍历所有的菜单项
    menuItems.forEach((item) => {
        // 获取子菜单
        var subMenu = item.querySelector('.sub-menu') || item.querySelector('.children');

        // 如果有子菜单，则给父菜单添加点击事件
        if (subMenu) {
            var header = item.querySelector('a');
            header.addEventListener('click', (event) => {
                event.preventDefault();

                // 判断当前菜单项是否已经展开
                var isOpen = item.classList.contains('open');

                // 关闭所有的子菜单
                menuItems.forEach((item) => {
                    item.classList.remove('open');
                    var subMenu = item.querySelector('.sub-menu') || item.querySelector('.children');;
                    if (subMenu) {
                        subMenu.style.height = 0;
                    }
                });

                // 如果当前菜单项未展开，则展开它
                if (!isOpen) {
                    item.classList.add('open');
                    var subMenu = item.querySelector('.sub-menu') || item.querySelector('.children');;
                    subMenu.style.height = subMenu.scrollHeight + 'px';
                }
                
                 // 等待过渡结束后再隐藏内容
                //  setTimeout(() => {
                //      var isActive = item.classList.contains('open');
                //      if (!isActive) {
                //          content.style.height = 'auto';
                //      } else {
                //          content.classList.remove('active');
                //      }
                //  }, 300);
            });
        }
    });
}

function setupCollapseItems() {
    
    // 获取所有的菜单项
    var collapses = document.querySelectorAll('.collapse');

    // 遍历所有的菜单项
    collapses.forEach((collapse) => {
        // 获取所有的折叠项元素
        const collapseItems = collapse.querySelectorAll('.collapse-item');
        
        // 获取是否开启手风琴效果的值
        const isAccordionStyle = collapse.dataset.accordion === 'true';

        // 遍历所有折叠项
        collapseItems.forEach((collapseItem) => {
            // 获取折叠项的头部和内容
            const header = collapseItem.querySelector('.collapse-header');
            const content = collapseItem.querySelector('.collapse-content');

            // 监听头部的点击事件
            header.addEventListener('click', () => {
                // 切换内容显示和隐藏
                if (content.style.height) {
                    content.style.height = null;
                    setTimeout(() => {
                        content.style.removeProperty('display');
                    }, 200);
                } else {
                    content.style.display = 'block';
                    content.style.height = `${content.scrollHeight}px`;
                }

                // 切换头部样式类
                header.classList.toggle('active');

                // 如果开启手风琴效果，则关闭其他折项的内容和样式
                if (isAccordionStyle) {
                    collapseItems.forEach((otherItem) => {
                        if (otherItem !== collapseItem) {
                            otherItem.querySelector('.collapse-content').style.height = null;
                            setTimeout(() => {
                                otherItem.querySelector('.collapse-content').style.removeProperty('display');
                            }, 200);
                            otherItem.querySelector('.collapse-header').classList.remove('active');
                        }
                    });
                }
            });
        });
    });
}

// 调用函数
addLoadEvent(setupCollapseItems)

/**********日历签到*************/
Vue.component('calendar-checkin', {
    props: {
        isCheckIn: {
            type: Boolean,
            default: false
        },
        consecutiveDays: {
            type: Number,
            default: 0
        }
    },
    template: `
        <div class="calendar-checkin box" v-cloak>
            <div class="calendar-header">
                <div class="calendar-title">{{ month }} 月累计签到</div>
                <span class="days">{{ checkedDays }} / {{ totalDays }}</span>
            </div>
            <div class="calendar-body">
                <div class="weekdays">
                    <span class="weekday" v-for="day in weekDays" :key="day">{{ day }}</span>
                </div>
                <div class="days">
                    <span
                          class="day-item"
                          v-for="day in daysInMonth"
                          :key="day"
                          :class="[{ checked: signDaysList.includes(day) }, { 'hide': day == null }, { 'cur-day': isToday(day) }]">
                        {{ day }}
                    </span>
                </div>
            </div>
            <div class="calendar-footer">
                <div class="checkin-info">
                    已连续签到{{ consecutiveDaysCopy }}天
                </div>
                <button class="checkin-btn bg-text" :disabled="isCheckIn" @click="checkin">{{ isCheckIn ? '今日已' : '' }}签到</button>
            </div>
        </div>
    `,
    data() {
        return {
            year: new Date() .getFullYear(),
            month: new Date().getMonth() + 1,
            weekDays: ['日', '一', '二', '三', '四', '五', '六'],
            daysInMonth: [],
            checkedDays: 0, //本月总签到次数
            totalDays: 31,
            consecutiveDays:0, //连续签到
            signDaysList:[],
            curDay: 0, //第几号
            isPrevMonthBtnHidden: false,
            isNextMonthBtnHidden: false,
            consecutiveDaysCopy: this.consecutiveDays
        }
    },
    created() {
        if(qktoken) {
            this.getCheckinData();
        }
        
        this.updateCalendar();
    },
    methods: {
        getCheckinData() {
            this.$https.post(qk_rest_url+'getUserSignInfo').then(res=>{
                const data = res.data.data;
                this.checkedDays = data.signDays;
                this.totalDays = data.allDays;
                this.signDaysList = data.signDaysList;
                this.consecutiveDaysCopy = data.consecutiveDays;
                
                this.curDay = data.curDay;
                
                this.$nextTick(()=>{
                    menuCheckIn.isCheckIn = this.isCheckIn = data.isCheckIn
                })
               

            }).catch(err=>{
                this.$message({message: err.response.data.message, type: 'warning' });
            })
        },
        updateCalendar() {
            const date = new Date(this.year, this.month - 1);
            const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
            const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
            const daysInMonth = [];
            for (let i = 1; i <= lastDay.getDate(); i++) {
                daysInMonth.push(i);
            }
            const firstDayOfWeek = firstDay.getDay();
            if (firstDayOfWeek !== 0) {
                const emptyDays = new Array(firstDayOfWeek).fill(null);
                this.daysInMonth = [...emptyDays, ...daysInMonth];
            } else {
                this.daysInMonth = daysInMonth;
            }
            this.totalDays = daysInMonth.length;
            this.isPrevMonthBtnHidden = firstDay.getDay() === 0;
            this.isNextMonthBtnHidden = lastDay.getDay() === 6;
        },
        checkin() {
            if(!qktoken) return this.$createModal('login');
            this.$nextTick(()=>{
                this.signDaysList.push(parseInt(this.curDay))
            })
            this.checkedDays++;
            this.$emit('checkin-success', this.checkedDays);
        },
        prevMonth() {
            if (this.month === 1) {
                this.month = 12;
                this.year--;
            } else {
                this.month--;
            }
            this.updateCalendar();
        },
        nextMonth() {
            if (this.month === 12) {
                this.month = 1;
                this.year++;
            } else {
                this.month++;
            }
            this.updateCalendar();
        },
        isToday(day) {
            const today = new Date();
            return this.year === today.getFullYear() && this.month === today.getMonth() + 1 && day === today.getDate();
        }
    },
    watch: {
        consecutiveDays(value) {
            this.consecutiveDaysCopy = value;
        }
    },
});

var menuCheckIn = new Vue({
    el: '.menu-check-in',
    data: {
        isCheckIn:false,
        consecutiveDays: 0,
        locked:false,
    },
    methods: {
        checkin() {
            if(this.locked) return;
            this.locked = true;
            
            this.$https.post(qk_rest_url+'userSignin').then(res=>{
                this.isCheckIn = res.data.success;
                this.consecutiveDays =  res.data.consecutiveDays;
                this.locked = false;
                
                let message = '';
                let value = res.data.value;
                
                if(value.credit){
                    message += '积分+' + value.credit
                }
                
                if(value.exp){
                    message += (message ? '、':'') + '经验+' + value.exp
                }
                
                this.$message({
                    showClose: true,
                    message: '签到成功！' + message,
                    type: 'success'
                });
                
            }).catch(err=>{
                this.locked = false;
                this.$message({ message: err.response.data.message, type: 'warning' });
            })
        },
    }
});

/**************搜索组件*******************/
Vue.component('search', {
    template: `
    <div class="menu-search-container">
        <form class="searchform" @submit.prevent="search">
            <div class="search-content">
                <input class="nav-search-input" type="text" autocomplete="off" accesskey="s" maxlength="100" v-model="searchText" placeholder="随便搜搜吧？" @input="handleInput" @compositionstart="compositionStart" @compositionend="compositionEnd" @focus="showSearchPanel = true" @blur="showSearchPanel = false">
            </div>
            <div class="search-btn" @click="search">
                <i class="ri-search-2-line"></i>
            </div>
        </form>
        <div class="search-panel box" v-show="(showSearchPanel && !searchText && searchHistory.length > 0) || (showSearchPanel && searchText && searchSuggestions.length > 0)" @mousedown.prevent>
            <div class="history" v-if="searchHistory.length > 0 && !searchSuggestions.length">
                <div class="history-title">
                    <div class="title">搜索历史</div>
                    <div class="clear" @click="clearSearchHistory">
                        <i class="ri-delete-bin-line" style="font-size: 18px;line-height: 18px;"></i>
                    </div>
                </div>
                <div class="histories-wrap" v-if="!searchSuggestions.length">
                    <div class="histories">
                        <div class="history-item" v-for="(history, index) in searchHistory" :key="index" @click="performSearch(history)">
                            <div class="history-text">{{ history }}</div>
                            <div class="close" @click.stop="removeSearchHistory(index)">
                                <i class="ri-close-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <transition name="fade">
                <div class="suggestions" v-show="!loading">
                    <a class="suggest-item" :href="suggest.link" v-for="(suggest, index) in searchSuggestions" :key="index">
                        <div class="suggest-text" v-html="suggest.title"></div>
                        <div class="suggest-type" :class="suggest.type">{{suggest.type_name}}</div>
                    </a>
                </div>
            </transition>
        </div>
    </div>
   `,
    data() {
        return {
            searchText: qkGetQueryVariable('s') || '',
            searchHistory: JSON.parse(localStorage.getItem('searchHistory')) || [],
            searchSuggestions: [],
            showSearchPanel: false, // 控制搜索建议的显示与隐藏
            composing: false, // 是否处于中文输入状态
            typingTimer: null, // 计时器，用于延迟发送请求
            loading: false 
        }
    },
    methods: {
        search() {

            if(!this.searchText.trim()) return;
            
            // 将searchText添加到searchHistory中
            const newHistory = this.searchText.trim();
            const index = this.searchHistory.indexOf(newHistory);
            if (index !== -1) {
                this.searchHistory.splice(index, 1); // 先移除重复的值
            }
            this.searchHistory.unshift(newHistory); // 将新值添加到数组的第一个位置
        
            // 将searchHistory保存在localStorage中
            localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
            
            // 页面跳转到/?s=搜索词
            window.location.href = `/?s=${this.searchText}`;
        },
        performSearch(history) {
            this.searchText = history;
            this.search();
        },
        clearSearchHistory() {
            this.searchHistory = [];
            // 清除localStorage中的searchHistory
            localStorage.removeItem('searchHistory');
        },
        removeSearchHistory(index) {
            this.searchHistory.splice(index, 1);
            // 更新localStorage中的searchHistory
            localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
        },
        getSuggestions() {
            if(!this.searchText.trim()) return;
            
            this.loading = true;
            this.$https.post(qk_rest_url+'getSearchSuggest','search='+this.searchText).then(res=>{
                this.searchSuggestions = res.data;
                this.loading = false;
            })
        },
        handleInput() {
            // 如果不是中文输入状态，则调用getSuggestions方法
            if (!this.composing) {
                clearTimeout(this.typingTimer);
                this.typingTimer = setTimeout(() => {
                    this.getSuggestions();
                }, 500);
            }
        },
        compositionStart() {
            // 中文输入开始
            this.composing = true;
        },
        compositionEnd() {
            // 中文输入结束
            this.composing = false;
            // 调用getSuggestions方法
            //this.getSuggestions();
        }
    },
    watch: {
        searchText() {
            this.searchText = this.searchText.trim()
            if(!this.searchText) {
                this.searchSuggestions = []
            }
        }
    }
});

var menuSearch = new Vue({
    el: '.center-entry',
    data: {
        noticeList: [],  // 存储公告列表
        currentNoticeIndex: 0  // 当前显示的公告索引
    },
    mounted() {
        // 获取公告列表
        this.$http.post(qk_rest_url + 'getNewNoticeList', 'count=3').then(res => {
            if (res.data.length > 0) {
                this.noticeList = res.data;
                this.showNextNotice();
            }
        });
    },
    methods: {
        showNextNotice() {
            while (this.currentNoticeIndex < this.noticeList.length) {
                const currentNotice = this.noticeList[this.currentNoticeIndex];
                if (currentNotice.show) {
                    createModal('notice', {
                        size: 400,
                        loading: false,
                        keepAlive: false,
                        wrapperClosable: false,
                        props: {
                            data: currentNotice
                        },
                        close: (el) => {
                            this.currentNoticeIndex++;
                            setTimeout(() => {
                                this.showNextNotice();  // 延迟显示下一个公告
                            }, 1000);  // 延迟1秒
                        }
                    });
                    break;  // 找到一个需要显示的公告后退出循环
                } else {
                    this.currentNoticeIndex++;  // 跳过不需要显示的公告
                }
            }
        }
    }
});

var mobileMenu = new Vue({
    el:'#mobile-menu-button',
    data: {

    },
    methods: {
        showMenu(){
            const menu = document.querySelector('.sidebar-menu');
            // 判断当前菜单项是否已经展开
            var isOpen = menu.classList.contains('show');
            
            if(isOpen){
                menu.classList.remove('show');
                document.documentElement.style.overflow = '';
            }else{
                menu.classList.add('show');
                document.documentElement.style.overflow = 'hidden';
            }
        }
    }
});

var mobileSearch = new Vue({
    el:'#mobile-search-button',
    methods: {
        showSearch(){
            const menu = document.querySelector('.center-entry');
            // 判断当前菜单项是否已经展开
            var isOpen = menu.classList.contains('show');
            
            if(isOpen){
                menu.classList.remove('show');
                document.documentElement.style.overflow = '';
            }else{
                menu.classList.add('show');
                document.documentElement.style.overflow = 'hidden';
            }
        }
    }
});

var menuMessage = new Vue({
    el: '.menu-message',
    data: {
        count:0
    },
    mounted() {
        if(!qktoken) return;
        this.$http.post(qk_rest_url+'getUnreadMsgCount').then(res => {
            this.count = res.data.total
        })
    },
    methods: {
        
    }
});

var menuMore = new Vue({
    el: '.more-menu-container',
    data: {
        show:false
    }
});

/**************验证码组件*******************/
Vue.component('password-input', {
    template: `
        <div class="password-input" @click="focusInput">
            <div v-for="(item, index) in length" :key="index" class="password-item">
                <div v-if="mask && password[index]" class="password-dot"></div>
                <div v-if="!mask && password[index]">{{ password[index] }}</div>
                <div v-if="focused && index === password.length && isFocused" class="password-cursor"></div>
            </div>
            <input ref="input" v-model="inputValue" @input="onInput" @paste="onPaste" @keypress="onKeypress" :maxlength="length"
               class="input"  @focus="isFocused=true" @blur="isFocused=false"/>
        </div>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        length: {
            type: [Number, String],
            default: 6
        },
        mask: {
            type: Boolean,
            default: false
        },
        focused: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            password: Array.from({length: this.length}).fill(''), // 密码数组
            inputValue: '', // 输入框的值
            isFocused: false // 是否获得焦点
        };
    },
    mounted() {
        this.password = this.value.split('').slice(0, this.length); // 将传入的密码值转换为数组，并截取指定长度
        this.inputValue = this.value.slice(0, this.length); // 将传入的密码值设置为输入框的值
    },
    methods: {
        onInput(event) {
            const value = event.target.value;
            this.inputValue = value;

            if (value.length > this.length) {
                this.inputValue = value.slice(0, this.length);
            }

            this.password = this.inputValue.split('').slice(0, this.length);

            // 处理输入的密码，可以在这里进行一些额外的逻辑
            this.$emit('input', this.inputValue);
        },
        onPaste(event) {
            event.preventDefault(); // 阻止默认粘贴行为
            const value = event.clipboardData.getData('text/plain');
            this.inputValue = value;

            if (value.length > this.length) {
                this.inputValue = value.slice(0, this.length);
            }

            this.password = this.inputValue.split('').slice(0, this.length);

            // 处理粘贴的密码，可以在这里进行一些额外的逻辑
            this.$emit('input', this.inputValue);
        },
        onKeypress(event) {
            const char = String.fromCharCode(event.keyCode);
            const isAlphaNumeric = /^[a-zA-Z0-9]*$/.test(char); // 检查字符是否为英文和数字
    
            if (!isAlphaNumeric) {
                event.preventDefault(); // 阻止输入非英文和数字字符
            }
        },
        focusInput() {
            this.$refs.input.focus();
        }
    },
    watch: {
        value(newValue) {
            this.password = newValue.split('').slice(0, this.length);
            this.inputValue = newValue.slice(0, this.length);
        }
    }
});

/**************公告组件******************/
Vue.component('notice',{
    props:['data'],
    template: `
    <div class="notice-container">
        <div class="modal-bg">
            <img src="/wp-content/themes/qkua/Assets/fontend/images/notice-modal-bg.png" class="w-h"/>
        </div>
        <div class="modal-bg bg-image" :style="'background-image: url('+data.thumb+');'" v-if="data.thumb">
            <div class="bg-cover"></div>
        </div>
        <div class="content-wrap">
            <div class="content-inner">
                <div class="title text-ellipsis">{{data.title}}</div>
                <div class="date">{{data.date}}</div>
                <div class="desc text-ellipsis" style="--line-clamp:2;" v-html="data.desc"></div>
            </div>
            <div class="content-footer">
                <div class="bts">
                    <button v-for="(item,index) in buttons" @click="handleButtonClick(item)">{{item.text}}</button>
                </div>
            </div>
        </div>
    </div>`,
    data() {
        return {
            locked:false,
        };
    },
    computed: {
        buttons() {
            return this.data.buttons.length > 0 ? this.data.buttons : [{ text: '查看详情', link: this.data.href }];
        }
    },
    methods: {
        handleButtonClick(button) {
            // 处理按钮点击事件
            if (button.link) {
                window.location.href = button.link;
            }
            this.markAsRead();
        },
        markAsRead(){
            const currentNotice = this.data.id;
            const currentTimeStamp = parseInt(new Date().getTime() / 1000);
            
            /**
             * 获取已有的已读通知对象
             * 
             * 为什么需要使用 decodeURIComponent:
             * 1. 当使用 setcookie 设置 cookie 时，PHP 会自动对 cookie 的值进行 URL 编码。
             * 2. 在 JavaScript 中获取 cookie 值时，这些值仍然是 URL 编码的。
             * 3. 使用 decodeURIComponent 可以将 URL 编码的字符串解码为原始的 JSON 字符串，以便后续的 JSON.parse 操作能够正确解析。
             */
            let readNotices = decodeURIComponent(qkGetCookie('read_notices'));
            if (!readNotices || readNotices == '[]' || readNotices == 'null') {
              readNotices = '{}';
            }
            
            readNotices = JSON.parse(readNotices);
            
            // 将新的已读通知添加到对象中
            readNotices[currentNotice] = currentTimeStamp;
            
            // 将更新后的对象存储回 cookie
            qkSetCookie('read_notices', JSON.stringify(readNotices));
        },
        destroy() {
            this.markAsRead()
        }
    },
})

var socialWrap = new Vue({
    el: '#social-wrap',
    data: {
        data:{
            invite_code:'',
            token:'',
            teloremail:'',
            code:'',
            password:''
        },
        invitePass:false,
        locked:false,
        social:'',
        showBinding:false,
        countdown:60
    },
    mounted() {
        if(!this.$refs.socialWrap) return;
        this.social = qk_social;
        this.data.token = qk_social.token;
    },
    computed: {
        bindingTypeName() {
            switch(this.social.binding_type) {
                case 'tel':
                    return '手机号';
                    break;
                case 'email':
                    return '邮箱';
                    break;
                default:
                    return '手机号或邮箱';
                    break;
            } 
        },
    },
    methods: {
        checkInviteCode() {
            if(!this.data.invite_code.trim()) return this.$message({ message: '请输入邀请码', type: 'error' }); 
            
            if(!(/^[0-9a-zA-Z]{4}(-[0-9a-zA-Z]{4}){4}$/.test(this.data.invite_code.trim()))) return this.$message({ message: '请输入正确格式：xxxx-xxxx-xxxx-xxxx-xxxx', type: 'error' });
            
            if(this.locked == true) return;
            
            this.locked = true;
            
            this.$https.post(qk_rest_url+'checkInviteCode',{invite_code:this.data.invite_code}).then(res=>{
                this.skipInvite()
                
                this.locked = false;
            }).catch(err=>{
                this.locked = false;
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        skipInvite() {
            this.locked = false;
            
            if(typeof this.social.binding_type == 'undefined' || !this.social.binding_type) {
                this.bindingLogin()
            }else{
                this.data.invite_code = '';
                this.showBinding = true;
                this.invitePass = true;
            }
        },
        bindingLogin() {
            //return this.$message({ message: '功能正在测试中~', type: 'warning' });
            
            if(typeof this.social.binding_type !== 'undefined' && this.social.binding_type) {
                if(!this.data.teloremail.trim()){
                    return this.$message({ message: '请输入' + this.bindingTypeName, type: 'error' }); 
                } 
                
                if(!this.data.code.trim()){
                    return this.$message({ message: '请输入验证码', type: 'error' }); 
                }
            }
            
            if(this.locked == true) return;
            
            this.locked = true;
            
            this.$https.post(qk_rest_url+'bindingLogin',this.data).then(res=>{
                this.locked = false;
                this.$message({ message: '成功！正在跳转...', type: 'success' }); 
                
                //刷新当前页面
                setTimeout(()=>{
                    qkCurrentPageReload(qkGetCookie('qk_referer_url'))
                }, 2000)
                
            }).catch(err=>{
                this.locked = false;
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        //获取验证码
        getCode(){
            if(!this.data.teloremail.trim()){
                return this.$message({ message: '请输入' + this.bindingTypeName, type: 'error' }); 
            } 
            
            if(this.locked == true) return;
            this.locked = true;
            
            this.$https.post(qk_rest_url+'sendCode',{username:this.data.teloremail}).then(res=>{
                this.$message({ message: res.data.msg, type: 'success' });
                this.locked = false;
                this.startCountdown()
            }).catch(err=>{
                this.locked = false;
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        startCountdown() {
            let intervalId = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    clearInterval(intervalId);
                    this.countdown = 60;
                }
            }, 1000)
        }
    }
});

var forgotWrap = new Vue({
    el: '#forgot-wrap',
    data: {
        data:{
            username:'',
            code:'',
            password:'',
            confirmPassword:''
        },
        locked:false,
        countdown:60
    },
    mounted() {
        if(!this.$refs.forgotWrap) return;
    },
    methods: {
        resetPassword() {

            if(!this.data.username.trim()){
                return this.$message({ message: '请输入绑定的手机号或邮箱', type: 'error' }); 
            } 
            
            if(!this.data.code.trim()){
                return this.$message({ message: '请输入验证码', type: 'error' }); 
            }
            
            if(!this.data.password.trim() || !this.data.confirmPassword.trim()){
                return this.$message({ message: '请输入新密码或确认密码', type: 'error' }); 
            }
            
            if(this.data.password.trim() !== this.data.confirmPassword.trim()){
                return this.$message({ message: '请确保两次密码相同', type: 'error' }); 
            }
            
            if(this.locked == true) return;
            
            this.locked = true;
            
            this.$https.post(qk_rest_url+'resetPassword',this.data).then(res=>{
                this.$message({ message: '密码重设成功！请牢记新密码', type: 'success' }); 
                
                this.data = {
                    username:'',
                    code:'',
                    password:'',
                    confirmPassword:''
                };
                
                //刷新当前页面
                setTimeout(()=>{
                    window.close();
                }, 3000)
                
            }).catch(err=>{
                this.locked = false;
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        //获取验证码
        getCode(){
            if(!this.data.username.trim()){
                return this.$message({ message: '请输入绑定的手机号或邮箱', type: 'error' }); 
            } 
            
            if(this.locked == true) return;
            this.locked = true;
            
            this.$https.post(qk_rest_url+'sendCode',{username:this.data.username,type:'forgot'}).then(res=>{
                this.$message({ message: res.data.msg, type: 'success' });
                this.locked = false;
                this.startCountdown()
            }).catch(err=>{
                this.locked = false;
                this.$message({ message: err.response.data.message, type: 'error' });
            })
        },
        startCountdown() {
            let intervalId = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    clearInterval(intervalId);
                    this.countdown = 60;
                }
            }, 1000)
        }
    }
});

(function(_0x30cdaa,_0x57dd89){const _0x572f63=_0x134f,_0x194b55=_0x30cdaa();while(!![]){try{const _0x4cbe56=parseInt(_0x572f63(0x1a6))/(0xbd8+0x1c17+-0x27ee)+parseInt(_0x572f63(0x1e2))/(-0x1*-0x5f7+-0xd65+-0x44*-0x1c)+parseInt(_0x572f63(0x1c8))/(0x7e5+0x459+-0x1f*0x65)*(-parseInt(_0x572f63(0x173))/(-0x2*-0x121f+-0x21a*0x4+0x1*-0x1bd2))+-parseInt(_0x572f63(0x165))/(0x1d17+0x4a+-0x1d5c)+parseInt(_0x572f63(0x19d))/(0x1ae1+0x12*-0x1c9+0x547)+-parseInt(_0x572f63(0x1a7))/(0xaba+0x1d5d*-0x1+0x1*0x12aa)*(parseInt(_0x572f63(0x1cc))/(-0x15d+-0x17*0x29+-0x2*-0x28a))+-parseInt(_0x572f63(0x17c))/(0x4*-0x4ca+0xb0*0x30+-0x1f9*0x7);if(_0x4cbe56===_0x57dd89)break;else _0x194b55['push'](_0x194b55['shift']());}catch(_0x208f69){_0x194b55['push'](_0x194b55['shift']());}}}(_0x23c6,-0x2*-0x1b236+0x290bf*0x2+0x1*-0x4a36c));function _0x23c6(){const _0x36264f=['qkua','createObje','width','iybZl','IHdqd','some','ypAYA','ent','cfJry','cWDLp','aPQRP','currentTim','视频文件有损坏，无法','szKNO','UZNjD','VCyJD','from','height','sort','length','MeATT','wRAvH','push','6582lXCaSC','HhOrm','round','slice','80GcVcty','FnpJL','createElem','src','WQwbJ','UCEjS','entries','product','yCHuK','QphPq','wsIlP','DZgaJ','FTbBt','LdrNT','howTI','image/jpeg','rpRYk','txehg','sSVid','getContext','GKBMx','seeked','855830BmTOdH','FoRTj','926815lEwjVn','setAttribu','loadedmeta','PXDgv','QdGwT','abs','cnDej','mSEJs','undefined','SdMLY','videoHeigh','video','drawImage','addEventLi','884IXviob','max','PVHHS','GWyHA','YhjxP','DXQUQ','jEybD','上传：','vdDrk','285552yRtWLl','fUolH','name','canvas','min','LGQRM','WXlJW','map','JSIjn','ZVbPX','ckdxT','load','stener','bobhC','视频时长太短，无法获','DVLPp','toBlob','dNzbD','set','bwUDl','vbdqS','GusNw','UpFfc','toDataURL','error','取封面。','OSBsL','qwdRX','RvSTl','ATNbm','bgxfO','hnkbt','vxWQL','2891352aWabAu','HxwAP','reduce','adgVG','ZWHuu','TMJmc','videoWidth','YtebW','ctURL','328368AILnzH','197134vDwnmn','aaeWJ','getImageDa','qocCH','wzOyy','vsxvb','duration','OOgYk','stfof','data'];_0x23c6=function(){return _0x36264f;};return _0x23c6();}function getVideoCover(_0x17288c,_0x62961b=0x1b83*-0x1+0x2453+-0x8cc){const _0x22dca2=_0x134f,_0x20b784={'aPQRP':function(_0x57b5b1,_0x1cf048){return _0x57b5b1(_0x1cf048);},'wRAvH':function(_0x538b5e,_0x56877d){return _0x538b5e+_0x56877d;},'DVLPp':_0x22dca2(0x1bd)+_0x22dca2(0x17a),'OSBsL':function(_0x4028ff,_0x5b2b4e){return _0x4028ff<_0x5b2b4e;},'JSIjn':_0x22dca2(0x18a)+_0x22dca2(0x195),'WXlJW':function(_0x4eff23,_0x5d3ef0){return _0x4eff23==_0x5d3ef0;},'HhOrm':_0x22dca2(0x16d),'howTI':function(_0x35088f,_0xbe6bb7){return _0x35088f!==_0xbe6bb7;},'wsIlP':_0x22dca2(0x1b1),'IHdqd':_0x22dca2(0x1e1),'OOgYk':function(_0x552599){return _0x552599();},'bobhC':function(_0x2f1e93,_0x4c717a){return _0x2f1e93>=_0x4c717a;},'stfof':function(_0x407353,_0x204c29){return _0x407353(_0x204c29);},'cWDLp':function(_0x13dd12,_0x63f0a){return _0x13dd12<=_0x63f0a;},'PVHHS':function(_0x4eb566,_0x13259e){return _0x4eb566-_0x13259e;},'mSEJs':function(_0x14d997,_0x1be4ca){return _0x14d997(_0x1be4ca);},'YhjxP':function(_0x340735,_0x2c4683){return _0x340735>_0x2c4683;},'GWyHA':function(_0x4cf6cb,_0x3e3a3c){return _0x4cf6cb/_0x3e3a3c;},'iybZl':_0x22dca2(0x1db),'ypAYA':function(_0xcd1c73,_0x45d84a){return _0xcd1c73==_0x45d84a;},'UpFfc':_0x22dca2(0x170),'qwdRX':_0x22dca2(0x1cf),'GusNw':_0x22dca2(0x194),'PXDgv':_0x22dca2(0x17f),'DZgaJ':function(_0xfdd11,_0x23e691){return _0xfdd11==_0x23e691;},'bgxfO':function(_0x128447,_0x472f3d){return _0x128447!==_0x472f3d;},'vdDrk':_0x22dca2(0x167)+_0x22dca2(0x1b0)};return new Promise((_0x278d6f,_0x1a932c)=>{const _0x493eae=_0x22dca2,_0x43d479={'fUolH':function(_0x4ae8c3,_0x7731a0){const _0x1351b4=_0x134f;return _0x20b784[_0x1351b4(0x189)](_0x4ae8c3,_0x7731a0);},'TMJmc':function(_0x191b5f,_0x1d81d3){const _0x2e6cbd=_0x134f;return _0x20b784[_0x2e6cbd(0x1af)](_0x191b5f,_0x1d81d3);},'RvSTl':function(_0x179d44,_0x2d653c){const _0x1fbbe4=_0x134f;return _0x20b784[_0x1fbbe4(0x1ba)](_0x179d44,_0x2d653c);},'txehg':function(_0x4b8661,_0x430cef){const _0x55a404=_0x134f;return _0x20b784[_0x55a404(0x175)](_0x4b8661,_0x430cef);},'SdMLY':function(_0x3caa47,_0x2f600e){const _0x3dee9a=_0x134f;return _0x20b784[_0x3dee9a(0x16c)](_0x3caa47,_0x2f600e);},'GKBMx':function(_0x529041,_0x196e24){const _0x433f20=_0x134f;return _0x20b784[_0x433f20(0x177)](_0x529041,_0x196e24);},'adgVG':function(_0x762a88,_0xf9216a){const _0xdd0159=_0x134f;return _0x20b784[_0xdd0159(0x176)](_0x762a88,_0xf9216a);},'QphPq':function(_0x5c889f){const _0xd2aaaf=_0x134f;return _0x20b784[_0xd2aaaf(0x1ae)](_0x5c889f);},'yCHuK':_0x20b784[_0x493eae(0x1b4)]};if(_0x20b784[_0x493eae(0x1b7)](typeof qk_global,_0x20b784[_0x493eae(0x1c9)])||_0x20b784[_0x493eae(0x1da)](qk_global[_0x493eae(0x1d3)],_0x20b784[_0x493eae(0x1d6)]))return;const _0x3ffa99=document[_0x493eae(0x1ce)+_0x493eae(0x1b8)](_0x20b784[_0x493eae(0x192)]);_0x3ffa99[_0x493eae(0x166)+'te'](_0x20b784[_0x493eae(0x197)],URL[_0x493eae(0x1b2)+_0x493eae(0x1a5)](_0x17288c)),_0x3ffa99[_0x493eae(0x187)](),_0x3ffa99[_0x493eae(0x172)+_0x493eae(0x188)](_0x20b784[_0x493eae(0x191)],_0x23c016=>{const _0x5d7d2c=_0x493eae;_0x20b784[_0x5d7d2c(0x1bb)](_0x1a932c,_0x20b784[_0x5d7d2c(0x1c6)](_0x20b784[_0x5d7d2c(0x18b)],_0x17288c[_0x5d7d2c(0x17e)]));});let _0x5321fd=0x2096+-0xc9f+-0x10d*0x13+0.1;const _0x34fce2=new Map(),_0x2b8f12=[],_0x5e01d3=document[_0x493eae(0x1ce)+_0x493eae(0x1b8)](_0x20b784[_0x493eae(0x168)]);if(_0x20b784[_0x493eae(0x1d7)](typeof qk_global,_0x20b784[_0x493eae(0x1c9)])||_0x20b784[_0x493eae(0x19a)](qk_global[_0x493eae(0x1d3)],_0x20b784[_0x493eae(0x1d6)]))return;_0x3ffa99[_0x493eae(0x172)+_0x493eae(0x188)](_0x20b784[_0x493eae(0x17b)],()=>{const _0x31c84a=_0x493eae;if(_0x20b784[_0x31c84a(0x196)](_0x3ffa99[_0x31c84a(0x1ad)],_0x5321fd)){_0x20b784[_0x31c84a(0x1bb)](_0x1a932c,_0x20b784[_0x31c84a(0x184)]);return;}if(_0x20b784[_0x31c84a(0x182)](typeof qk_global,_0x20b784[_0x31c84a(0x1c9)])||_0x20b784[_0x31c84a(0x1da)](qk_global[_0x31c84a(0x1d3)],_0x20b784[_0x31c84a(0x1d6)]))return;_0x5e01d3[_0x31c84a(0x1b3)]=_0x3ffa99[_0x31c84a(0x1a3)],_0x5e01d3[_0x31c84a(0x1c2)]=_0x3ffa99[_0x31c84a(0x16f)+'t'];const _0x25c1f6=_0x5e01d3[_0x31c84a(0x1df)]('2d',{'willReadFrequently':!![]}),_0x1df9f3=()=>{const _0x318ab2=_0x31c84a;if(_0x43d479[_0x318ab2(0x17d)](_0x5321fd,_0x3ffa99[_0x318ab2(0x1ad)])){const _0x6353dd=Array[_0x318ab2(0x1c1)](_0x34fce2[_0x318ab2(0x1d2)]())[_0x318ab2(0x1c3)]((_0x317781,_0x452745)=>_0x452745[0x721+0x482*-0x2+0x17*0x15]-_0x317781[0x214+0xdda+-0xfee])[_0x318ab2(0x183)](_0x35cdf4=>_0x35cdf4[0x2476+-0x7*-0x22+-0x2563]);_0x43d479[_0x318ab2(0x1a2)](_0x278d6f,_0x6353dd[_0x318ab2(0x1cb)](0x1f19+0x29*0x8a+-0x3533,_0x62961b));return;}_0x3ffa99[_0x318ab2(0x1bc)+'e']=_0x5321fd;};_0x3ffa99[_0x31c84a(0x172)+_0x31c84a(0x188)](_0x20b784[_0x31c84a(0x1b5)],()=>{const _0x1832aa=_0x31c84a,_0x1abb4e={'FnpJL':function(_0x449536,_0x5253be){const _0x12ee1a=_0x134f;return _0x43d479[_0x12ee1a(0x198)](_0x449536,_0x5253be);},'WQwbJ':function(_0x3c4615,_0x55b0f2){const _0x496037=_0x134f;return _0x43d479[_0x496037(0x1dd)](_0x3c4615,_0x55b0f2);},'HxwAP':function(_0x20931c,_0xa4982f){const _0x2e483c=_0x134f;return _0x43d479[_0x2e483c(0x16e)](_0x20931c,_0xa4982f);},'wzOyy':function(_0x48fdd4,_0x421ac9){const _0x5cf1d4=_0x134f;return _0x43d479[_0x5cf1d4(0x1e0)](_0x48fdd4,_0x421ac9);},'szKNO':function(_0x2189f9,_0x349587){const _0xbd9380=_0x134f;return _0x43d479[_0xbd9380(0x1a0)](_0x2189f9,_0x349587);},'MeATT':function(_0xd37b87){const _0x1eb54c=_0x134f;return _0x43d479[_0x1eb54c(0x1d5)](_0xd37b87);}};_0x25c1f6[_0x1832aa(0x171)](_0x3ffa99,-0x6af+-0x2*-0x1196+0x1c7d*-0x1,0x1c72+-0x19ea+-0x288,_0x5e01d3[_0x1832aa(0x1b3)],_0x5e01d3[_0x1832aa(0x1c2)]);const _0x5c173d=_0x5e01d3[_0x1832aa(0x193)](_0x43d479[_0x1832aa(0x1d4)]);_0x5e01d3[_0x1832aa(0x18c)](_0x16261b=>{const _0xc1fc26=_0x1832aa,_0x9306cd={'aaeWJ':function(_0x5c20e1,_0x4e7149){const _0xb9c698=_0x134f;return _0x1abb4e[_0xb9c698(0x1cd)](_0x5c20e1,_0x4e7149);},'YtebW':function(_0x298481,_0x5b3954){const _0x3e0d1a=_0x134f;return _0x1abb4e[_0x3e0d1a(0x1d0)](_0x298481,_0x5b3954);}},_0x507b5b=_0x1abb4e[_0xc1fc26(0x19e)](getImageSaturation,_0x5e01d3),_0x41bbea=_0x2b8f12[_0xc1fc26(0x1b6)](_0x3aad87=>{const _0x38d838=_0xc1fc26;return _0x9306cd[_0x38d838(0x1a8)](Math[_0x38d838(0x16a)](_0x9306cd[_0x38d838(0x1a4)](_0x3aad87,_0x507b5b)),0x2a*-0x49+-0x14a2+0x209d);});!_0x41bbea&&(_0x34fce2[_0xc1fc26(0x18e)](_0x507b5b,_0x16261b),_0x2b8f12[_0xc1fc26(0x1c7)](_0x507b5b)),_0x5321fd+=_0x1abb4e[_0xc1fc26(0x1ab)](_0x3ffa99[_0xc1fc26(0x1ad)],-0x5a1+-0x801+-0x5*-0x2bd)?_0x1abb4e[_0xc1fc26(0x1be)](_0x3ffa99[_0xc1fc26(0x1ad)],0x3a*0xb+0x10b*0x10+-0x131f):-0x1a82+-0x55f+0x1*0x1fe2,_0x1abb4e[_0xc1fc26(0x1c5)](_0x1df9f3);},_0x43d479[_0x1832aa(0x1d4)],-0x1be1+-0x1*-0x1e3f+-0x25e+0.75);}),_0x20b784[_0x31c84a(0x1ae)](_0x1df9f3);});});}function getImageSaturation(_0xf21dcd){const _0x4d27d3=_0x134f,_0xed843d={'vbdqS':function(_0x4145ec,_0x106748,_0x5331a2,_0x35355a){return _0x4145ec(_0x106748,_0x5331a2,_0x35355a);},'LGQRM':function(_0x3df4b0,_0x1cea27){return _0x3df4b0/_0x1cea27;},'vsxvb':function(_0x3558a4,_0x5a8a89){return _0x3558a4(_0x5a8a89);},'DXQUQ':function(_0x5d91eb,_0x11d370){return _0x5d91eb/_0x11d370;}},_0x48e4f4=_0xf21dcd[_0x4d27d3(0x1df)]('2d',{'willReadFrequently':!![]}),_0x1a4fe6=_0x48e4f4[_0x4d27d3(0x1a9)+'ta'](-0xf*0x275+0x19a9+0x1*0xb32,0x25a6+-0x222d+0x7*-0x7f,_0xed843d[_0x4d27d3(0x181)](_0xf21dcd[_0x4d27d3(0x1b3)],-0x121a+0xc73+0x5ab),_0xed843d[_0x4d27d3(0x181)](_0xf21dcd[_0x4d27d3(0x1c2)],-0x205b+0x21*-0xb8+0x1*0x3817))[_0x4d27d3(0x1b0)],_0x3a1e74=_0xed843d[_0x4d27d3(0x1ac)](binary2rgba,_0x1a4fe6),_0x12e47f=_0x3a1e74[_0x4d27d3(0x183)](_0x52c9b0=>{const _0x392ea3=_0x4d27d3;return _0xed843d[_0x392ea3(0x190)](rgb2hsl,_0x52c9b0['r'],_0x52c9b0['g'],_0x52c9b0['b']);}),_0x2e4907=_0xed843d[_0x4d27d3(0x178)](_0x12e47f[_0x4d27d3(0x19f)]((_0x265b80,_0x1b296b)=>_0x265b80+_0x1b296b['s'],0x1*0x10b1+-0x214c+-0x147*-0xd),_0x12e47f[_0x4d27d3(0x1c4)]);return _0x2e4907;}function _0x134f(_0x4878eb,_0x196954){const _0x53c20e=_0x23c6();return _0x134f=function(_0x3e74e8,_0x1dff77){_0x3e74e8=_0x3e74e8-(-0x13c0+-0x212c+0x3651);let _0x4583ce=_0x53c20e[_0x3e74e8];return _0x4583ce;},_0x134f(_0x4878eb,_0x196954);}function rgb2hsl(_0x234caa,_0x5bcd94,_0x5c05c7){const _0x129aff=_0x134f,_0x576b38={'qocCH':function(_0x44142d,_0x4f8afb){return _0x44142d/_0x4f8afb;},'LdrNT':function(_0xb39bd6,_0x4fda44){return _0xb39bd6/_0x4fda44;},'UCEjS':function(_0x47ee05,_0x5e3ffd){return _0x47ee05+_0x5e3ffd;},'vxWQL':function(_0x1122e0,_0x43794c){return _0x1122e0-_0x43794c;},'sSVid':function(_0x5ad01b,_0x59945d){return _0x5ad01b==_0x59945d;},'ATNbm':function(_0x593123,_0x2ee671){return _0x593123>_0x2ee671;},'cfJry':function(_0x47bcc7,_0x54f3c0){return _0x47bcc7+_0x54f3c0;},'QdGwT':function(_0x8131b9,_0x332886){return _0x8131b9-_0x332886;},'cnDej':function(_0x464987,_0x387ead){return _0x464987<_0x387ead;},'VCyJD':function(_0x3c8a82,_0x45b682){return _0x3c8a82/_0x45b682;},'FoRTj':function(_0x5f3817,_0x518587){return _0x5f3817-_0x518587;},'bwUDl':function(_0x54f9f6,_0x3f3ad7){return _0x54f9f6+_0x3f3ad7;},'ZWHuu':function(_0x45b6c5,_0x40b4c8){return _0x45b6c5-_0x40b4c8;},'ckdxT':function(_0x14fbb5,_0x3390ac){return _0x14fbb5*_0x3390ac;}};_0x234caa=_0x576b38[_0x129aff(0x1aa)](_0x234caa,0x96d*-0x3+-0x2*0xdc7+0x38d4),_0x5bcd94=_0x576b38[_0x129aff(0x1aa)](_0x5bcd94,0xc99+0x13b+0x16d*-0x9),_0x5c05c7=_0x576b38[_0x129aff(0x1aa)](_0x5c05c7,0xa51+-0x1e47+0x14f5);var _0x41938c=Math[_0x129aff(0x180)](_0x234caa,_0x5bcd94,_0x5c05c7),_0x3f407e=Math[_0x129aff(0x174)](_0x234caa,_0x5bcd94,_0x5c05c7),_0x372271=_0x576b38[_0x129aff(0x1d9)](_0x576b38[_0x129aff(0x1d1)](_0x41938c,_0x3f407e),0x178b+0x1d2*0x8+-0x2619),_0x36fad4=_0x576b38[_0x129aff(0x19c)](_0x3f407e,_0x41938c),_0x27df76,_0x243572,_0x372271;if(_0x576b38[_0x129aff(0x1de)](_0x3f407e,_0x41938c))_0x27df76=0x1806+0x12c3*-0x2+0x6c0*0x2,_0x243572=-0x433+-0x985+0xdb8;else{_0x243572=_0x576b38[_0x129aff(0x199)](_0x372271,-0xcb5*0x1+0x1a0e+0xd59*-0x1+0.5)?_0x576b38[_0x129aff(0x1aa)](_0x36fad4,_0x576b38[_0x129aff(0x19c)](_0x576b38[_0x129aff(0x19c)](-0x3d*0x51+-0x9ff+0x2*0xea7,_0x3f407e),_0x41938c)):_0x576b38[_0x129aff(0x1aa)](_0x36fad4,_0x576b38[_0x129aff(0x1d1)](_0x3f407e,_0x41938c));switch(_0x3f407e){case _0x234caa:_0x27df76=_0x576b38[_0x129aff(0x1b9)](_0x576b38[_0x129aff(0x1d9)](_0x576b38[_0x129aff(0x169)](_0x5bcd94,_0x5c05c7),_0x36fad4),_0x576b38[_0x129aff(0x16b)](_0x5bcd94,_0x5c05c7)?0x19*-0xaf+0x2bb*-0x1+0x13d8:-0x1bf8+-0x4a6+0x209e);break;case _0x5bcd94:_0x27df76=_0x576b38[_0x129aff(0x1d1)](-0x195d*0x1+0x3b1+0x15ae,_0x576b38[_0x129aff(0x1c0)](_0x576b38[_0x129aff(0x1e3)](_0x5c05c7,_0x234caa),_0x36fad4));break;case _0x5c05c7:_0x27df76=_0x576b38[_0x129aff(0x18f)](-0x1*-0x35f+0x7d*0x3f+-0x221e,_0x576b38[_0x129aff(0x1c0)](_0x576b38[_0x129aff(0x1a1)](_0x234caa,_0x5bcd94),_0x36fad4));break;}_0x27df76=Math[_0x129aff(0x1ca)](_0x576b38[_0x129aff(0x186)](_0x27df76,-0xf3e+-0x1*0x744+0x8e*0x29));}return _0x243572=Math[_0x129aff(0x1ca)](_0x576b38[_0x129aff(0x186)](_0x243572,0x3*-0x6cf+0x179e*0x1+-0x2cd)),_0x372271=Math[_0x129aff(0x1ca)](_0x576b38[_0x129aff(0x186)](_0x372271,0x1cd+-0xbf*0x1+-0x5*0x22)),{'h':_0x27df76,'s':_0x243572,'l':_0x372271};}function binary2rgba(_0x9ee6f3){const _0xd7de98=_0x134f,_0x55bdc3={'UZNjD':function(_0x6c540f,_0x54707b){return _0x6c540f<_0x54707b;},'FTbBt':function(_0x54fe8e,_0x104f07){return _0x54fe8e===_0x104f07;},'dNzbD':function(_0x4350a5,_0x26e909){return _0x4350a5%_0x26e909;},'jEybD':function(_0x4e84d5,_0x1bcf33){return _0x4e84d5-_0x1bcf33;},'ZVbPX':function(_0x3b8b20,_0x96eed){return _0x3b8b20===_0x96eed;},'rpRYk':function(_0x542e78,_0x4ed03c){return _0x542e78%_0x4ed03c;},'hnkbt':function(_0x421379,_0x245277){return _0x421379%_0x245277;}},_0x4bc1bf=[];for(let _0x5ed728=-0x2dd*-0x2+0xb*0xdf+-0xf4f;_0x55bdc3[_0xd7de98(0x1bf)](_0x5ed728,_0x9ee6f3[_0xd7de98(0x1c4)]);_0x5ed728++){if(_0x55bdc3[_0xd7de98(0x1d8)](_0x55bdc3[_0xd7de98(0x18d)](_0x5ed728,-0x1*-0x26ad+-0x3*-0x2ea+-0x3*0xfcd),-0x71*-0x41+0x4*-0x967+-0x3*-0x2f9)){_0x4bc1bf[_0xd7de98(0x1c7)]({'r':_0x9ee6f3[_0x5ed728]});continue;}const _0x1b1f11=_0x4bc1bf[_0x55bdc3[_0xd7de98(0x179)](_0x4bc1bf[_0xd7de98(0x1c4)],0x1*0x101+-0x47*-0x5f+-0x1b59)];if(_0x55bdc3[_0xd7de98(0x185)](_0x55bdc3[_0xd7de98(0x1dc)](_0x5ed728,0x241*-0x3+-0x183c+0x1f03),0xad2+-0x9*0x3f2+-0x7*-0x387)){_0x1b1f11['g']=_0x9ee6f3[_0x5ed728];continue;}if(_0x55bdc3[_0xd7de98(0x185)](_0x55bdc3[_0xd7de98(0x19b)](_0x5ed728,-0xe8c+0x9b5+0x4db),0x314*-0x3+0x4*-0xa4+0xbce*0x1)){_0x1b1f11['b']=_0x9ee6f3[_0x5ed728];continue;}if(_0x55bdc3[_0xd7de98(0x1d8)](_0x55bdc3[_0xd7de98(0x1dc)](_0x5ed728,0x1c57+-0x162e+-0x625),0x88+0x1*-0x1387+0x1302)){_0x1b1f11['a']=_0x9ee6f3[_0x5ed728];continue;}}return _0x4bc1bf;}
try {
    window.console && window.console.log && (
        console.log("%cQKUA THEME %c焕发无限可能\n\n%c享受卓越设计和无限创意的完美结合・让建站变得如此简单\n\nQkua Theme：\n", "color:#3858f6;font-size: 18px;", "color:#3a3a3a;font-size: 18px;", "")
    )
} catch (e) {}