import{_ as m}from"./index-Ch5822Og.js";import{r as g,c as x,x as i,z as a,L as b,O as e,Q as u,ac as p,aY as l,P as d,y as c,C as v}from"./vue-vendor-CKToUHZx.js";import"./element-plus-B8Fs_0jW.js";import"./vendor-BDiMbLwQ.js";const y={class:"card p-6"},f={class:"mb-8"},h={class:"flex flex-wrap gap-2 p-2 bg-gray-100 rounded-xl"},C=["onClick"],w={key:0,class:"tutorial-content"},A={class:"mb-10"},T={class:"bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-6 border border-orange-100 mb-6"},P={class:"space-y-4"},N={class:"bg-white rounded-lg p-4 border border-orange-200"},S={class:"bg-gray-900 text-green-400 p-3 rounded font-mono text-sm"},H={class:"text-gray-300"},O={class:"bg-white rounded-lg p-4 border border-orange-200"},I={class:"mt-3 space-y-2"},R={class:"bg-gray-100 p-2 rounded text-sm"},B={class:"font-mono"},U={class:"bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6"},j={class:"mt-3 space-y-2"},E={class:"bg-gray-100 p-2 rounded text-sm font-mono"},L={key:1,class:"tutorial-content"},_={class:"mb-10"},q={class:"bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-6 border border-orange-100 mb-6"},k={class:"space-y-4"},G={class:"bg-white rounded-lg p-4 border border-orange-200"},K={class:"bg-gray-900 text-green-400 p-3 rounded font-mono text-sm"},z={class:"text-gray-300"},D={class:"bg-white rounded-lg p-4 border border-orange-200"},W={class:"bg-gray-900 text-green-400 p-3 rounded font-mono text-sm mb-3"},$={class:"text-gray-300"},M={class:"bg-gray-900 text-green-400 p-3 rounded font-mono text-sm"},V={class:"text-gray-300"},F={key:2,class:"tutorial-content"},Q={class:"mb-10"},Y={class:"bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-6 border border-orange-100 mb-6"},J={class:"space-y-4"},X={class:"bg-white rounded-lg p-4 border border-orange-200"},Z={class:"bg-gray-900 text-green-400 p-3 rounded font-mono text-sm"},tt={class:"text-gray-300"},at={class:"bg-white rounded-lg p-4 border border-orange-200"},et={class:"bg-gray-900 text-green-400 p-3 rounded font-mono text-sm mb-3"},dt={class:"text-gray-300"},st={class:"bg-gray-900 text-green-400 p-3 rounded font-mono text-sm"},lt={class:"text-gray-300"},rt={__name:"TutorialView",setup(ot){const r=g("windows"),n=[{key:"windows",name:"Windows",icon:"fab fa-windows"},{key:"macos",name:"macOS",icon:"fab fa-apple"},{key:"linux",name:"Linux / WSL2",icon:"fab fa-linux"}];return x(()=>window.location.origin),(s,t)=>(c(),i("div",y,[t[55]||(t[55]=a("div",{class:"mb-8"},[a("h3",{class:"text-2xl font-bold text-gray-900 mb-4 flex items-center"},[a("i",{class:"fas fa-graduation-cap text-blue-600 mr-3"}),e(" Claude Code 使用教程 ")]),a("p",{class:"text-gray-600 text-lg"}," 跟着这个教程，你可以轻松在自己的电脑上安装并使用 Claude Code。 ")],-1)),a("div",f,[a("div",h,[(c(),i(u,null,p(n,o=>a("button",{key:o.key,class:v(["flex-1 py-3 px-6 text-sm font-semibold rounded-lg transition-all duration-300 flex items-center justify-center gap-2",r.value===o.key?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:bg-white/50 hover:text-gray-900"]),onClick:it=>r.value=o.key},[a("i",{class:v(o.icon)},null,2),e(" "+d(o.name),1)],10,C)),64))])]),r.value==="windows"?(c(),i("div",w,[t[19]||(t[19]=l('<div class="mb-10" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>1</span> 安装 Node.js 环境 </h4><p class="text-gray-600 mb-6" data-v-508c8654> Claude Code 需要 Node.js 环境才能运行。 </p><div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 mb-6" data-v-508c8654><h5 class="text-lg font-semibold text-gray-800 mb-3 flex items-center" data-v-508c8654><i class="fab fa-windows text-blue-600 mr-2" data-v-508c8654></i> Windows 安装方法 </h5><div class="mb-4" data-v-508c8654><p class="text-gray-700 mb-3" data-v-508c8654> 方法一：官网下载（推荐） </p><ol class="list-decimal list-inside text-gray-600 space-y-2 ml-4" data-v-508c8654><li data-v-508c8654>打开浏览器访问 <code class="bg-gray-100 px-2 py-1 rounded text-sm" data-v-508c8654>https://nodejs.org/</code></li><li data-v-508c8654>点击 &quot;LTS&quot; 版本进行下载（推荐长期支持版本）</li><li data-v-508c8654>下载完成后双击 <code class="bg-gray-100 px-2 py-1 rounded text-sm" data-v-508c8654>.msi</code> 文件</li><li data-v-508c8654>按照安装向导完成安装，保持默认设置即可</li></ol></div><div class="mb-4" data-v-508c8654><p class="text-gray-700 mb-3" data-v-508c8654> 方法二：使用包管理器 </p><p class="text-gray-600 mb-2" data-v-508c8654> 如果你安装了 Chocolatey 或 Scoop，可以使用命令行安装： </p><div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm" data-v-508c8654><div class="mb-2" data-v-508c8654> # 使用 Chocolatey </div><div class="text-gray-300" data-v-508c8654> choco install nodejs </div><div class="mt-3 mb-2" data-v-508c8654> # 或使用 Scoop </div><div class="text-gray-300" data-v-508c8654> scoop install nodejs </div></div></div><div class="bg-blue-50 border border-blue-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-blue-800 mb-2" data-v-508c8654> Windows 注意事项 </h6><ul class="text-blue-700 text-sm space-y-1" data-v-508c8654><li data-v-508c8654>• 建议使用 PowerShell 而不是 CMD</li><li data-v-508c8654>• 如果遇到权限问题，尝试以管理员身份运行</li><li data-v-508c8654>• 某些杀毒软件可能会误报，需要添加白名单</li></ul></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-green-800 mb-2" data-v-508c8654> 验证安装是否成功 </h6><p class="text-green-700 text-sm mb-3" data-v-508c8654> 安装完成后，打开 PowerShell 或 CMD，输入以下命令： </p><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> node --version </div><div class="text-gray-300" data-v-508c8654> npm --version </div></div><p class="text-green-700 text-sm mt-2" data-v-508c8654> 如果显示版本号，说明安装成功了！ </p></div></div><div class="mb-10" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>2</span> 安装 Git Bash </h4><p class="text-gray-600 mb-6" data-v-508c8654> Windows 环境下需要使用 Git Bash 安装Claude code。安装完成后，环境变量设置和使用 Claude Code 仍然在普通的 PowerShell 或 CMD 中进行。 </p><div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100 mb-6" data-v-508c8654><h5 class="text-lg font-semibold text-gray-800 mb-3 flex items-center" data-v-508c8654><i class="fab fa-git-alt text-green-600 mr-2" data-v-508c8654></i> 下载并安装 Git for Windows </h5><ol class="list-decimal list-inside text-gray-600 space-y-2 ml-4 mb-4" data-v-508c8654><li data-v-508c8654>访问 <code class="bg-gray-100 px-2 py-1 rounded text-sm" data-v-508c8654>https://git-scm.com/downloads/win</code></li><li data-v-508c8654>点击 &quot;Download for Windows&quot; 下载安装包</li><li data-v-508c8654>运行下载的 <code class="bg-gray-100 px-2 py-1 rounded text-sm" data-v-508c8654>.exe</code> 安装文件</li><li data-v-508c8654>在安装过程中保持默认设置，直接点击 &quot;Next&quot; 完成安装</li></ol><div class="bg-green-50 border border-green-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-green-800 mb-2" data-v-508c8654> 安装完成后 </h6><ul class="text-green-700 text-sm space-y-1" data-v-508c8654><li data-v-508c8654>• 在任意文件夹右键可以看到 &quot;Git Bash Here&quot; 选项</li><li data-v-508c8654>• 也可以从开始菜单启动 &quot;Git Bash&quot;</li><li data-v-508c8654>• 只需要在 Git Bash 中运行 npm install 命令</li><li data-v-508c8654>• 后续的环境变量设置和使用都在 PowerShell/CMD 中</li></ul></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-green-800 mb-2" data-v-508c8654> 验证 Git Bash 安装 </h6><p class="text-green-700 text-sm mb-3" data-v-508c8654> 打开 Git Bash，输入以下命令验证： </p><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> git --version </div></div><p class="text-green-700 text-sm mt-2" data-v-508c8654> 如果显示 Git 版本号，说明安装成功！ </p></div></div><div class="mb-10" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>3</span> 安装 Claude Code </h4><div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100 mb-6" data-v-508c8654><h5 class="text-lg font-semibold text-gray-800 mb-3 flex items-center" data-v-508c8654><i class="fas fa-download text-purple-600 mr-2" data-v-508c8654></i> 安装 Claude Code </h5><p class="text-gray-700 mb-4" data-v-508c8654> 打开 Git Bash（重要：不要使用 PowerShell），运行以下命令： </p><div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm mb-4" data-v-508c8654><div class="mb-2" data-v-508c8654> # 在 Git Bash 中全局安装 Claude Code </div><div class="text-gray-300" data-v-508c8654> npm install -g @anthropic-ai/claude-code </div></div><p class="text-gray-600 text-sm" data-v-508c8654> 这个命令会从 npm 官方仓库下载并安装最新版本的 Claude Code。 </p><div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4" data-v-508c8654><h6 class="font-medium text-yellow-800 mb-2" data-v-508c8654> 重要提醒 </h6><ul class="text-yellow-700 text-sm space-y-1" data-v-508c8654><li data-v-508c8654>• 必须在 Git Bash 中运行，不要在 PowerShell 中运行</li><li data-v-508c8654>• 如果遇到权限问题，可以尝试在 Git Bash 中使用 sudo 命令</li></ul></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-green-800 mb-2" data-v-508c8654> 验证 Claude Code 安装 </h6><p class="text-green-700 text-sm mb-3" data-v-508c8654> 安装完成后，输入以下命令检查是否安装成功： </p><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> claude --version </div></div><p class="text-green-700 text-sm mt-2" data-v-508c8654> 如果显示版本号，恭喜你！Claude Code 已经成功安装了。 </p></div></div>',3)),a("div",A,[t[18]||(t[18]=a("h4",{class:"text-xl font-semibold text-gray-800 mb-4 flex items-center"},[a("span",{class:"w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3"},"4"),e(" 设置环境变量 ")],-1)),a("div",T,[t[12]||(t[12]=a("h5",{class:"text-lg font-semibold text-gray-800 mb-3 flex items-center"},[a("i",{class:"fas fa-cog text-orange-600 mr-2"}),e(" 配置 Claude Code 环境变量 ")],-1)),t[13]||(t[13]=a("p",{class:"text-gray-700 mb-4"}," 为了让 Claude Code 连接到你的中转服务，需要设置两个环境变量： ",-1)),a("div",P,[a("div",N,[t[1]||(t[1]=a("h6",{class:"font-medium text-gray-800 mb-2"}," 方法一：PowerShell 临时设置（推荐） ",-1)),t[2]||(t[2]=a("p",{class:"text-gray-600 text-sm mb-3"}," 在 PowerShell 中运行以下命令： ",-1)),a("div",S,[a("div",H,' $env:ANTHROPIC_BASE_URL = "'+d(s.currentBaseUrl)+'" ',1),t[0]||(t[0]=a("div",{class:"text-gray-300"},' $env:ANTHROPIC_AUTH_TOKEN = "你的API密钥" ',-1))]),t[3]||(t[3]=a("p",{class:"text-yellow-700 text-xs mt-2"},' 💡 记得将 "你的API密钥" 替换为在上方 "API Keys" 标签页中创建的实际密钥。 ',-1))]),a("div",O,[t[10]||(t[10]=a("h6",{class:"font-medium text-gray-800 mb-2"}," 方法二：系统环境变量（永久设置） ",-1)),t[11]||(t[11]=a("ol",{class:"text-gray-600 text-sm space-y-1 list-decimal list-inside"},[a("li",null,'右键"此电脑" → "属性" → "高级系统设置"'),a("li",null,'点击"环境变量"按钮'),a("li",null,'在"用户变量"或"系统变量"中点击"新建"'),a("li",null,"添加以下两个变量：")],-1)),a("div",I,[a("div",R,[t[4]||(t[4]=a("strong",null,"变量名：",-1)),t[5]||(t[5]=e(" ANTHROPIC_BASE_URL",-1)),t[6]||(t[6]=a("br",null,null,-1)),t[7]||(t[7]=a("strong",null,"变量值：",-1)),t[8]||(t[8]=e()),a("span",B,d(s.currentBaseUrl),1)]),t[9]||(t[9]=a("div",{class:"bg-gray-100 p-2 rounded text-sm"},[a("strong",null,"变量名："),e(" ANTHROPIC_AUTH_TOKEN"),a("br"),a("strong",null,"变量值："),e(),a("span",{class:"font-mono"},"你的API密钥")],-1))])])])]),a("div",U,[t[17]||(t[17]=l('<h6 class="font-medium text-blue-800 mb-2" data-v-508c8654> 验证环境变量设置 </h6><p class="text-blue-700 text-sm mb-3" data-v-508c8654> 设置完环境变量后，可以通过以下命令验证是否设置成功： </p><div class="space-y-4" data-v-508c8654><div data-v-508c8654><h6 class="font-medium text-gray-800 mb-2" data-v-508c8654> 在 PowerShell 中验证： </h6><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm space-y-1" data-v-508c8654><div class="text-gray-300" data-v-508c8654> echo $env:ANTHROPIC_BASE_URL </div><div class="text-gray-300" data-v-508c8654> echo $env:ANTHROPIC_AUTH_TOKEN </div></div></div><div data-v-508c8654><h6 class="font-medium text-gray-800 mb-2" data-v-508c8654> 在 CMD 中验证： </h6><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm space-y-1" data-v-508c8654><div class="text-gray-300" data-v-508c8654> echo %ANTHROPIC_BASE_URL% </div><div class="text-gray-300" data-v-508c8654> echo %ANTHROPIC_AUTH_TOKEN% </div></div></div></div>',3)),a("div",j,[t[15]||(t[15]=a("p",{class:"text-blue-700 text-sm"},[a("strong",null,"预期输出示例：")],-1)),a("div",E,[a("div",null,d(s.currentBaseUrl),1),t[14]||(t[14]=a("div",null,"cr_xxxxxxxxxxxxxxxxxx",-1))]),t[16]||(t[16]=a("p",{class:"text-blue-700 text-xs"}," 💡 如果输出为空或显示变量名本身，说明环境变量设置失败，请重新设置。 ",-1))])])]),t[20]||(t[20]=l('<div class="mb-8" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>5</span> 开始使用 Claude Code </h4><div class="bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl p-6 border border-yellow-100" data-v-508c8654><p class="text-gray-700 mb-4" data-v-508c8654> 现在你可以开始使用 Claude Code 了！ </p><div class="space-y-4" data-v-508c8654><div data-v-508c8654><h6 class="font-medium text-gray-800 mb-2" data-v-508c8654> 启动 Claude Code </h6><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> claude </div></div></div><div data-v-508c8654><h6 class="font-medium text-gray-800 mb-2" data-v-508c8654> 在特定项目中使用 </h6><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="mb-2" data-v-508c8654> # 进入你的项目目录 </div><div class="text-gray-300" data-v-508c8654> cd C:\\path\\to\\your\\project </div><div class="mt-2 mb-2" data-v-508c8654> # 启动 Claude Code </div><div class="text-gray-300" data-v-508c8654> claude </div></div></div></div></div></div><div class="mb-8" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><i class="fas fa-wrench text-red-600 mr-3" data-v-508c8654></i> Windows 常见问题解决 </h4><div class="space-y-4" data-v-508c8654><details class="bg-gray-50 rounded-lg border border-gray-200" data-v-508c8654><summary class="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100" data-v-508c8654> 安装时提示 &quot;permission denied&quot; 错误 </summary><div class="px-4 pb-4 text-gray-600" data-v-508c8654><p class="mb-2" data-v-508c8654> 这通常是权限问题，尝试以下解决方法： </p><ul class="list-disc list-inside space-y-1 text-sm" data-v-508c8654><li data-v-508c8654>以管理员身份运行 PowerShell</li><li data-v-508c8654>或者配置 npm 使用用户目录：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>npm config set prefix %APPDATA%\\npm</code></li></ul></div></details><details class="bg-gray-50 rounded-lg border border-gray-200" data-v-508c8654><summary class="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100" data-v-508c8654> PowerShell 执行策略错误 </summary><div class="px-4 pb-4 text-gray-600" data-v-508c8654><p class="mb-2" data-v-508c8654> 如果遇到执行策略限制，运行： </p><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser </div></div></div></details><details class="bg-gray-50 rounded-lg border border-gray-200" data-v-508c8654><summary class="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100" data-v-508c8654> 环境变量设置后不生效 </summary><div class="px-4 pb-4 text-gray-600" data-v-508c8654><p class="mb-2" data-v-508c8654> 设置永久环境变量后需要： </p><ul class="list-disc list-inside space-y-1 text-sm" data-v-508c8654><li data-v-508c8654>重新启动 PowerShell 或 CMD</li><li data-v-508c8654>或者注销并重新登录 Windows</li><li data-v-508c8654>验证设置：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>echo $env:ANTHROPIC_BASE_URL</code></li></ul></div></details></div></div>',2))])):r.value==="macos"?(c(),i("div",L,[t[36]||(t[36]=l('<div class="mb-10" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>1</span> 安装 Node.js 环境 </h4><p class="text-gray-600 mb-6" data-v-508c8654> Claude Code 需要 Node.js 环境才能运行。 </p><div class="bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 border border-gray-200 mb-6" data-v-508c8654><h5 class="text-lg font-semibold text-gray-800 mb-3 flex items-center" data-v-508c8654><i class="fab fa-apple text-gray-700 mr-2" data-v-508c8654></i> macOS 安装方法 </h5><div class="mb-4" data-v-508c8654><p class="text-gray-700 mb-3" data-v-508c8654> 方法一：使用 Homebrew（推荐） </p><p class="text-gray-600 mb-2" data-v-508c8654> 如果你已经安装了 Homebrew，使用它安装 Node.js 会更方便： </p><div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm" data-v-508c8654><div class="mb-2" data-v-508c8654> # 更新 Homebrew </div><div class="text-gray-300" data-v-508c8654> brew update </div><div class="mt-3 mb-2" data-v-508c8654> # 安装 Node.js </div><div class="text-gray-300" data-v-508c8654> brew install node </div></div></div><div class="mb-4" data-v-508c8654><p class="text-gray-700 mb-3" data-v-508c8654> 方法二：官网下载 </p><ol class="list-decimal list-inside text-gray-600 space-y-2 ml-4" data-v-508c8654><li data-v-508c8654>访问 <code class="bg-gray-100 px-2 py-1 rounded text-sm" data-v-508c8654>https://nodejs.org/</code></li><li data-v-508c8654>下载适合 macOS 的 LTS 版本</li><li data-v-508c8654>打开下载的 <code class="bg-gray-100 px-2 py-1 rounded text-sm" data-v-508c8654>.pkg</code> 文件</li><li data-v-508c8654>按照安装程序指引完成安装</li></ol></div><div class="bg-gray-50 border border-gray-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-gray-800 mb-2" data-v-508c8654> macOS 注意事项 </h6><ul class="text-gray-700 text-sm space-y-1" data-v-508c8654><li data-v-508c8654>• 如果遇到权限问题，可能需要使用 <code class="bg-gray-200 px-1 rounded" data-v-508c8654>sudo</code></li><li data-v-508c8654>• 首次运行可能需要在系统偏好设置中允许</li><li data-v-508c8654>• 建议使用 Terminal 或 iTerm2</li></ul></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-green-800 mb-2" data-v-508c8654> 验证安装是否成功 </h6><p class="text-green-700 text-sm mb-3" data-v-508c8654> 安装完成后，打开 Terminal，输入以下命令： </p><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> node --version </div><div class="text-gray-300" data-v-508c8654> npm --version </div></div><p class="text-green-700 text-sm mt-2" data-v-508c8654> 如果显示版本号，说明安装成功了！ </p></div></div><div class="mb-10" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>2</span> 安装 Claude Code </h4><div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100 mb-6" data-v-508c8654><h5 class="text-lg font-semibold text-gray-800 mb-3 flex items-center" data-v-508c8654><i class="fas fa-download text-purple-600 mr-2" data-v-508c8654></i> 安装 Claude Code </h5><p class="text-gray-700 mb-4" data-v-508c8654> 打开 Terminal，运行以下命令： </p><div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm mb-4" data-v-508c8654><div class="mb-2" data-v-508c8654> # 全局安装 Claude Code </div><div class="text-gray-300" data-v-508c8654> npm install -g @anthropic-ai/claude-code </div></div><p class="text-gray-600 text-sm mb-2" data-v-508c8654> 如果遇到权限问题，可以使用 sudo： </p><div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> sudo npm install -g @anthropic-ai/claude-code </div></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-green-800 mb-2" data-v-508c8654> 验证 Claude Code 安装 </h6><p class="text-green-700 text-sm mb-3" data-v-508c8654> 安装完成后，输入以下命令检查是否安装成功： </p><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> claude --version </div></div><p class="text-green-700 text-sm mt-2" data-v-508c8654> 如果显示版本号，恭喜你！Claude Code 已经成功安装了。 </p></div></div>',2)),a("div",_,[t[35]||(t[35]=a("h4",{class:"text-xl font-semibold text-gray-800 mb-4 flex items-center"},[a("span",{class:"w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3"},"3"),e(" 设置环境变量 ")],-1)),a("div",q,[t[33]||(t[33]=a("h5",{class:"text-lg font-semibold text-gray-800 mb-3 flex items-center"},[a("i",{class:"fas fa-cog text-orange-600 mr-2"}),e(" 配置 Claude Code 环境变量 ")],-1)),t[34]||(t[34]=a("p",{class:"text-gray-700 mb-4"}," 为了让 Claude Code 连接到你的中转服务，需要设置两个环境变量： ",-1)),a("div",k,[a("div",G,[t[22]||(t[22]=a("h6",{class:"font-medium text-gray-800 mb-2"}," 方法一：临时设置（当前会话） ",-1)),t[23]||(t[23]=a("p",{class:"text-gray-600 text-sm mb-3"}," 在 Terminal 中运行以下命令： ",-1)),a("div",K,[a("div",z,' export ANTHROPIC_BASE_URL="'+d(s.currentBaseUrl)+'" ',1),t[21]||(t[21]=a("div",{class:"text-gray-300"},' export ANTHROPIC_AUTH_TOKEN="你的API密钥" ',-1))]),t[24]||(t[24]=a("p",{class:"text-yellow-700 text-xs mt-2"},' 💡 记得将 "你的API密钥" 替换为在上方 "API Keys" 标签页中创建的实际密钥。 ',-1))]),a("div",D,[t[31]||(t[31]=a("h6",{class:"font-medium text-gray-800 mb-2"}," 方法二：永久设置 ",-1)),t[32]||(t[32]=a("p",{class:"text-gray-600 text-sm mb-3"}," 编辑你的 shell 配置文件（根据你使用的 shell）： ",-1)),a("div",W,[t[25]||(t[25]=a("div",{class:"mb-2"}," # 对于 zsh (默认) ",-1)),a("div",$,` echo 'export ANTHROPIC_BASE_URL="`+d(s.currentBaseUrl)+`"' >> ~/.zshrc `,1),t[26]||(t[26]=a("div",{class:"text-gray-300"},` echo 'export ANTHROPIC_AUTH_TOKEN="你的API密钥"' >> ~/.zshrc `,-1)),t[27]||(t[27]=a("div",{class:"text-gray-300"}," source ~/.zshrc ",-1))]),a("div",M,[t[28]||(t[28]=a("div",{class:"mb-2"}," # 对于 bash ",-1)),a("div",V,` echo 'export ANTHROPIC_BASE_URL="`+d(s.currentBaseUrl)+`"' >> ~/.bash_profile `,1),t[29]||(t[29]=a("div",{class:"text-gray-300"},` echo 'export ANTHROPIC_AUTH_TOKEN="你的API密钥"' >> ~/.bash_profile `,-1)),t[30]||(t[30]=a("div",{class:"text-gray-300"}," source ~/.bash_profile ",-1))])])])])]),t[37]||(t[37]=l('<div class="mb-8" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>4</span> 开始使用 Claude Code </h4><div class="bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl p-6 border border-yellow-100" data-v-508c8654><p class="text-gray-700 mb-4" data-v-508c8654> 现在你可以开始使用 Claude Code 了！ </p><div class="space-y-4" data-v-508c8654><div data-v-508c8654><h6 class="font-medium text-gray-800 mb-2" data-v-508c8654> 启动 Claude Code </h6><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> claude </div></div></div><div data-v-508c8654><h6 class="font-medium text-gray-800 mb-2" data-v-508c8654> 在特定项目中使用 </h6><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="mb-2" data-v-508c8654> # 进入你的项目目录 </div><div class="text-gray-300" data-v-508c8654> cd /path/to/your/project </div><div class="mt-2 mb-2" data-v-508c8654> # 启动 Claude Code </div><div class="text-gray-300" data-v-508c8654> claude </div></div></div></div></div></div><div class="mb-8" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><i class="fas fa-wrench text-red-600 mr-3" data-v-508c8654></i> macOS 常见问题解决 </h4><div class="space-y-4" data-v-508c8654><details class="bg-gray-50 rounded-lg border border-gray-200" data-v-508c8654><summary class="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100" data-v-508c8654> 安装时提示权限错误 </summary><div class="px-4 pb-4 text-gray-600" data-v-508c8654><p class="mb-2" data-v-508c8654> 尝试以下解决方法： </p><ul class="list-disc list-inside space-y-1 text-sm" data-v-508c8654><li data-v-508c8654>使用 sudo 安装：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>sudo npm install -g @anthropic-ai/claude-code</code></li><li data-v-508c8654>或者配置 npm 使用用户目录：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>npm config set prefix ~/.npm-global</code></li></ul></div></details><details class="bg-gray-50 rounded-lg border border-gray-200" data-v-508c8654><summary class="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100" data-v-508c8654> macOS 安全设置阻止运行 </summary><div class="px-4 pb-4 text-gray-600" data-v-508c8654><p class="mb-2" data-v-508c8654> 如果系统阻止运行 Claude Code： </p><ul class="list-disc list-inside space-y-1 text-sm" data-v-508c8654><li data-v-508c8654>打开&quot;系统偏好设置&quot; → &quot;安全性与隐私&quot;</li><li data-v-508c8654>点击&quot;仍要打开&quot;或&quot;允许&quot;</li><li data-v-508c8654>或者在 Terminal 中运行：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>sudo spctl --master-disable</code></li></ul></div></details><details class="bg-gray-50 rounded-lg border border-gray-200" data-v-508c8654><summary class="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100" data-v-508c8654> 环境变量不生效 </summary><div class="px-4 pb-4 text-gray-600" data-v-508c8654><p class="mb-2" data-v-508c8654> 检查以下几点： </p><ul class="list-disc list-inside space-y-1 text-sm" data-v-508c8654><li data-v-508c8654>确认修改了正确的配置文件（.zshrc 或 .bash_profile）</li><li data-v-508c8654>重新启动 Terminal</li><li data-v-508c8654>验证设置：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>echo $ANTHROPIC_BASE_URL</code></li></ul></div></details></div></div>',2))])):r.value==="linux"?(c(),i("div",F,[t[53]||(t[53]=l('<div class="mb-10" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>1</span> 安装 Node.js 环境 </h4><p class="text-gray-600 mb-6" data-v-508c8654> Claude Code 需要 Node.js 环境才能运行。 </p><div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-6 border border-orange-100 mb-6" data-v-508c8654><h5 class="text-lg font-semibold text-gray-800 mb-3 flex items-center" data-v-508c8654><i class="fab fa-ubuntu text-orange-600 mr-2" data-v-508c8654></i> Linux 安装方法 </h5><div class="mb-4" data-v-508c8654><p class="text-gray-700 mb-3" data-v-508c8654> 方法一：使用官方仓库（推荐） </p><div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm" data-v-508c8654><div class="mb-2" data-v-508c8654> # 添加 NodeSource 仓库 </div><div class="text-gray-300" data-v-508c8654> curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash - </div><div class="mt-3 mb-2" data-v-508c8654> # 安装 Node.js </div><div class="text-gray-300" data-v-508c8654> sudo apt-get install -y nodejs </div></div></div><div class="mb-4" data-v-508c8654><p class="text-gray-700 mb-3" data-v-508c8654> 方法二：使用系统包管理器 </p><p class="text-gray-600 mb-2" data-v-508c8654> 虽然版本可能不是最新的，但对于基本使用已经足够： </p><div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm" data-v-508c8654><div class="mb-2" data-v-508c8654> # Ubuntu/Debian </div><div class="text-gray-300" data-v-508c8654> sudo apt update </div><div class="text-gray-300" data-v-508c8654> sudo apt install nodejs npm </div><div class="mt-3 mb-2" data-v-508c8654> # CentOS/RHEL/Fedora </div><div class="text-gray-300" data-v-508c8654> sudo dnf install nodejs npm </div></div></div><div class="bg-orange-50 border border-orange-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-orange-800 mb-2" data-v-508c8654> Linux 注意事项 </h6><ul class="text-orange-700 text-sm space-y-1" data-v-508c8654><li data-v-508c8654>• 某些发行版可能需要安装额外的依赖</li><li data-v-508c8654>• 如果遇到权限问题，使用 <code class="bg-orange-200 px-1 rounded" data-v-508c8654>sudo</code></li><li data-v-508c8654>• 确保你的用户在 npm 的全局目录有写权限</li></ul></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-green-800 mb-2" data-v-508c8654> 验证安装是否成功 </h6><p class="text-green-700 text-sm mb-3" data-v-508c8654> 安装完成后，打开终端，输入以下命令： </p><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> node --version </div><div class="text-gray-300" data-v-508c8654> npm --version </div></div><p class="text-green-700 text-sm mt-2" data-v-508c8654> 如果显示版本号，说明安装成功了！ </p></div></div><div class="mb-10" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>2</span> 安装 Claude Code </h4><div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100 mb-6" data-v-508c8654><h5 class="text-lg font-semibold text-gray-800 mb-3 flex items-center" data-v-508c8654><i class="fas fa-download text-purple-600 mr-2" data-v-508c8654></i> 安装 Claude Code </h5><p class="text-gray-700 mb-4" data-v-508c8654> 打开终端，运行以下命令： </p><div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm mb-4" data-v-508c8654><div class="mb-2" data-v-508c8654> # 全局安装 Claude Code </div><div class="text-gray-300" data-v-508c8654> npm install -g @anthropic-ai/claude-code </div></div><p class="text-gray-600 text-sm mb-2" data-v-508c8654> 如果遇到权限问题，可以使用 sudo： </p><div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> sudo npm install -g @anthropic-ai/claude-code </div></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4" data-v-508c8654><h6 class="font-medium text-green-800 mb-2" data-v-508c8654> 验证 Claude Code 安装 </h6><p class="text-green-700 text-sm mb-3" data-v-508c8654> 安装完成后，输入以下命令检查是否安装成功： </p><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> claude --version </div></div><p class="text-green-700 text-sm mt-2" data-v-508c8654> 如果显示版本号，恭喜你！Claude Code 已经成功安装了。 </p></div></div>',2)),a("div",Q,[t[52]||(t[52]=a("h4",{class:"text-xl font-semibold text-gray-800 mb-4 flex items-center"},[a("span",{class:"w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3"},"3"),e(" 设置环境变量 ")],-1)),a("div",Y,[t[50]||(t[50]=a("h5",{class:"text-lg font-semibold text-gray-800 mb-3 flex items-center"},[a("i",{class:"fas fa-cog text-orange-600 mr-2"}),e(" 配置 Claude Code 环境变量 ")],-1)),t[51]||(t[51]=a("p",{class:"text-gray-700 mb-4"}," 为了让 Claude Code 连接到你的中转服务，需要设置两个环境变量： ",-1)),a("div",J,[a("div",X,[t[39]||(t[39]=a("h6",{class:"font-medium text-gray-800 mb-2"}," 方法一：临时设置（当前会话） ",-1)),t[40]||(t[40]=a("p",{class:"text-gray-600 text-sm mb-3"}," 在终端中运行以下命令： ",-1)),a("div",Z,[a("div",tt,' export ANTHROPIC_BASE_URL="'+d(s.currentBaseUrl)+'" ',1),t[38]||(t[38]=a("div",{class:"text-gray-300"},' export ANTHROPIC_AUTH_TOKEN="你的API密钥" ',-1))]),t[41]||(t[41]=a("p",{class:"text-yellow-700 text-xs mt-2"},' 💡 记得将 "你的API密钥" 替换为在上方 "API Keys" 标签页中创建的实际密钥。 ',-1))]),a("div",at,[t[48]||(t[48]=a("h6",{class:"font-medium text-gray-800 mb-2"}," 方法二：永久设置 ",-1)),t[49]||(t[49]=a("p",{class:"text-gray-600 text-sm mb-3"}," 编辑你的 shell 配置文件： ",-1)),a("div",et,[t[42]||(t[42]=a("div",{class:"mb-2"}," # 对于 bash (默认) ",-1)),a("div",dt,` echo 'export ANTHROPIC_BASE_URL="`+d(s.currentBaseUrl)+`"' >> ~/.bashrc `,1),t[43]||(t[43]=a("div",{class:"text-gray-300"},` echo 'export ANTHROPIC_AUTH_TOKEN="你的API密钥"' >> ~/.bashrc `,-1)),t[44]||(t[44]=a("div",{class:"text-gray-300"}," source ~/.bashrc ",-1))]),a("div",st,[t[45]||(t[45]=a("div",{class:"mb-2"}," # 对于 zsh ",-1)),a("div",lt,` echo 'export ANTHROPIC_BASE_URL="`+d(s.currentBaseUrl)+`"' >> ~/.zshrc `,1),t[46]||(t[46]=a("div",{class:"text-gray-300"},` echo 'export ANTHROPIC_AUTH_TOKEN="你的API密钥"' >> ~/.zshrc `,-1)),t[47]||(t[47]=a("div",{class:"text-gray-300"}," source ~/.zshrc ",-1))])])])])]),t[54]||(t[54]=l('<div class="mb-8" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><span class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3" data-v-508c8654>4</span> 开始使用 Claude Code </h4><div class="bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl p-6 border border-yellow-100" data-v-508c8654><p class="text-gray-700 mb-4" data-v-508c8654> 现在你可以开始使用 Claude Code 了！ </p><div class="space-y-4" data-v-508c8654><div data-v-508c8654><h6 class="font-medium text-gray-800 mb-2" data-v-508c8654> 启动 Claude Code </h6><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="text-gray-300" data-v-508c8654> claude </div></div></div><div data-v-508c8654><h6 class="font-medium text-gray-800 mb-2" data-v-508c8654> 在特定项目中使用 </h6><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="mb-2" data-v-508c8654> # 进入你的项目目录 </div><div class="text-gray-300" data-v-508c8654> cd /path/to/your/project </div><div class="mt-2 mb-2" data-v-508c8654> # 启动 Claude Code </div><div class="text-gray-300" data-v-508c8654> claude </div></div></div></div></div></div><div class="mb-8" data-v-508c8654><h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center" data-v-508c8654><i class="fas fa-wrench text-red-600 mr-3" data-v-508c8654></i> Linux 常见问题解决 </h4><div class="space-y-4" data-v-508c8654><details class="bg-gray-50 rounded-lg border border-gray-200" data-v-508c8654><summary class="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100" data-v-508c8654> 安装时提示权限错误 </summary><div class="px-4 pb-4 text-gray-600" data-v-508c8654><p class="mb-2" data-v-508c8654> 尝试以下解决方法： </p><ul class="list-disc list-inside space-y-1 text-sm" data-v-508c8654><li data-v-508c8654>使用 sudo 安装：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>sudo npm install -g @anthropic-ai/claude-code</code></li><li data-v-508c8654>或者配置 npm 使用用户目录：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>npm config set prefix ~/.npm-global</code></li><li data-v-508c8654>然后添加到 PATH：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>export PATH=~/.npm-global/bin:$PATH</code></li></ul></div></details><details class="bg-gray-50 rounded-lg border border-gray-200" data-v-508c8654><summary class="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100" data-v-508c8654> 缺少依赖库 </summary><div class="px-4 pb-4 text-gray-600" data-v-508c8654><p class="mb-2" data-v-508c8654> 某些 Linux 发行版需要安装额外依赖： </p><div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm" data-v-508c8654><div class="mb-2" data-v-508c8654> # Ubuntu/Debian </div><div class="text-gray-300" data-v-508c8654> sudo apt install build-essential </div><div class="mt-2 mb-2" data-v-508c8654> # CentOS/RHEL </div><div class="text-gray-300" data-v-508c8654> sudo dnf groupinstall &quot;Development Tools&quot; </div></div></div></details><details class="bg-gray-50 rounded-lg border border-gray-200" data-v-508c8654><summary class="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100" data-v-508c8654> 环境变量不生效 </summary><div class="px-4 pb-4 text-gray-600" data-v-508c8654><p class="mb-2" data-v-508c8654> 检查以下几点： </p><ul class="list-disc list-inside space-y-1 text-sm" data-v-508c8654><li data-v-508c8654>确认修改了正确的配置文件（.bashrc 或 .zshrc）</li><li data-v-508c8654>重新启动终端或运行 <code class="bg-gray-200 px-1 rounded" data-v-508c8654>source ~/.bashrc</code></li><li data-v-508c8654>验证设置：<code class="bg-gray-200 px-1 rounded" data-v-508c8654>echo $ANTHROPIC_BASE_URL</code></li></ul></div></details></div></div>',2))])):b("",!0),t[56]||(t[56]=a("div",{class:"bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl p-6 text-center"},[a("h5",{class:"text-xl font-semibold mb-2"}," 🎉 恭喜你！ "),a("p",{class:"text-blue-100 mb-4"}," 你已经成功安装并配置了 Claude Code，现在可以开始享受 AI 编程助手带来的便利了。 "),a("p",{class:"text-sm text-blue-200"}," 如果在使用过程中遇到任何问题，可以查看官方文档或社区讨论获取帮助。 ")],-1))]))}},gt=m(rt,[["__scopeId","data-v-508c8654"]]);export{gt as default};
