// API模块统一导出
export { apiClient, ApiError, type ApiResponse, type PaginatedResponse } from './client'
export { dashboardApi } from './dashboard'
export { apiKeysApi } from './apiKeys' 
export { usageApi } from './usage'
export { subscriptionApi } from './subscription'

// 延迟加载的 API 统一对象
export const api = {
  get dashboard() {
    return import('./dashboard').then(m => m.dashboardApi)
  },
  get apiKeys() {
    return import('./apiKeys').then(m => m.apiKeysApi)
  },
  get usage() {
    return import('./usage').then(m => m.usageApi)
  },
  get subscription() {
    return import('./subscription').then(m => m.subscriptionApi)
  }
}