import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { useAuthStore } from '@/stores/auth'

// 导入全局样式
import './styles/main.css'

console.log('🚀 Starting Vue app initialization...')

const app = createApp(App)

// 安装插件
const pinia = createPinia()
app.use(pinia)
app.use(router)

// 初始化认证状态
console.log('🔐 Initializing authentication...')
const authStore = useAuthStore()
// 确保认证状态在应用启动时初始化
authStore.initAuth().then(() => {
  console.log('🔐 Authentication initialization completed')
}).catch(error => {
  console.error('🔐 Authentication initialization failed:', error)
})

// 开发模式下的调试信息
if (import.meta.env.DEV) {
  // 添加全局调试工具
  ;(window as any).__VUE_APP__ = app
  ;(window as any).__AUTH_STORE__ = authStore
  
  console.log('🚀 Vue app initialized in development mode')
}

// 基础错误处理
app.config.errorHandler = (error: Error, instance, info: string) => {
  console.error('Vue Error:', error)
  console.error('Component:', instance)
  console.error('Info:', info)
}

console.log('🚀 Mounting Vue app to #app...')
app.mount('#app')
