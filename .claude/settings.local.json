{"permissions": {"allow": ["Bash(npm install:*)", "Bash(npx tailwindcss:*)", "Bash(./node_modules/.bin/tailwindcss:*)", "Bash(cp:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_evaluate", "mcp__zen__chat", "mcp__sequential-thinking__sequentialthinking", "Bash(ls:*)", "mcp__chrome-mcp-stdio__chrome_navigate", "mcp__chrome-mcp-stdio__chrome_screenshot", "mcp__chrome-mcp-stdio__chrome_get_web_content", "mcp__chrome-mcp-stdio__chrome_network_request", "mcp__chrome-mcp-stdio__chrome_inject_script", "mcp__chrome-mcp-stdio__chrome_console", "mcp__chrome-mcp-stdio__chrome_get_interactive_elements", "Bash(find:*)", "Bash(grep:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(sed:*)", "WebFetch(domain:)", "mcp__puppeteer__puppeteer_fill", "mcp__puppeteer__puppeteer_click", "Bash(node:*)", "Bash(npx tsc:*)", "Bash(npm run type-check:*)"], "deny": []}}