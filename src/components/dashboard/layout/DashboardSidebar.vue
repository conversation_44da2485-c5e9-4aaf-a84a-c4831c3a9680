<template>
  <div class="account-page-left mg-r account-mobile-hidden">
    <div class="user-assets-item mg-b">
      <div class="title">资产概览</div>
      <div class="user-assets qk-flex">
        <div class="user-money-card">
          <div class="user-assets-name">余额<i class="ri-arrow-right-s-line"></i></div>
          <div class="user-assets-num">{{ dashboardStats.billing.balance || 0 }}</div>
        </div>
        <div class="user-credit-card">
          <div class="user-assets-name">积分<i class="ri-arrow-right-s-line"></i></div>
          <div class="user-assets-num">{{ dashboardStats.usage.credits || 0 }}</div>
        </div>
      </div>
    </div>
    
    <div class="tab-links">
      <router-link 
        v-for="item in navigationItems" 
        :key="item.path"
        :to="item.path" 
        class="link-item"
        :class="{ active: $route.path === item.path }"
      >
        <div class="link-title qk-flex">
          <i :class="item.icon"></i>
          <span>{{ item.name }}</span>
        </div>
        <i class="ri-arrow-right-s-line"></i>
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { type NavigationItem } from '@/types/dashboard'

const route = useRoute()

// 使用mock数据，避免store依赖导致的循环依赖问题
const mockDashboardStats = ref({
  billing: { 
    balance: 0
  },
  usage: { 
    credits: 0
  }
})

const dashboardStats = computed(() => mockDashboardStats.value)

const navigationItems: NavigationItem[] = [
  { name: '控制台概览', path: '/dashboard', icon: 'ri-dashboard-line' },
  { name: 'API密钥管理', path: '/dashboard/api-management', icon: 'ri-key-line' },
  { name: '使用统计', path: '/dashboard/usage-stats', icon: 'ri-bar-chart-line' },
  { name: '套餐管理', path: '/dashboard/subscription', icon: 'ri-vip-crown-line' },
  { name: '账单详情', path: '/dashboard/billing', icon: 'ri-file-list-line' },
  { name: '个人设置', path: '/dashboard/settings', icon: 'ri-settings-line' }
]
</script>
