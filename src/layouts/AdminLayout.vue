<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <img
              class="h-8 w-auto"
              src="https://ui-avatars.com/api/?name=X&background=4f46e5&color=fff&size=32"
              alt="XiangluAI"
            >
            <h1 class="ml-3 text-xl font-semibold text-gray-900">
              管理后台
            </h1>
          </div>
          
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">
              {{ adminUser?.username }}
            </span>
            <button
              @click="handleLogout"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <div class="flex">
      <!-- Sidebar -->
      <aside class="w-64 bg-white shadow-md h-[calc(100vh-4rem)]">
        <nav class="mt-5 px-2">
          <router-link
            v-for="item in menuItems"
            :key="item.path"
            :to="item.path"
            v-slot="{ isActive }"
            custom
          >
            <a
              :class="[
                isActive
                  ? 'bg-gray-100 text-gray-900'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                'group flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer'
              ]"
              @click="$router.push(item.path)"
            >
              <component 
                :is="item.icon" 
                :class="[
                  isActive ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500',
                  'mr-3 flex-shrink-0 h-6 w-6'
                ]"
              />
              {{ item.name }}
            </a>
          </router-link>
        </nav>
      </aside>

      <!-- Page Content -->
      <main class="flex-1 overflow-y-auto">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAdminAuthStore } from '@/stores/adminAuth'
import { 
  HomeIcon,
  UsersIcon,
  KeyIcon,
  ChartBarIcon,
  CreditCardIcon,
  CogIcon,
  DocumentTextIcon
} from '@/utils/icons'

const router = useRouter()
const adminAuthStore = useAdminAuthStore()

const adminUser = computed(() => adminAuthStore.adminUser)

const menuItems = computed(() => {
  const items = [
    { path: '/admin', name: '仪表盘', icon: HomeIcon },
    { path: '/admin/users', name: '用户管理', icon: UsersIcon },
    { path: '/admin/api-keys', name: 'API密钥', icon: KeyIcon },
    { path: '/admin/usage', name: '使用统计', icon: ChartBarIcon },
    { path: '/admin/subscriptions', name: '订阅管理', icon: CreditCardIcon },
  ]
  
  // 只有超级管理员可以看到系统配置和财务报表
  if (adminAuthStore.isSuperAdmin) {
    items.push(
      { path: '/admin/settings', name: '系统配置', icon: CogIcon },
      { path: '/admin/finance', name: '财务报表', icon: DocumentTextIcon }
    )
  }
  
  return items
})

const handleLogout = () => {
  adminAuthStore.logout()
  router.push('/admin/login')
}
</script>