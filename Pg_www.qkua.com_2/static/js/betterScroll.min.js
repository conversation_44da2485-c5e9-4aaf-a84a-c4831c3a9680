!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).BScroll={})}(this,function(t){"use strict";var z=function(t,e){return(z=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}))(t,e)};function e(t,e){function o(){this.constructor=t}z(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}var r=function(){return(r=Object.assign||function(t){for(var e,o=1,i=arguments.length;o<i;o++)for(var n in e=arguments[o])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)};function H(){for(var t=0,e=0,o=arguments.length;e<o;e++)t+=arguments[e].length;for(var i=Array(t),n=0,e=0;e<o;e++)for(var s=arguments[e],r=0,h=s.length;r<h;r++,n++)i[n]=s[r];return i}var F=[{sourceKey:"scroller.scrollBehaviorX.currentPos",key:"x"},{sourceKey:"scroller.scrollBehaviorY.currentPos",key:"y"},{sourceKey:"scroller.scrollBehaviorX.hasScroll",key:"hasHorizontalScroll"},{sourceKey:"scroller.scrollBehaviorY.hasScroll",key:"hasVerticalScroll"},{sourceKey:"scroller.scrollBehaviorX.contentSize",key:"scrollerWidth"},{sourceKey:"scroller.scrollBehaviorY.contentSize",key:"scrollerHeight"},{sourceKey:"scroller.scrollBehaviorX.maxScrollPos",key:"maxScrollX"},{sourceKey:"scroller.scrollBehaviorY.maxScrollPos",key:"maxScrollY"},{sourceKey:"scroller.scrollBehaviorX.minScrollPos",key:"minScrollX"},{sourceKey:"scroller.scrollBehaviorY.minScrollPos",key:"minScrollY"},{sourceKey:"scroller.scrollBehaviorX.movingDirection",key:"movingDirectionX"},{sourceKey:"scroller.scrollBehaviorY.movingDirection",key:"movingDirectionY"},{sourceKey:"scroller.scrollBehaviorX.direction",key:"directionX"},{sourceKey:"scroller.scrollBehaviorY.direction",key:"directionY"},{sourceKey:"scroller.actions.enabled",key:"enabled"},{sourceKey:"scroller.animater.pending",key:"pending"},{sourceKey:"scroller.animater.stop",key:"stop"},{sourceKey:"scroller.scrollTo",key:"scrollTo"},{sourceKey:"scroller.scrollBy",key:"scrollBy"},{sourceKey:"scroller.scrollToElement",key:"scrollToElement"},{sourceKey:"scroller.resetPosition",key:"resetPosition"}];function i(t){console.error("[BScroll warn]: "+t)}var n="undefined"!=typeof window,o=n&&navigator.userAgent.toLowerCase(),s=!(!o||!/wechatdevtools/.test(o)),K=o&&0<o.indexOf("android"),R="string"==typeof o&&(!!(o=/os (\d\d?_\d(_\d)?)/.exec(o))&&!!(13===(o=o[1].split("_").map(function(t){return parseInt(t,10)}))[0]&&4<=o[1])),I=!1;if(n)try{var h={};Object.defineProperty(h,"passive",{get:function(){I=!0}}),window.addEventListener("test-passive",function(){},h)}catch(t){}function u(){return window.performance&&window.performance.now&&window.performance.timing?window.performance.now()+window.performance.timing.navigationStart:+new Date}var a=function(t,e){for(var o in e)t[o]=e[o];return t};function c(t){return null==t}function j(t,e,o){return t<e?e:o<t?o:t}var l=n&&document.createElement("div").style,p=function(){if(n)for(var t=0,e=[{key:"standard",value:"transform"},{key:"webkit",value:"webkitTransform"},{key:"Moz",value:"MozTransform"},{key:"O",value:"OTransform"},{key:"ms",value:"msTransform"}];t<e.length;t++){var o=e[t];if(void 0!==l[o.value])return o.key}return!1}();function d(t){return!1===p?t:"standard"===p?"transitionEnd"===t?"transitionend":t:p+t.charAt(0).toUpperCase()+t.substr(1)}function W(t){return"string"==typeof t?document.querySelector(t):t}function _(t,e,o,i){i=I?{passive:!1,capture:!!i}:!!i;t.addEventListener(e,o,i)}function N(t,e,o,i){t.removeEventListener(e,o,{capture:!!i})}function q(t){t.cancelable&&t.preventDefault()}function U(t){for(var e=0,o=0;t;)e-=t.offsetLeft,o-=t.offsetTop,t=t.offsetParent;return{left:e,top:o}}p&&"standard"!==p&&p.toLowerCase();var o=d("transform"),h=d("transition"),Z=n&&d("perspective")in l,V=n&&("ontouchstart"in window||s),$=n&&h in l,f={transform:o,transition:h,transitionTimingFunction:d("transitionTimingFunction"),transitionDuration:d("transitionDuration"),transitionDelay:d("transitionDelay"),transformOrigin:d("transformOrigin"),transitionEnd:d("transitionEnd"),transitionProperty:d("transitionProperty")},v={touchstart:1,touchmove:1,touchend:1,touchcancel:1,mousedown:2,mousemove:2,mouseup:2};function G(t){var e;return t instanceof window.SVGElement?{top:(e=t.getBoundingClientRect()).top,left:e.left,width:e.width,height:e.height}:{top:t.offsetTop,left:t.offsetLeft,width:t.offsetWidth,height:t.offsetHeight}}function y(t,e){for(var o in e)if(e[o].test(t[o]))return!0;return!1}var J=y;function Q(t,e){void 0===e&&(e="click"),"mouseup"===t.type?n=t:"touchend"!==t.type&&"touchcancel"!==t.type||(n=t.changedTouches[0]);var o,i={},n=(n&&(i.screenX=n.screenX||0,i.screenY=n.screenY||0,i.clientX=n.clientX||0,i.clientY=n.clientY||0),{ctrlKey:t.ctrlKey,shiftKey:t.shiftKey,altKey:t.altKey,metaKey:t.metaKey});if("undefined"!=typeof MouseEvent)try{o=new MouseEvent(e,a(r({bubbles:!0,cancelable:!0},n),i))}catch(t){s()}else s();function s(){(o=document.createEvent("Event")).initEvent(e,!0,!0),a(o,i)}o.forwardedTouchEvent=!0,o._constructed=!0,t.target.dispatchEvent(o)}var m={swipe:{style:"cubic-bezier(0.23, 1, 0.32, 1)",fn:function(t){return 1+--t*t*t*t*t}},swipeBounce:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(t){return t*(2-t)}},bounce:{style:"cubic-bezier(0.165, 0.84, 0.44, 1)",fn:function(t){return 1- --t*t*t*t}}},s=n&&window;function tt(){}function et(t){}var ot=n?s.requestAnimationFrame||s.webkitRequestAnimationFrame||s.mozRequestAnimationFrame||s.oRequestAnimationFrame||function(t){return window.setTimeout(t,t.interval||1e3/60)}:tt,g=n?s.cancelAnimationFrame||s.webkitCancelAnimationFrame||s.mozCancelAnimationFrame||s.oCancelAnimationFrame||function(t){window.clearTimeout(t)}:tt,it={enumerable:!0,configurable:!0,get:et,set:et};function nt(t,s,e){it.get=function(){for(var t=this,e=s.split("."),o=0;o<e.length-1;o++)if("object"!=typeof(t=t[e[o]])||!t)return;var i=e.pop();return"function"==typeof t[i]?function(){return t[i].apply(t,arguments)}:t[i]},it.set=function(t){for(var e,o=this,i=s.split("."),n=0;n<i.length-1;n++)o[e=i[n]]||(o[e]={}),o=o[e];o[i.pop()]=t},Object.defineProperty(t,e,it)}T.prototype.on=function(t,e,o){return void 0===o&&(o=this),this.hasType(t),this.events[t]||(this.events[t]=[]),this.events[t].push([e,o]),this},T.prototype.once=function(i,n,s){var r=this,h=(void 0===s&&(s=this),this.hasType(i),function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];r.off(i,h);var o=n.apply(s,t);if(!0===o)return o});return h.fn=n,this.on(i,h),this},T.prototype.off=function(t,e){if(!t&&!e)return this.events={},this;if(t){if(this.hasType(t),e){var o=this.events[t];if(o)for(var i=o.length;i--;)(o[i][0]===e||o[i][0]&&o[i][0].fn===e)&&o.splice(i,1)}else this.events[t]=[];return this}},T.prototype.trigger=function(t){for(var e=[],o=1;o<arguments.length;o++)e[o-1]=arguments[o];this.hasType(t);t=this.events[t];if(t)for(var i=t.length,n=H(t),s=0;s<i;s++){var r=n[s],h=r[0],r=r[1];if(h&&!0===(h=h.apply(r,e)))return h}},T.prototype.registerType=function(t){var e=this;t.forEach(function(t){e.eventTypes[t]=t})},T.prototype.destroy=function(){this.events={},this.eventTypes={}},T.prototype.hasType=function(t){var e=this.eventTypes;e[t]!==t&&i('EventEmitter has used unknown event type: "'+t+'", should be oneof ['+Object.keys(e).map(function(t){return JSON.stringify(t)})+"]")};var k=T;function T(t){this.events={},this.eventTypes={},this.registerType(t)}S.prototype.destroy=function(){this.removeDOMEvents(),this.events=[]},S.prototype.addDOMEvents=function(){this.handleDOMEvents(_)},S.prototype.removeDOMEvents=function(){this.handleDOMEvents(N)},S.prototype.handleDOMEvents=function(e){var o=this,i=this.wrapper;this.events.forEach(function(t){e(i,t.name,o,!!t.capture)})},S.prototype.handleEvent=function(e){var o=e.type;this.events.some(function(t){return t.name===o&&(t.handler(e),!0)})};var b=S;function S(t,e){this.wrapper=t,this.events=e,this.addDOMEvents()}function st(){}e(P,rt=st),P.prototype.merge=function(t){if(t)for(var e in t)"bounce"===e?this.bounce=this.resolveBounce(t[e]):this[e]=t[e];return this},P.prototype.process=function(){return this.translateZ=this.HWCompositing&&Z?" translateZ(1px)":"",this.useTransition=this.useTransition&&$,this.preventDefault=!this.eventPassthrough&&this.preventDefault,this.scrollX="horizontal"!==this.eventPassthrough&&this.scrollX,this.scrollY="vertical"!==this.eventPassthrough&&this.scrollY,this.freeScroll=this.freeScroll&&!this.eventPassthrough,this.scrollX=!!this.freeScroll||this.scrollX,this.scrollY=!!this.freeScroll||this.scrollY,this.directionLockThreshold=this.eventPassthrough?0:this.directionLockThreshold,this},P.prototype.resolveBounce=function(t){var e={top:!0,right:!0,bottom:!0,left:!0},t="object"==typeof t?a(e,t):t?e:{top:!1,right:!1,bottom:!1,left:!1};return t};var rt,ht=P;function P(){var t=rt.call(this)||this;return t.startX=0,t.startY=0,t.scrollX=!1,t.scrollY=!0,t.freeScroll=!1,t.directionLockThreshold=0,t.eventPassthrough="",t.click=!1,t.dblclick=!1,t.tap="",t.bounce={top:!0,bottom:!0,left:!0,right:!0},t.bounceTime=800,t.momentum=!0,t.momentumLimitTime=300,t.momentumLimitDistance=15,t.swipeTime=2500,t.swipeBounceTime=500,t.deceleration=.0015,t.flickLimitTime=200,t.flickLimitDistance=100,t.resizePolling=60,t.probeType=0,t.stopPropagation=!1,t.preventDefault=!0,t.preventDefaultException={tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT|AUDIO)$/},t.tagException={tagName:/^TEXTAREA$/},t.HWCompositing=!0,t.useTransition=!0,t.bindToWrapper=!1,t.bindToTarget=!1,t.disableMouse=V,t.disableTouch=!V,t.autoBlur=!0,t.autoEndDistance=5,t.outOfBoundaryDampingFactor=1/3,t.specifiedIndexAsContent=0,t.quadrant=1,t}w.prototype.handleDOMEvents=function(){var t=this.options,e=t.bindToWrapper,o=t.disableMouse,i=t.disableTouch,t=t.click,n=this.wrapper,e=e?n:window,s=[],r=[],i=!i,o=!o;t&&s.push({name:"click",handler:this.click.bind(this),capture:!0}),i&&(s.push({name:"touchstart",handler:this.start.bind(this)}),r.push({name:"touchmove",handler:this.move.bind(this)},{name:"touchend",handler:this.end.bind(this)},{name:"touchcancel",handler:this.end.bind(this)})),o&&(s.push({name:"mousedown",handler:this.start.bind(this)}),r.push({name:"mousemove",handler:this.move.bind(this)},{name:"mouseup",handler:this.end.bind(this)})),this.wrapperEventRegister=new b(n,s),this.targetEventRegister=new b(e,r)},w.prototype.beforeHandler=function(t,e){var o=this.options,i=o.preventDefault,n=o.stopPropagation,s=o.preventDefaultException;!{start:function(){return i&&!y(t.target,s)},end:function(){return i&&!y(t.target,s)},move:function(){return i}}[e]()||t.preventDefault(),n&&t.stopPropagation()},w.prototype.setInitiated=function(t){this.initiated=t=void 0===t?0:t},w.prototype.start=function(t){var e=v[t.type];this.initiated&&this.initiated!==e||(this.setInitiated(e),J(t.target,this.options.tagException)?this.setInitiated():2===e&&0!==t.button||this.hooks.trigger(this.hooks.eventTypes.beforeStart,t)||(this.beforeHandler(t,"start"),e=t.touches?t.touches[0]:t,this.pointX=e.pageX,this.pointY=e.pageY,this.hooks.trigger(this.hooks.eventTypes.start,t)))},w.prototype.move=function(t){var e,o,i;v[t.type]===this.initiated&&(this.beforeHandler(t,"move"),i=(o=t.touches?t.touches[0]:t).pageX-this.pointX,e=o.pageY-this.pointY,this.pointX=o.pageX,this.pointY=o.pageY,this.hooks.trigger(this.hooks.eventTypes.move,{deltaX:i,deltaY:e,e:t})||(o=document.documentElement.scrollLeft||window.pageXOffset||document.body.scrollLeft,i=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop,e=this.pointX-o,o=this.pointY-i,i=this.options.autoEndDistance,(e>document.documentElement.clientWidth-i||o>document.documentElement.clientHeight-i||e<i||o<i)&&this.end(t)))},w.prototype.end=function(t){v[t.type]===this.initiated&&(this.setInitiated(),this.beforeHandler(t,"end"),this.hooks.trigger(this.hooks.eventTypes.end,t))},w.prototype.click=function(t){this.hooks.trigger(this.hooks.eventTypes.click,t)},w.prototype.setContent=function(t){t!==this.wrapper&&(this.wrapper=t,this.rebindDOMEvents())},w.prototype.rebindDOMEvents=function(){this.wrapperEventRegister.destroy(),this.targetEventRegister.destroy(),this.handleDOMEvents()},w.prototype.destroy=function(){this.wrapperEventRegister.destroy(),this.targetEventRegister.destroy(),this.hooks.destroy()};var at=w;function w(t,e){this.wrapper=t,this.options=e,this.hooks=new k(["beforeStart","start","move","end","click"]),this.handleDOMEvents()}var ct={x:["translateX","px"],y:["translateY","px"]},lt=(E.prototype.getComputedPosition=function(){var t=window.getComputedStyle(this.content,null)[f.transform].split(")")[0].split(", ");return{x:+(t[12]||t[4])||0,y:+(t[13]||t[5])||0}},E.prototype.translate=function(i){var n=[];Object.keys(i).forEach(function(t){var e,o;!ct[t]||(e=ct[t][0])&&(o=ct[t][1],t=i[t],n.push(e+"("+t+o+")"))}),this.hooks.trigger(this.hooks.eventTypes.beforeTranslate,n,i),this.style[f.transform]=n.join(" "),this.hooks.trigger(this.hooks.eventTypes.translate,i)},E.prototype.setContent=function(t){this.content!==t&&(this.content=t,this.style=t.style)},E.prototype.destroy=function(){this.hooks.destroy()},E);function E(t){this.setContent(t),this.hooks=new k(["beforeTranslate","translate"])}B.prototype.translate=function(t){this.translater.translate(t)},B.prototype.setPending=function(t){this.pending=t},B.prototype.setForceStopped=function(t){this.forceStopped=t},B.prototype.setCallStop=function(t){this.callStopWhenPending=t},B.prototype.setContent=function(t){this.content!==t&&(this.content=t,this.style=t.style,this.stop())},B.prototype.clearTimer=function(){this.timer&&(g(this.timer),this.timer=0)},B.prototype.destroy=function(){this.hooks.destroy(),g(this.timer)};o=B;function B(t,e,o){this.translater=e,this.options=o,this.timer=0,this.hooks=new k(["move","end","beforeForceStop","forceStop","callStop","time","timeFunction"]),this.setContent(t)}function pt(t,e,o,i){function n(t,e){return 0<(t-=e)?-1:t<0?1:0}var s=n(e.x,t.x),e=n(e.y,t.y),t=o.x-i.x,o=o.y-i.y;return s*t<=0&&e*o<=0}e(C,ut=o),C.prototype.startProbe=function(e,o){var i=this,n=e,s=function(){var t=i.translater.getComputedPosition();pt(e,o,t,n)&&i.hooks.trigger(i.hooks.eventTypes.move,t),i.pending||(i.callStopWhenPending?i.callStopWhenPending=!1:i.hooks.trigger(i.hooks.eventTypes.end,t)),n=t,i.pending&&(i.timer=ot(s))};this.callStopWhenPending&&this.setCallStop(!1),g(this.timer),s()},C.prototype.transitionTime=function(t){this.style[f.transitionDuration]=(t=void 0===t?0:t)+"ms",this.hooks.trigger(this.hooks.eventTypes.time,t)},C.prototype.transitionTimingFunction=function(t){this.style[f.transitionTimingFunction]=t,this.hooks.trigger(this.hooks.eventTypes.timeFunction,t)},C.prototype.transitionProperty=function(){this.style[f.transitionProperty]=f.transform},C.prototype.move=function(t,e,o,i){this.setPending(0<o),this.transitionTimingFunction(i),this.transitionProperty(),this.transitionTime(o),this.translate(e);i=3===this.options.probeType;o&&i&&this.startProbe(t,e),o||(this._reflow=this.content.offsetHeight,i&&this.hooks.trigger(this.hooks.eventTypes.move,e),this.hooks.trigger(this.hooks.eventTypes.end,e))},C.prototype.doStop=function(){var t,e,o=this.pending;return this.setForceStopped(!1),this.setCallStop(!1),o&&(this.setPending(!1),g(this.timer),t=(e=this.translater.getComputedPosition()).x,e=e.y,this.transitionTime(),this.translate({x:t,y:e}),this.setForceStopped(!0),this.setCallStop(!0),this.hooks.trigger(this.hooks.eventTypes.forceStop,{x:t,y:e})),o},C.prototype.stop=function(){this.doStop()&&this.hooks.trigger(this.hooks.eventTypes.callStop)};var ut,dt=C;function C(){return null!==ut&&ut.apply(this,arguments)||this}e(D,ft=o),D.prototype.move=function(t,e,o,i){o?this.animate(t,e,o,i):(this.translate(e),3===this.options.probeType&&this.hooks.trigger(this.hooks.eventTypes.move,e),this.hooks.trigger(this.hooks.eventTypes.end,e))},D.prototype.animate=function(s,r,e,o){var h=this,a=u(),c=a+e,l=3===this.options.probeType,p=function(){var i,n,t=u();c<=t?(h.translate(r),l&&h.hooks.trigger(h.hooks.eventTypes.move,r),h.hooks.trigger(h.hooks.eventTypes.end,r)):(i=o((t-a)/e),n={},Object.keys(r).forEach(function(t){var e=s[t],o=r[t];n[t]=(o-e)*i+e}),h.translate(n),l&&h.hooks.trigger(h.hooks.eventTypes.move,n),h.pending&&(h.timer=ot(p)),h.pending||(h.callStopWhenPending?h.callStopWhenPending=!1:h.hooks.trigger(h.hooks.eventTypes.end,r)))};this.setPending(!0),this.callStopWhenPending&&this.setCallStop(!1),g(this.timer),p()},D.prototype.doStop=function(){var t,e=this.pending;return this.setForceStopped(!1),this.setCallStop(!1),e&&(this.setPending(!1),g(this.timer),t=this.translater.getComputedPosition(),this.setForceStopped(!0),this.setCallStop(!0),this.hooks.trigger(this.hooks.eventTypes.forceStop,t)),e},D.prototype.stop=function(){this.doStop()&&this.hooks.trigger(this.hooks.eventTypes.callStop)};var ft,vt=D;function D(){return null!==ft&&ft.apply(this,arguments)||this}x.prototype.start=function(){this.dist=0,this.setMovingDirection(0),this.setDirection(0)},x.prototype.move=function(t){return t=this.hasScroll?t:0,this.setMovingDirection(t),this.performDampingAlgorithm(t,this.options.outOfBoundaryDampingFactor)},x.prototype.setMovingDirection=function(t){this.movingDirection=0<t?-1:t<0?1:0},x.prototype.setDirection=function(t){this.direction=0<t?-1:t<0?1:0},x.prototype.performDampingAlgorithm=function(t,e){var o=this.currentPos+t;return o=o>this.minScrollPos||o<this.maxScrollPos?o>this.minScrollPos&&this.options.bounces[0]||o<this.maxScrollPos&&this.options.bounces[1]?this.currentPos+t*e:o>this.minScrollPos?this.minScrollPos:this.maxScrollPos:o},x.prototype.end=function(t){var e={duration:0},o=Math.abs(this.currentPos-this.startPos);return this.options.momentum&&t<this.options.momentumLimitTime&&o>this.options.momentumLimitDistance?(o=-1===this.direction&&this.options.bounces[0]||1===this.direction&&this.options.bounces[1]?this.wrapperSize:0,e=this.hasScroll?this.momentum(this.currentPos,this.startPos,t,this.maxScrollPos,this.minScrollPos,o,this.options):{destination:this.currentPos,duration:0}):this.hooks.trigger(this.hooks.eventTypes.end,e),e},x.prototype.momentum=function(t,e,o,i,n,s,r){void 0===r&&(r=this.options);var e=t-e,o=Math.abs(e)/o,h=r.deceleration,a=r.swipeBounceTime,r=r.swipeTime,t={destination:t+o*o/h*(e<0?-1:1),duration:Math.min(r,2*o/h),rate:15};return this.hooks.trigger(this.hooks.eventTypes.momentum,t,e),t.destination<i?(t.destination=s?Math.max(i-s/4,i-s/t.rate*o):i,t.duration=a):t.destination>n&&(t.destination=s?Math.min(n+s/4,n+s/t.rate*o):n,t.duration=a),t.destination=Math.round(t.destination),t},x.prototype.updateDirection=function(){var t=this.currentPos-this.absStartPos;this.setDirection(t)},x.prototype.refresh=function(t){var e=this.options.rect,o=e.size,e=e.position,i="static"===window.getComputedStyle(this.wrapper,null).position,n=G(this.wrapper),t=(this.wrapperSize=this.wrapper["width"===o?"clientWidth":"clientHeight"],this.setContent(t),G(this.content));this.contentSize=t[o],this.relativeOffset=t[e],i&&(this.relativeOffset-=n[e]),this.computeBoundary(),this.setDirection(0)},x.prototype.setContent=function(t){t!==this.content&&(this.content=t,this.resetState())},x.prototype.resetState=function(){this.currentPos=0,this.startPos=0,this.dist=0,this.setDirection(0),this.setMovingDirection(0),this.resetStartPos()},x.prototype.computeBoundary=function(){this.hooks.trigger(this.hooks.eventTypes.beforeComputeBoundary);var t={minScrollPos:0,maxScrollPos:this.wrapperSize-this.contentSize};t.maxScrollPos<0&&(t.maxScrollPos-=this.relativeOffset,0===this.options.specifiedIndexAsContent&&(t.minScrollPos=-this.relativeOffset)),this.hooks.trigger(this.hooks.eventTypes.computeBoundary,t),this.minScrollPos=t.minScrollPos,this.maxScrollPos=t.maxScrollPos,this.hasScroll=this.options.scrollable&&this.maxScrollPos<this.minScrollPos,!this.hasScroll&&this.minScrollPos<this.maxScrollPos&&(this.maxScrollPos=this.minScrollPos,this.contentSize=this.wrapperSize)},x.prototype.updatePosition=function(t){this.currentPos=t},x.prototype.getCurrentPos=function(){return this.currentPos},x.prototype.checkInBoundary=function(){var t=this.adjustPosition(this.currentPos);return{position:t,inBoundary:t===this.getCurrentPos()}},x.prototype.adjustPosition=function(t){return!this.hasScroll&&!this.hooks.trigger(this.hooks.eventTypes.ignoreHasScroll)||t>this.minScrollPos?t=this.minScrollPos:t<this.maxScrollPos&&(t=this.maxScrollPos),t},x.prototype.updateStartPos=function(){this.startPos=this.currentPos},x.prototype.updateAbsStartPos=function(){this.absStartPos=this.currentPos},x.prototype.resetStartPos=function(){this.updateStartPos(),this.updateAbsStartPos()},x.prototype.getAbsDist=function(t){return this.dist+=t,Math.abs(this.dist)},x.prototype.destroy=function(){this.hooks.destroy()};var yt=x;function x(t,e,o){this.wrapper=t,this.options=o,this.hooks=new k(["beforeComputeBoundary","computeBoundary","momentum","end","ignoreHasScroll"]),this.refresh(e)}(h={}).yes=function(t){return!0},h.no=function(t){return q(t),!1};var mt=h,gt=((s={}).horizontal=((o={}).yes="horizontal",o.no="vertical",o),s.vertical=((h={}).yes="vertical",h.no="horizontal",h),s),kt=(M.prototype.reset=function(){this.directionLocked=""},M.prototype.checkMovingDirection=function(t,e,o){return this.computeDirectionLock(t,e),this.handleEventPassthrough(o)},M.prototype.adjustDelta=function(t,e){return"horizontal"===this.directionLocked?e=0:"vertical"===this.directionLocked&&(t=0),{deltaX:t,deltaY:e}},M.prototype.computeDirectionLock=function(t,e){""!==this.directionLocked||this.freeScroll||(t>e+this.directionLockThreshold?this.directionLocked="horizontal":e>=t+this.directionLockThreshold?this.directionLocked="vertical":this.directionLocked="none")},M.prototype.handleEventPassthrough=function(t){var e=gt[this.directionLocked];if(e){if(this.eventPassthrough===e.yes)return mt.yes(t);if(this.eventPassthrough===e.no)return mt.no(t)}return!1},M);function M(t,e,o){this.directionLockThreshold=t,this.freeScroll=e,this.eventPassthrough=o,this.reset()}X.prototype.bindActionsHandler=function(){var n=this;this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.start,function(t){return!n.enabled||n.handleStart(t)}),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.move,function(t){var e,o=t.deltaX,i=t.deltaY,t=t.e;return!n.enabled||(o=o,i=i,e=2===(e=n.options.quadrant)?[i,-o]:3===e?[-o,-i]:4===e?[-i,o]:[o,i],n.hooks.trigger(n.hooks.eventTypes.coordinateTransformation,o={deltaX:e[0],deltaY:e[1]}),n.handleMove(o.deltaX,o.deltaY,t))}),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.end,function(t){return!n.enabled||n.handleEnd(t)}),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.click,function(t){n.enabled&&!t._constructed&&n.handleClick(t)})},X.prototype.handleStart=function(t){var e=u();this.fingerMoved=!1,this.contentMoved=!1,this.startTime=e,this.directionLockAction.reset(),this.scrollBehaviorX.start(),this.scrollBehaviorY.start(),this.animater.doStop(),this.scrollBehaviorX.resetStartPos(),this.scrollBehaviorY.resetStartPos(),this.hooks.trigger(this.hooks.eventTypes.start,t)},X.prototype.handleMove=function(t,e,o){var i,n,s;if(!this.hooks.trigger(this.hooks.eventTypes.beforeMove,o))return s=this.scrollBehaviorX.getAbsDist(t),n=this.scrollBehaviorY.getAbsDist(e),i=u(),!!this.checkMomentum(s,n,i)||(this.directionLockAction.checkMovingDirection(s,n,o)?(this.actionsHandler.setInitiated(),!0):(s=this.directionLockAction.adjustDelta(t,e),n=this.scrollBehaviorX.getCurrentPos(),o=this.scrollBehaviorX.move(s.deltaX),t=this.scrollBehaviorY.getCurrentPos(),e=this.scrollBehaviorY.move(s.deltaY),void(this.hooks.trigger(this.hooks.eventTypes.detectMovingDirection)||(this.fingerMoved||(this.fingerMoved=!0),s=o!==n||e!==t,this.contentMoved||s||this.hooks.trigger(this.hooks.eventTypes.contentNotMoved),!this.contentMoved&&s&&(this.contentMoved=!0,this.hooks.trigger(this.hooks.eventTypes.scrollStart)),this.contentMoved&&s&&(this.animater.translate({x:o,y:e}),this.dispatchScroll(i))))))},X.prototype.dispatchScroll=function(t){t-this.startTime>this.options.momentumLimitTime&&(this.startTime=t,this.scrollBehaviorX.updateStartPos(),this.scrollBehaviorY.updateStartPos(),1===this.options.probeType&&this.hooks.trigger(this.hooks.eventTypes.scroll,this.getCurrentPos())),1<this.options.probeType&&this.hooks.trigger(this.hooks.eventTypes.scroll,this.getCurrentPos())},X.prototype.checkMomentum=function(t,e,o){return o-this.endTime>this.options.momentumLimitTime&&e<this.options.momentumLimitDistance&&t<this.options.momentumLimitDistance},X.prototype.handleEnd=function(t){if(!this.hooks.trigger(this.hooks.eventTypes.beforeEnd,t)){var e=this.getCurrentPos();if(this.scrollBehaviorX.updateDirection(),this.scrollBehaviorY.updateDirection(),this.hooks.trigger(this.hooks.eventTypes.end,t,e))return!0;e=this.ensureIntegerPos(e),this.animater.translate(e),this.endTime=u();t=this.endTime-this.startTime;this.hooks.trigger(this.hooks.eventTypes.scrollEnd,e,t)}},X.prototype.ensureIntegerPos=function(t){this.ensuringInteger=!0;var e=t.x,t=t.y,o=this.scrollBehaviorX,i=o.minScrollPos,o=o.maxScrollPos,n=this.scrollBehaviorY,s=n.minScrollPos,n=n.maxScrollPos,e=0<e?Math.ceil(e):Math.floor(e),t=0<t?Math.ceil(t):Math.floor(t);return{x:j(e,o,i),y:j(t,n,s)}},X.prototype.handleClick=function(t){y(t.target,this.options.preventDefaultException)||(q(t),t.stopPropagation())},X.prototype.getCurrentPos=function(){return{x:this.scrollBehaviorX.getCurrentPos(),y:this.scrollBehaviorY.getCurrentPos()}},X.prototype.refresh=function(){this.endTime=0},X.prototype.destroy=function(){this.hooks.destroy()};var Tt=X;function X(t,e,o,i,n){this.hooks=new k(["start","beforeMove","scrollStart","scroll","beforeEnd","end","scrollEnd","contentNotMoved","detectMovingDirection","coordinateTransformation"]),this.scrollBehaviorX=t,this.scrollBehaviorY=e,this.actionsHandler=o,this.animater=i,this.options=n,this.directionLockAction=new kt(n.directionLockThreshold,n.freeScroll,n.eventPassthrough),this.enabled=!0,this.bindActionsHandler()}function bt(o,t,e,i){var n=["momentum","momentumLimitTime","momentumLimitDistance","deceleration","swipeBounceTime","swipeTime","outOfBoundaryDampingFactor","specifiedIndexAsContent"].reduce(function(t,e){return t[e]=o[e],t},{});return n.scrollable=!!o[t],n.bounces=e,n.rect=i,n}function St(i,n,t){t.forEach(function(t){var e,o;"string"==typeof t?e=o=t:(e=t.source,o=t.target),i.on(e,function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n.trigger.apply(n,H([o],t))})})}Y.prototype.init=function(){var t=this;this.bindTranslater(),this.bindAnimater(),this.bindActions(),this.hooks.on(this.hooks.eventTypes.scrollEnd,function(){t.togglePointerEvents(!0)})},Y.prototype.registerTransitionEnd=function(){this.transitionEndRegister=new b(this.content,[{name:f.transitionEnd,handler:this.transitionEnd.bind(this)}])},Y.prototype.bindTranslater=function(){var o=this,t=this.translater.hooks;t.on(t.eventTypes.beforeTranslate,function(t){o.options.translateZ&&t.push(o.options.translateZ)}),t.on(t.eventTypes.translate,function(t){var e=o.getCurrentPos();o.updatePositions(t),!0===o.actions.ensuringInteger?o.actions.ensuringInteger=!1:t.x===e.x&&t.y===e.y||o.togglePointerEvents(!1)})},Y.prototype.bindAnimater=function(){var e=this;this.animater.hooks.on(this.animater.hooks.eventTypes.end,function(t){e.resetPosition(e.options.bounceTime)||(e.animater.setPending(!1),e.hooks.trigger(e.hooks.eventTypes.scrollEnd,t))}),St(this.animater.hooks,this.hooks,[{source:this.animater.hooks.eventTypes.move,target:this.hooks.eventTypes.scroll},{source:this.animater.hooks.eventTypes.forceStop,target:this.hooks.eventTypes.scrollEnd}])},Y.prototype.bindActions=function(){var n=this,s=this.actions;St(s.hooks,this.hooks,[{source:s.hooks.eventTypes.start,target:this.hooks.eventTypes.beforeStart},{source:s.hooks.eventTypes.start,target:this.hooks.eventTypes.beforeScrollStart},{source:s.hooks.eventTypes.beforeMove,target:this.hooks.eventTypes.beforeMove},{source:s.hooks.eventTypes.scrollStart,target:this.hooks.eventTypes.scrollStart},{source:s.hooks.eventTypes.scroll,target:this.hooks.eventTypes.scroll},{source:s.hooks.eventTypes.beforeEnd,target:this.hooks.eventTypes.beforeEnd}]),s.hooks.on(s.hooks.eventTypes.end,function(t,e){return n.hooks.trigger(n.hooks.eventTypes.touchEnd,e),!!n.hooks.trigger(n.hooks.eventTypes.end,e)||(!(s.fingerMoved||(n.hooks.trigger(n.hooks.eventTypes.scrollCancel),!n.checkClick(t)))||(n.resetPosition(n.options.bounceTime,m.bounce)?(n.animater.setForceStopped(!1),!0):void 0))}),s.hooks.on(s.hooks.eventTypes.scrollEnd,function(t,e){var o=Math.abs(t.x-n.scrollBehaviorX.startPos),i=Math.abs(t.y-n.scrollBehaviorY.startPos);n.checkFlick(e,o,i)?(n.animater.setForceStopped(!1),n.hooks.trigger(n.hooks.eventTypes.flick)):(n.momentum(t,e)||(s.contentMoved&&n.hooks.trigger(n.hooks.eventTypes.scrollEnd,t),n.animater.forceStopped))&&n.animater.setForceStopped(!1)})},Y.prototype.checkFlick=function(t,e,o){if(1<this.hooks.events.flick.length&&t<this.options.flickLimitTime&&e<this.options.flickLimitDistance&&o<this.options.flickLimitDistance&&(1<o||1<e))return!0},Y.prototype.momentum=function(t,e){var o={time:0,easing:m.swiper,newX:t.x,newY:t.y},i=this.scrollBehaviorX.end(e),e=this.scrollBehaviorY.end(e);if(o.newX=c(i.destination)?o.newX:i.destination,o.newY=c(e.destination)?o.newY:e.destination,o.time=Math.max(i.duration,e.duration),this.hooks.trigger(this.hooks.eventTypes.momentum,o,this),o.newX!==t.x||o.newY!==t.y)return(o.newX>this.scrollBehaviorX.minScrollPos||o.newX<this.scrollBehaviorX.maxScrollPos||o.newY>this.scrollBehaviorY.minScrollPos||o.newY<this.scrollBehaviorY.maxScrollPos)&&(o.easing=m.swipeBounce),this.scrollTo(o.newX,o.newY,o.time,o.easing),!0},Y.prototype.checkClick=function(t){var e,o,i,n=this.animater.forceStopped;return this.hooks.trigger(this.hooks.eventTypes.checkClick)?(this.animater.setForceStopped(!1),!0):!n&&(n=!1,(e=this.options.dblclick)&&this.lastClickTime&&(e=void 0===(e=e.delay)?300:e,u()-this.lastClickTime<e&&(n=!0,Q(t,"dblclick"))),this.options.tap&&(e=t,o=this.options.tap,(i=document.createEvent("Event")).initEvent(o,!0,!0),i.pageX=e.pageX,i.pageY=e.pageY,e.target.dispatchEvent(i)),this.options.click&&!y(t.target,this.options.preventDefaultException)&&Q(t),this.lastClickTime=n?null:u(),!0)},Y.prototype.resize=function(){var t=this;this.actions.enabled&&(K&&(this.wrapper.scrollTop=0),clearTimeout(this.resizeTimeout),this.resizeTimeout=window.setTimeout(function(){t.hooks.trigger(t.hooks.eventTypes.resize)},this.options.resizePolling))},Y.prototype.transitionEnd=function(t){t.target===this.content&&this.animater.pending&&(this.animater.transitionTime(),this.resetPosition(this.options.bounceTime,m.bounce)||(this.animater.setPending(!1),3!==this.options.probeType&&this.hooks.trigger(this.hooks.eventTypes.scrollEnd,this.getCurrentPos())))},Y.prototype.togglePointerEvents=function(t){for(var e=this.content.children.length?this.content.children:[this.content],o=(t=void 0===t?!0:t)?"auto":"none",i=0;i<e.length;i++){var n=e[i];n.isBScrollContainer||(n.style.pointerEvents=o)}},Y.prototype.refresh=function(t){var e=this.setContent(t);this.hooks.trigger(this.hooks.eventTypes.beforeRefresh),this.scrollBehaviorX.refresh(t),this.scrollBehaviorY.refresh(t),e&&(this.translater.setContent(t),this.animater.setContent(t),this.transitionEndRegister.destroy(),this.registerTransitionEnd(),this.options.bindToTarget&&this.actionsHandler.setContent(t)),this.actions.refresh(),this.wrapperOffset=U(this.wrapper)},Y.prototype.setContent=function(t){var e=t!==this.content;return e&&(this.content=t),e},Y.prototype.scrollBy=function(t,e,o,i){void 0===o&&(o=0);var n=this.getCurrentPos(),s=n.x,n=n.y;this.scrollTo(t+=s,e+=n,o,i=i||m.bounce)},Y.prototype.scrollTo=function(t,e,o,i,n){void 0===o&&(o=0),void 0===i&&(i=m.bounce),void 0===n&&(n={start:{},end:{}});var i=this.options.useTransition?i.style:i.fn,s=this.getCurrentPos(),s=r({x:s.x,y:s.y},n.start),t=r({x:t,y:e},n.end);this.hooks.trigger(this.hooks.eventTypes.scrollTo,t),function(t,e){for(var o=0,i=Object.keys(t);o<i.length;o++){var n=i[o];if(t[n]!==e[n])return}return 1}(s,t)||(e=Math.abs(t.x-s.x),n=Math.abs(t.y-s.y),e<1&&n<1&&(o=0,this.hooks.trigger(this.hooks.eventTypes.minDistanceScroll)),this.animater.move(s,t,o,i))},Y.prototype.scrollToElement=function(t,e,o,i,n){function s(t,e,o){return"number"==typeof t?t:t?Math.round(e/2-o/2):0}function r(t,e,o,i){return t=i.adjustPosition((t-=e)-o)}var t=W(t),h=U(t);o=s(o,t.offsetWidth,this.wrapper.offsetWidth),i=s(i,t.offsetHeight,this.wrapper.offsetHeight);h.left=r(h.left,this.wrapperOffset.left,o,this.scrollBehaviorX),h.top=r(h.top,this.wrapperOffset.top,i,this.scrollBehaviorY),this.hooks.trigger(this.hooks.eventTypes.scrollToElement,t,h)||this.scrollTo(h.left,h.top,e,n)},Y.prototype.resetPosition=function(t,e){void 0===t&&(t=0),void 0===e&&(e=m.bounce);var o=this.scrollBehaviorX.checkInBoundary(),i=o.position,o=o.inBoundary,n=this.scrollBehaviorY.checkInBoundary(),s=n.position,n=n.inBoundary;return(!o||!n)&&(R&&this.reflow(),this.scrollTo(i,s,t,e),!0)},Y.prototype.reflow=function(){this._reflow=this.content.offsetHeight},Y.prototype.updatePositions=function(t){this.scrollBehaviorX.updatePosition(t.x),this.scrollBehaviorY.updatePosition(t.y)},Y.prototype.getCurrentPos=function(){return this.actions.getCurrentPos()},Y.prototype.enable=function(){this.actions.enabled=!0},Y.prototype.disable=function(){g(this.animater.timer),this.actions.enabled=!1},Y.prototype.destroy=function(){var e=this;["resizeRegister","transitionEndRegister","actionsHandler","actions","hooks","animater","translater","scrollBehaviorX","scrollBehaviorY"].forEach(function(t){return e[t].destroy()})};var Pt=Y;function Y(t,e,o){this.wrapper=t,this.content=e,this.resizeTimeout=0,this.hooks=new k(["beforeStart","beforeMove","beforeScrollStart","scrollStart","scroll","beforeEnd","scrollEnd","resize","touchEnd","end","flick","scrollCancel","momentum","scrollTo","minDistanceScroll","scrollToElement","beforeRefresh"]),this.options=o;var i,n,s=this.options.bounce,r=s.left,h=s.right,a=s.top,s=s.bottom,a=(this.scrollBehaviorX=new yt(t,e,bt(o,"scrollX",[r,h],{size:"width",position:"left"})),this.scrollBehaviorY=new yt(t,e,bt(o,"scrollY",[a,s],{size:"height",position:"top"})),this.translater=new lt(this.content),this.animater=(r=this.content,h=this.translater,i=this.options,e=i.useTransition,o={},Object.defineProperty(o,"probeType",{enumerable:!0,configurable:!1,get:function(){return i.probeType}}),new(e?dt:vt)(r,h,o)),this.actionsHandler=new at(this.options.bindToTarget?this.content:t,(n=this.options,["click","bindToWrapper","disableMouse","disableTouch","preventDefault","stopPropagation","tagException","preventDefaultException","autoEndDistance"].reduce(function(t,e){return t[e]=n[e],t},{}))),this.actions=new Tt(this.scrollBehaviorX,this.scrollBehaviorY,this.actionsHandler,this.animater,this.options),this.resize.bind(this));this.resizeRegister=new b(window,[{name:"orientationchange",handler:a},{name:"resize",handler:a}]),this.registerTransitionEnd(),this.init()}e(L,wt=k),L.use=function(e){var t=e.pluginName;return L.plugins.some(function(t){return e===t.ctor})||(c(t)?i("Plugin Class must specify plugin's name in static property by 'pluginName' field."):(L.pluginsMap[t]=!0,L.plugins.push({name:t,applyOrder:e.applyOrder,ctor:e}))),L},L.prototype.setContent=function(t){var e=!1,o=!0,t=t.children[this.options.specifiedIndexAsContent];return t?(e=this.content!==t)&&(this.content=t):(i("The wrapper need at least one child element to be content element to scroll."),o=!1),{valid:o,contentChanged:e}},L.prototype.init=function(t){var e=this,t=((this.wrapper=t).isBScrollContainer=!0,this.scroller=new Pt(t,this.content,this.options),this.scroller.hooks.on(this.scroller.hooks.eventTypes.resize,function(){e.refresh()}),this.eventBubbling(),this.handleAutoBlur(),this.enable(),this.proxy(F),this.applyPlugins(),this.refreshWithoutReset(this.content),this.options),t={x:t.startX,y:t.startY};this.hooks.trigger(this.hooks.eventTypes.beforeInitialScrollTo,t)||this.scroller.scrollTo(t.x,t.y)},L.prototype.applyPlugins=function(){var o=this,i=this.options;L.plugins.sort(function(t,e){var o={pre:-1,post:1};return(t.applyOrder?o[t.applyOrder]:0)-(e.applyOrder?o[e.applyOrder]:0)}).forEach(function(t){var e=t.ctor;i[t.name]&&"function"==typeof e&&(o.plugins[t.name]=new e(o))})},L.prototype.handleAutoBlur=function(){this.options.autoBlur&&this.on(this.eventTypes.beforeScrollStart,function(){var t=document.activeElement;!t||"INPUT"!==t.tagName&&"TEXTAREA"!==t.tagName||t.blur()})},L.prototype.eventBubbling=function(){St(this.scroller.hooks,this,[this.eventTypes.beforeScrollStart,this.eventTypes.scrollStart,this.eventTypes.scroll,this.eventTypes.scrollEnd,this.eventTypes.scrollCancel,this.eventTypes.touchEnd,this.eventTypes.flick])},L.prototype.refreshWithoutReset=function(t){this.scroller.refresh(t),this.hooks.trigger(this.hooks.eventTypes.refresh,t),this.trigger(this.eventTypes.refresh,t)},L.prototype.proxy=function(t){var o=this;t.forEach(function(t){var e=t.key,t=t.sourceKey;nt(o,t,e)})},L.prototype.refresh=function(){var t=this.setContent(this.wrapper),e=t.contentChanged;t.valid&&(t=this.content,this.refreshWithoutReset(t),e&&(this.hooks.trigger(this.hooks.eventTypes.contentChanged,t),this.trigger(this.eventTypes.contentChanged,t)),this.scroller.resetPosition())},L.prototype.enable=function(){this.scroller.enable(),this.hooks.trigger(this.hooks.eventTypes.enable),this.trigger(this.eventTypes.enable)},L.prototype.disable=function(){this.scroller.disable(),this.hooks.trigger(this.hooks.eventTypes.disable),this.trigger(this.eventTypes.disable)},L.prototype.destroy=function(){this.hooks.trigger(this.hooks.eventTypes.destroy),this.trigger(this.eventTypes.destroy),this.scroller.destroy()},L.prototype.eventRegister=function(t){this.registerType(t)},L.plugins=[],L.pluginsMap={};var wt,O=L;function L(t,e){var o=wt.call(this,["refresh","contentChanged","enable","disable","beforeScrollStart","scrollStart","scroll","scrollEnd","scrollCancel","touchEnd","flick","destroy"])||this,t=W(t);return t?(o.plugins={},o.options=(new ht).merge(e).process(),o.setContent(t).valid&&(o.hooks=new k(["refresh","enable","disable","destroy","beforeInitialScrollTo","contentChanged"]),o.init(t))):i("Can not resolve the wrapper DOM."),o}function A(t,e){return new O(t,e)}A.use=O.use,A.plugins=O.plugins,A.pluginsMap=O.pluginsMap;o=A;t.Behavior=yt,t.CustomOptions=st,t.createBScroll=A,t.default=o,Object.defineProperty(t,"__esModule",{value:!0})}),"undefined"!=typeof window&&window.BScroll&&(window.BScroll=window.BScroll.default);
