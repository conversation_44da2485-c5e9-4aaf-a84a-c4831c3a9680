<template>
  <div class="p-6">
    <div class="mb-8 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">财务报表</h1>
        <p class="mt-1 text-sm text-gray-600">收入统计和财务分析</p>
      </div>
      <div class="flex space-x-3">
        <select
          v-model="selectedPeriod"
          class="block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        >
          <option value="month">本月</option>
          <option value="quarter">本季度</option>
          <option value="year">本年度</option>
          <option value="custom">自定义</option>
        </select>
        <button
          @click="exportReport"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <ArrowDownTrayIcon class="-ml-1 mr-2 h-5 w-5" />
          导出报表
        </button>
      </div>
    </div>

    <!-- 财务概览 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      <div v-for="metric in financialMetrics" :key="metric.name" class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <component :is="metric.icon" :class="['h-6 w-6', metric.iconColor]" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  {{ metric.name }}
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    {{ metric.prefix }}{{ metric.value }}
                  </div>
                  <p
                    v-if="metric.change"
                    :class="[
                      metric.change > 0 ? 'text-green-600' : 'text-red-600',
                      'ml-2 flex items-baseline text-sm font-semibold'
                    ]"
                  >
                    {{ metric.change > 0 ? '+' : '' }}{{ metric.change }}%
                  </p>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 收入趋势图 -->
    <div class="bg-white shadow rounded-lg p-6 mb-8">
      <h3 class="text-lg font-medium text-gray-900 mb-4">收入趋势</h3>
      <div class="h-64">
        <canvas ref="revenueChart"></canvas>
      </div>
    </div>

    <!-- 收入构成 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- 套餐收入分布 -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">套餐收入分布</h3>
        <div class="h-64">
          <canvas ref="planRevenueChart"></canvas>
        </div>
      </div>

      <!-- 支付方式统计 -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">支付方式统计</h3>
        <div class="h-64">
          <canvas ref="paymentMethodChart"></canvas>
        </div>
      </div>
    </div>

    <!-- 交易记录 -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          最近交易记录
        </h3>
      </div>
      <div class="border-t border-gray-200">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                交易ID
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                套餐
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                金额
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                支付方式
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                时间
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="transaction in transactions" :key="transaction.id">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {{ transaction.id }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ transaction.userName }}</div>
                <div class="text-sm text-gray-500">{{ transaction.userEmail }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ transaction.plan }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ¥{{ transaction.amount.toFixed(2) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ transaction.paymentMethod }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="[
                    'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                    transaction.status === 'completed' ? 'bg-green-100 text-green-800' :
                    transaction.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    transaction.status === 'failed' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  ]"
                >
                  {{ getStatusName(transaction.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(transaction.createdAt) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 财务摘要 -->
    <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">收入摘要</h3>
        <dl class="space-y-3">
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">订阅收入</dt>
            <dd class="text-sm text-gray-900 font-semibold">¥{{ revenueSummary.subscription.toLocaleString() }}</dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">一次性收入</dt>
            <dd class="text-sm text-gray-900 font-semibold">¥{{ revenueSummary.oneTime.toLocaleString() }}</dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">退款</dt>
            <dd class="text-sm text-red-600 font-semibold">-¥{{ revenueSummary.refunds.toLocaleString() }}</dd>
          </div>
          <div class="flex justify-between pt-3 border-t border-gray-200">
            <dt class="text-base font-medium text-gray-900">净收入</dt>
            <dd class="text-base text-gray-900 font-semibold">
              ¥{{ (revenueSummary.subscription + revenueSummary.oneTime - revenueSummary.refunds).toLocaleString() }}
            </dd>
          </div>
        </dl>
      </div>

      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">增长指标</h3>
        <dl class="space-y-3">
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">MRR (月经常性收入)</dt>
            <dd class="text-sm text-gray-900 font-semibold">¥{{ growthMetrics.mrr.toLocaleString() }}</dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">ARR (年经常性收入)</dt>
            <dd class="text-sm text-gray-900 font-semibold">¥{{ growthMetrics.arr.toLocaleString() }}</dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">ARPU (用户平均收入)</dt>
            <dd class="text-sm text-gray-900 font-semibold">¥{{ growthMetrics.arpu.toFixed(2) }}</dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">客户流失率</dt>
            <dd class="text-sm text-gray-900 font-semibold">{{ growthMetrics.churnRate }}%</dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { 
  CurrencyDollarIcon,
  ChartBarIcon,
  UserGroupIcon,
  CreditCardIcon,
  ArrowDownTrayIcon
} from '@heroicons/vue/24/outline'
import Chart from 'chart.js/auto'

// 财务指标
const financialMetrics = ref([
  {
    name: '总收入',
    value: '458,760',
    prefix: '¥',
    icon: CurrencyDollarIcon,
    iconColor: 'text-green-600',
    change: 23
  },
  {
    name: '净利润',
    value: '312,450',
    prefix: '¥',
    icon: ChartBarIcon,
    iconColor: 'text-blue-600',
    change: 18
  },
  {
    name: '付费用户',
    value: '3,842',
    prefix: '',
    icon: UserGroupIcon,
    iconColor: 'text-purple-600',
    change: 15
  },
  {
    name: '平均客单价',
    value: '119.5',
    prefix: '¥',
    icon: CreditCardIcon,
    iconColor: 'text-indigo-600',
    change: 5
  }
])

// 收入摘要
const revenueSummary = ref({
  subscription: 425600,
  oneTime: 45200,
  refunds: 12040
})

// 增长指标
const growthMetrics = ref({
  mrr: 142000,
  arr: 1704000,
  arpu: 119.5,
  churnRate: 2.3
})

// 交易记录
const transactions = ref([
  {
    id: 'TXN-001',
    userName: '张三',
    userEmail: '<EMAIL>',
    plan: '专业版 - 年付',
    amount: 2990,
    paymentMethod: '支付宝',
    status: 'completed',
    createdAt: new Date('2025-01-30T10:00:00')
  },
  {
    id: 'TXN-002',
    userName: '李四',
    userEmail: '<EMAIL>',
    plan: '基础版 - 月付',
    amount: 99,
    paymentMethod: '微信支付',
    status: 'completed',
    createdAt: new Date('2025-01-30T09:30:00')
  },
  {
    id: 'TXN-003',
    userName: '王五',
    userEmail: '<EMAIL>',
    plan: '企业版 - 年付',
    amount: 9990,
    paymentMethod: '银行转账',
    status: 'pending',
    createdAt: new Date('2025-01-30T08:45:00')
  },
  {
    id: 'TXN-004',
    userName: '赵六',
    userEmail: '<EMAIL>',
    plan: '专业版 - 月付',
    amount: 299,
    paymentMethod: '信用卡',
    status: 'completed',
    createdAt: new Date('2025-01-29T16:20:00')
  },
  {
    id: 'TXN-005',
    userName: '钱七',
    userEmail: '<EMAIL>',
    plan: '基础版 - 年付',
    amount: 990,
    paymentMethod: '支付宝',
    status: 'refunded',
    createdAt: new Date('2025-01-29T14:15:00')
  }
])

const selectedPeriod = ref('month')

// 图表引用
const revenueChart = ref<HTMLCanvasElement>()
const planRevenueChart = ref<HTMLCanvasElement>()
const paymentMethodChart = ref<HTMLCanvasElement>()

let revenueChartInstance: Chart | null = null
let planRevenueChartInstance: Chart | null = null
let paymentMethodChartInstance: Chart | null = null

const getStatusName = (status: string) => {
  const statusNames: Record<string, string> = {
    completed: '已完成',
    pending: '待处理',
    failed: '失败',
    refunded: '已退款'
  }
  return statusNames[status] || status
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const initCharts = () => {
  // 收入趋势图
  if (revenueChart.value) {
    revenueChartInstance = new Chart(revenueChart.value, {
      type: 'line',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: [{
          label: '总收入',
          data: [320000, 340000, 365000, 380000, 395000, 410000, 425000, 440000, 458000, 478000, 495000, 512000],
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          tension: 0.1
        }, {
          label: '净利润',
          data: [210000, 225000, 240000, 252000, 265000, 278000, 290000, 305000, 312000, 325000, 338000, 350000],
          borderColor: 'rgb(34, 197, 94)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }

  // 套餐收入分布
  if (planRevenueChart.value) {
    planRevenueChartInstance = new Chart(planRevenueChart.value, {
      type: 'doughnut',
      data: {
        labels: ['企业版', '专业版', '基础版', '免费版'],
        datasets: [{
          data: [45, 35, 18, 2],
          backgroundColor: [
            'rgba(34, 197, 94, 0.8)',
            'rgba(168, 85, 247, 0.8)',
            'rgba(59, 130, 246, 0.8)',
            'rgba(156, 163, 175, 0.8)'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }

  // 支付方式统计
  if (paymentMethodChart.value) {
    paymentMethodChartInstance = new Chart(paymentMethodChart.value, {
      type: 'bar',
      data: {
        labels: ['支付宝', '微信支付', '信用卡', '银行转账'],
        datasets: [{
          label: '交易笔数',
          data: [1250, 980, 560, 320],
          backgroundColor: 'rgba(99, 102, 241, 0.8)'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}

const exportReport = () => {
  console.log('导出财务报表')
  // 这里添加导出报表的逻辑
}

onMounted(() => {
  initCharts()
})

onBeforeUnmount(() => {
  revenueChartInstance?.destroy()
  planRevenueChartInstance?.destroy()
  paymentMethodChartInstance?.destroy()
})
</script>