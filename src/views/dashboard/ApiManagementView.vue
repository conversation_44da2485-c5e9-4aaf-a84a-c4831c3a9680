<template>
  <div class="api-management">
    <div class="page-header box qk-radius">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">API密钥管理</h1>
          <p class="page-description">管理您的API密钥，控制访问权限和使用限制</p>
        </div>
        <div class="header-actions">
          <button 
            @click="showCreateModal = true" 
            class="btn btn-primary"
            :disabled="isLoading"
          >
            <i class="ri-add-line"></i>
            创建新密钥
          </button>
        </div>
      </div>
      
      <div class="stats-overview">
        <div class="stat-item">
          <div class="stat-value">{{ apiKeys.length }}</div>
          <div class="stat-label">总密钥数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ activeKeysCount }}</div>
          <div class="stat-label">活跃密钥</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ totalUsage }}</div>
          <div class="stat-label">总调用次数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ totalTokensUsed }}</div>
          <div class="stat-label">总Token消耗</div>
        </div>
      </div>
    </div>

    <div class="api-keys-section box qk-radius">
      <div class="section-header">
        <div class="section-title">API密钥列表</div>
        <div class="section-controls">
          <div class="search-box">
            <i class="ri-search-line"></i>
            <input 
              v-model="searchQuery"
              type="text" 
              placeholder="搜索密钥名称..."
              class="search-input"
            >
          </div>
          <select v-model="statusFilter" class="status-filter">
            <option value="">全部状态</option>
            <option value="active">活跃</option>
            <option value="inactive">已禁用</option>
            <option value="expired">已过期</option>
          </select>
        </div>
      </div>
      
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <div>加载API密钥...</div>
      </div>
      
      <div v-else-if="filteredApiKeys.length === 0" class="empty-state">
        <i class="ri-key-line"></i>
        <div class="empty-title">
          {{ searchQuery || statusFilter ? '没有找到匹配的密钥' : '还没有API密钥' }}
        </div>
        <div class="empty-description">
          {{ searchQuery || statusFilter ? '尝试调整搜索条件' : '创建您的第一个API密钥来开始使用' }}
        </div>
        <button 
          v-if="!searchQuery && !statusFilter"
          @click="showCreateModal = true" 
          class="btn btn-primary"
        >
          <i class="ri-add-line"></i>
          创建API密钥
        </button>
      </div>
      
      <div v-else class="api-keys-list">
        <ApiKeyCard
          v-for="apiKey in filteredApiKeys"
          :key="apiKey.id"
          :api-key="apiKey"
          @toggle="handleToggleKey"
          @delete="handleDeleteKey"
          @copy="handleCopyKey"
        />
      </div>
    </div>

    <!-- 创建API密钥模态框 -->
    <div v-if="showCreateModal" class="modal-overlay" @click.self="showCreateModal = false">
      <div class="modal-content">
        <div class="modal-header">
          <h3>创建新的API密钥</h3>
          <button @click="showCreateModal = false" class="modal-close">
            <i class="ri-close-line"></i>
          </button>
        </div>
        
        <form @submit.prevent="handleCreateKey" class="modal-body">
          <div class="form-group">
            <label for="keyName">密钥名称</label>
            <input
              id="keyName"
              v-model="createForm.name"
              type="text"
              placeholder="为此密钥命名"
              required
              class="form-input"
            >
          </div>
          
          <div class="form-group">
            <label>权限设置</label>
            <div class="permissions-grid">
              <label 
                v-for="permission in permissionOptions"
                :key="permission.value"
                class="permission-checkbox"
              >
                <input
                  v-model="createForm.permissions"
                  :value="permission.value"
                  type="checkbox"
                >
                <span class="checkbox-custom"></span>
                <div class="permission-info">
                  <div class="permission-name">{{ permission.label }}</div>
                  <div class="permission-desc">{{ permission.description }}</div>
                </div>
              </label>
            </div>
          </div>
          
          <div class="form-group">
            <label for="tokenLimit">Token额度限制（每月）</label>
            <input
              id="tokenLimit"
              v-model.number="createForm.tokenLimit"
              type="number"
              min="1000"
              max="1000000"
              placeholder="50000"
              class="form-input"
            >
            <div class="form-hint">设置此密钥每月可使用的最大token数量</div>
          </div>
        </form>
        
        <div class="modal-footer">
          <button 
            @click="showCreateModal = false" 
            type="button" 
            class="btn btn-outline"
          >
            取消
          </button>
          <button 
            @click="handleCreateKey" 
            type="button" 
            class="btn btn-primary"
            :disabled="creating || !createForm.name || createForm.permissions.length === 0"
          >
            <i v-if="creating" class="ri-loader-line spinning"></i>
            <i v-else class="ri-add-line"></i>
            {{ creating ? '创建中...' : '创建密钥' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useApiKeysStore } from '@/stores/apiKeys'
import ApiKeyCard from '@/components/dashboard/cards/ApiKeyCard.vue'
import type { ApiKeyPermission } from '@/types/dashboard'

const apiKeysStore = useApiKeysStore()

const isLoading = ref(false)
const creating = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const showCreateModal = ref(false)

const createForm = ref({
  name: '',
  permissions: [] as ApiKeyPermission[],
  tokenLimit: 50000
})

const permissionOptions = [
  {
    value: 'read' as ApiKeyPermission,
    label: '读取权限',
    description: '允许读取数据和配置'
  },
  {
    value: 'write' as ApiKeyPermission,
    label: '写入权限',
    description: '允许创建和修改数据'
  },
  {
    value: 'delete' as ApiKeyPermission,
    label: '删除权限',
    description: '允许删除数据'
  },
  {
    value: 'admin' as ApiKeyPermission,
    label: '管理员权限',
    description: '完全访问权限'
  }
]

const apiKeys = computed(() => apiKeysStore.apiKeys)

const activeKeysCount = computed(() => 
  apiKeys.value.filter(key => key.status === 'active').length
)

const totalUsage = computed(() => 
  apiKeys.value.reduce((sum, key) => sum + key.usageCount, 0)
)

const totalTokensUsed = computed(() => {
  // 模拟计算总token消耗，基于使用次数的估算
  // 假设平均每次API调用消耗500个tokens
  return (totalUsage.value * 500).toLocaleString()
})

const filteredApiKeys = computed(() => {
  let filtered = apiKeys.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(key => 
      key.name.toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(key => key.status === statusFilter.value)
  }

  return filtered
})

const handleToggleKey = async (keyId: string) => {
  try {
    await apiKeysStore.toggleApiKey(keyId)
  } catch (error) {
    console.error('Failed to toggle API key:', error)
  }
}

const handleDeleteKey = async (keyId: string) => {
  try {
    await apiKeysStore.deleteApiKey(keyId)
  } catch (error) {
    console.error('Failed to delete API key:', error)
  }
}

const handleCopyKey = (key: string) => {
  // 显示成功提示或处理复制反馈
  console.log('Key copied:', key)
}

const handleCreateKey = async () => {
  if (!createForm.value.name || createForm.value.permissions.length === 0) {
    return
  }

  creating.value = true
  try {
    await apiKeysStore.createApiKey({
      name: createForm.value.name,
      permissions: createForm.value.permissions,
      tokenLimit: createForm.value.tokenLimit,
      status: 'active'
    })
    
    // 重置表单
    createForm.value = {
      name: '',
      permissions: [],
      tokenLimit: 50000
    }
    
    showCreateModal.value = false
  } catch (error) {
    console.error('Failed to create API key:', error)
  } finally {
    creating.value = false
  }
}

onMounted(async () => {
  isLoading.value = true
  try {
    await apiKeysStore.fetchApiKeys()
  } catch (error) {
    console.error('Failed to fetch API keys:', error)
  } finally {
    isLoading.value = false
  }
})
</script>

<style scoped>
.api-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.page-header {
  padding: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.page-description {
  color: var(--color-text-secondary);
  margin: 0;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  border: 1px solid transparent;
  border-radius: var(--radius);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  gap: 8px;
  min-height: 40px;
}

.btn i {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: var(--theme-color);
  color: white;
}

.btn-primary:hover {
  background: color-mix(in srgb, var(--theme-color) 90%, black);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-outline {
  background: white;
  border-color: var(--border-color-base);
  color: var(--color-text-primary);
}

.btn-outline:hover {
  border-color: var(--theme-color);
  color: var(--theme-color);
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: var(--bg-muted-color);
  border-radius: var(--radius);
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.api-keys-section {
  padding: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.section-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: var(--color-text-secondary);
}

.search-input {
  padding: 8px 12px 8px 36px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  width: 200px;
}

.status-filter {
  padding: 8px 12px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  background: white;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color-base);
  border-top: 3px solid var(--theme-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.empty-description {
  margin-bottom: 24px;
}

.api-keys-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: var(--radius);
  width: 90vw;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color-base);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.modal-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
}

.modal-close:hover {
  color: var(--color-text-primary);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-color-base);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  font-size: 14px;
}

.form-input:focus {
  border-color: var(--theme-color);
  outline: none;
}

.form-hint {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: 4px;
}

.permissions-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.permission-checkbox {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  padding: 12px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  transition: all 0.2s;
}

.permission-checkbox:hover {
  border-color: var(--theme-color);
}

.permission-checkbox input {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color-base);
  border-radius: 3px;
  margin-right: 12px;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.permission-checkbox input:checked + .checkbox-custom {
  background: var(--theme-color);
  border-color: var(--theme-color);
}

.permission-checkbox input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.permission-info {
  flex: 1;
}

.permission-name {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.permission-desc {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.spinning {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .section-controls {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>