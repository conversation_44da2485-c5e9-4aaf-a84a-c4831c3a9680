@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dashboard原型样式文件 - 必须在Tailwind之后导入以确保优先级 */
@import '../assets/dashboard/style-1.3.6.css';
@import '../assets/dashboard/style-1.3.61.css';
@import '../assets/dashboard/style-1.3.62.css';
@import '../assets/dashboard/flickity.css';
@import '../assets/dashboard/fancybox.css';
@import '../assets/dashboard/account.css';
@import '../assets/dashboard/mobile.css';
@import '../assets/dashboard/remixicon.css';

/* 确保图标字体正确显示 */
[class^="ri-"], [class*=" ri-"] {
  font-family: 'remixicon' !important;
  font-style: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局CSS变量定义 - 确保在全局作用域生效 */
:root {
  --site-width: 2560px;
  --wrapper-width: 1750px;
  --sidebar-width: 300px;
  --radius: 4px;
  --btn-radius: 20px;
  --gap: 16px;
  --opacity: 0.1;
  
  --top-menu-width: 2560px;
  --top-menu-height: 56px;
  --top-menu-bg-color: #ffffff;
  --top-menu-text-color: #333333;
  
  --theme-color: #3858f6;
  --color-primary: var(--theme-color);
  --color-text-primary: #333333;
  --color-text-regular: #61666d;
  --color-text-secondary: #9499a0;
  --color-text-placeholder: #c9ccd0;
  --border-color-base: #f7f7f7;
  --border-color-muted: #f7f7f7;
  --color-white: #ffffff;
  --bg-body-color: #f6f7f9;
  --bg-main-color: var(--color-white);
  --bg-text-color: rgba(56, 88, 246, var(--opacity, 0.1));
  --bg-muted-color: var(--bg-body-color);
}

/* 暗色主题 */
.dark-theme {
  --top-menu-bg-color: #313348;
  --top-menu-text-color: #d1d5db;
  --color-primary: var(--theme-color);
  --color-text-primary: #d1d5db;
  --color-text-regular: #9ca3af;
  --color-text-secondary: #6b7280;
  --color-text-placeholder: #4b5563;
  --border-color-base: #3b3d56;
  --border-color-muted: #3b3d56;
  --color-white: #fff;
  --bg-body-color: #2a2b3d;
  --bg-main-color: #313348;
  --bg-muted-color: var(--bg-body-color);
}

/* Dashboard特有样式覆盖Tailwind */
.dashboard-container body {
  background-color: var(--bg-body-color) !important;
  margin: 0;
  padding: 0;
}

/* 确保Dashboard布局样式优先级 */
.dashboard-container .site {
  min-height: 100vh !important;
  background-color: var(--bg-body-color) !important;
  max-width: 100% !important;
  width: 100% !important;
  margin: 0 auto !important;
  display: flex !important;
  flex-direction: column !important;
}

.dashboard-container .site-content {
  flex: 1 !important;
  width: 100% !important;
}

.dashboard-container .wrapper {
  max-width: var(--wrapper-width) !important;
  margin: 0 auto !important;
  padding: 0 16px !important;
}

.dashboard-container .box {
  background-color: var(--bg-main-color) !important;
  border-radius: var(--radius) !important;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05) !important;
}

.dashboard-container .mg-b {
  margin-bottom: var(--gap) !important;
}

.dashboard-container .mg-r {
  margin-right: var(--gap) !important;
}

.dashboard-container .qk-flex {
  display: flex !important;
  align-items: center !important;
}

/* 右侧内容区域样式修复 */
.dashboard-container .account-page-right {
  flex: 1 1 0% !important;
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
}

.dashboard-container .account-page-right > div {
  display: flex !important;
  flex-direction: column !important;
  grid-gap: var(--gap) !important;
  width: 100% !important;
  height: 100% !important;
}

/* 确保右侧内容区域的直接子元素应用box样式 */
.dashboard-container .account-page-right .dashboard-main > div {
  background-color: var(--bg-main-color) !important;
  border-radius: var(--radius) !important;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05) !important;
  padding: 24px !important;
  margin-bottom: var(--gap) !important;
}

.dashboard-container .qk-radius {
  border-radius: var(--radius) !important;
}

.dashboard-container .text-ellipsis {
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}

.dashboard-container .w-h {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

.dashboard-container .no-hover {
  text-decoration: none !important;
}

.dashboard-container .title {
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
}

/* 确保Flexbox辅助类正确工作 */
.dashboard-container .qk-flex {
  display: flex !important;
  align-items: center !important;
}

/* 账户页面左侧边栏特殊样式 */
.dashboard-container .account-mobile-hidden {
  display: block !important;
}

@media (max-width: 768px) {
  .dashboard-container .account-mobile-hidden {
    display: none !important;
  }
}

/* 自定义基础样式 */
@layer base {
  html {
    font-family: 'Basier circle', system-ui, sans-serif;
  }
  
  body {
    @apply antialiased;
  }
}

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply px-6 py-3 border-2 border-blue-600 text-blue-600 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-lg p-6;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* 自定义工具类 */
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .bg-gradient-brand {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .shadow-brand {
    box-shadow: 0 10px 25px -3px rgba(102, 126, 234, 0.1), 0 4px 6px -2px rgba(102, 126, 234, 0.05);
  }
  
  /* 确保网格布局在首次加载时正确显示 */
  .stats-grid {
    display: grid !important;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1.5rem;
  }
  
  @media (min-width: 640px) {
    .stats-grid-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
    .stats-grid-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
  }
  
  @media (min-width: 1280px) {
    .stats-grid-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
    }
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.6s ease-out;
}

.animate-bounce-custom {
  animation: bounce 2s infinite;
}

/* 响应式设计增强 */
@media (max-width: 640px) {
  .container-custom {
    @apply px-4;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .card {
    @apply bg-gray-800 text-white;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
}

/* 可访问性增强 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply border-2 border-blue-800;
  }
  
  .btn-secondary {
    @apply border-2 border-blue-800;
  }
}
