import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useAdminAuthStore } from '@/stores/adminAuth'

export const adminAuthGuard = async (
  to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const adminAuthStore = useAdminAuthStore()
  
  console.log('Admin auth guard - store state:', {
    isInitialized: adminAuthStore.isInitialized,
    isAuthenticated: adminAuthStore.isAuthenticated,
    adminUser: adminAuthStore.adminUser,
    adminToken: adminAuthStore.adminToken
  })
  
  // 等待认证状态初始化
  while (!adminAuthStore.isInitialized) {
    await new Promise(resolve => setTimeout(resolve, 50))
  }
  
  // 如果是登录页面，已登录则重定向到管理后台
  if (to.path === '/admin/login') {
    if (adminAuthStore.isAuthenticated) {
      next('/admin')
    } else {
      next()
    }
    return
  }
  
  // 检查是否已登录
  if (!adminAuthStore.isAuthenticated) {
    next({
      path: '/admin/login',
      query: { redirect: to.fullPath }
    })
    return
  }
  
  // 检查权限
  const requiredPermission = to.meta.permission as string | undefined
  if (requiredPermission && !adminAuthStore.hasPermission(requiredPermission)) {
    // 没有权限，跳转到 403 页面或仪表盘
    next('/admin')
    return
  }
  
  // 检查角色要求
  const requiredRole = to.meta.role as string | undefined
  if (requiredRole) {
    const adminUser = adminAuthStore.adminUser
    if (!adminUser) {
      next('/admin/login')
      return
    }
    
    if (requiredRole === 'super_admin' && !adminAuthStore.isSuperAdmin) {
      next('/admin')
      return
    }
    
    if (requiredRole === 'admin' && !adminAuthStore.isAdmin) {
      next('/admin')
      return
    }
  }
  
  next()
}