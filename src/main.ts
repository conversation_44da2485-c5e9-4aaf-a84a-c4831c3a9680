import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { useAuthStore } from '@/stores/auth'

// 导入全局样式
import './styles/main.css'

// 导入dashboard样式 - 按正确顺序导入
import '@/assets/dashboard/style-1.3.61.css'  // 主样式文件
import '@/assets/dashboard/style-1.3.6.css'   // 基础样式
import '@/assets/dashboard/account.css'       // 账户页面样式
import '@/assets/dashboard/remixicon.css'     // 图标字体

console.log('🚀 Starting Vue app initialization...')

const app = createApp(App)

// 安装插件
const pinia = createPinia()
app.use(pinia)
app.use(router)

// 初始化认证状态
console.log('🔐 Initializing authentication...')
const authStore = useAuthStore()
// 确保认证状态在应用启动时初始化
authStore.initAuth().then(() => {
  console.log('🔐 Authentication initialization completed')
}).catch(error => {
  console.error('🔐 Authentication initialization failed:', error)
})

// 开发模式下的调试信息
if (import.meta.env.DEV) {
  // 添加全局调试工具
  ;(window as any).__VUE_APP__ = app
  ;(window as any).__AUTH_STORE__ = authStore
  
  console.log('🚀 Vue app initialized in development mode')
}

// 基础错误处理
app.config.errorHandler = (error: Error, instance, info: string) => {
  console.error('Vue Error:', error)
  console.error('Component:', instance)
  console.error('Info:', info)
}

console.log('🚀 Mounting Vue app to #app...')
app.mount('#app')
