<template>
  <div class="dashboard-main">
    <div class="dashboard-header box qk-radius">
      <div class="section-title">控制台概览</div>
      <div class="overview-stats">
        <div class="stats-grid">
          <StatsCard
            title="本月费用"
            :value="`￥${overview.billing.currentMonthCost}`"
            :change="monthlyChange"
            icon="ri-money-dollar-circle-line"
            color="primary"
          />
          <StatsCard
            title="API调用量"
            :value="formatNumber(overview.usage.monthlyUsage)"
            :subtitle="`成功率 ${(overview.usage.successRate * 100).toFixed(1)}%`"
            icon="ri-line-chart-line"
            color="success"
          />
          <StatsCard
            title="当前套餐"
            :value="getPlanDisplayName(overview.user.plan)"
            :subtitle="planStatusText"
            icon="ri-vip-crown-line"
            color="warning"
          />
          <StatsCard
            title="API密钥"
            :value="`${overview.apiKeys.active}/${overview.apiKeys.total}`"
            subtitle="活跃/总数"
            icon="ri-key-line"
            color="info"
          />
        </div>
      </div>
    </div>

    <div class="dashboard-content">
      <div class="dashboard-row">
        <div class="dashboard-col-8">
          <div class="usage-chart-section box qk-radius">
            <div class="section-header">
              <h3 class="section-title">使用趋势</h3>
              <div class="chart-controls">
                <select v-model="chartPeriod" @change="updateChartData">
                  <option value="7">最近7天</option>
                  <option value="30">最近30天</option>
                  <option value="90">最近90天</option>
                </select>
              </div>
            </div>
            <UsageChart :data="chartData" :period="chartPeriod" />
          </div>
        </div>
        
        <div class="dashboard-col-4">
          <div class="quick-actions box qk-radius">
            <h3 class="section-title">快速操作</h3>
            <div class="action-buttons">
              <router-link to="/dashboard/api-management" class="action-btn">
                <i class="ri-add-line"></i>
                创建API密钥
              </router-link>
              <router-link to="/dashboard/usage-stats" class="action-btn">
                <i class="ri-bar-chart-line"></i>
                查看详细统计
              </router-link>
              <router-link to="/dashboard/subscription" class="action-btn">
                <i class="ri-vip-crown-line"></i>
                升级套餐
              </router-link>
              <button @click="downloadReport" class="action-btn">
                <i class="ri-download-line"></i>
                下载使用报告
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-row">
        <div class="dashboard-col-12">
          <div class="api-keys-preview box qk-radius">
            <div class="section-header">
              <h3 class="section-title">API密钥概览</h3>
              <router-link to="/dashboard/api-management" class="view-all-link">
                查看全部
              </router-link>
            </div>
            <div class="api-keys-list">
              <ApiKeyCard
                v-for="apiKey in recentApiKeys"
                :key="apiKey.id"
                :api-key="apiKey"
                compact
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import StatsCard from '@/components/dashboard/cards/StatsCard.vue'
import UsageChart from '@/components/dashboard/charts/UsageChart.vue'
import ApiKeyCard from '@/components/dashboard/cards/ApiKeyCard.vue'
import { ApiKeyStatus, ApiKeyPermission, PlanType, PlanStatus, type ApiKey, type UsageChartData } from '@/types/dashboard'

// 简化的类型定义 - 避免复杂导入
interface SimpleOverview {
  user: {
    plan: PlanType
    planStatus: PlanStatus
  }
  usage: {
    monthlyUsage: number
    successRate: number
    todayUsage: number
    totalUsage: number
    averageResponseTime: number
    errorCount: number
    errorRate: number
  }
  apiKeys: {
    total: number
    active: number
  }
  billing: {
    currentMonthCost: number
    lastMonthCost: number
    totalSpent: number
  }
}

// 模拟数据存储 - 避免复杂的store依赖
const chartPeriod = ref(30)
const chartData = ref<UsageChartData>([])

// 使用简化的数据结构
const overview = ref<SimpleOverview>({
  user: { plan: PlanType.FREE, planStatus: PlanStatus.ACTIVE },
  usage: { 
    monthlyUsage: 1250, 
    successRate: 0.98,
    todayUsage: 45,
    totalUsage: 15600,
    averageResponseTime: 120,
    errorCount: 23,
    errorRate: 0.02
  },
  apiKeys: { total: 3, active: 2 },
  billing: { 
    currentMonthCost: 15.50, 
    lastMonthCost: 12.30, 
    totalSpent: 180.75 
  }
})

const monthlyChange = computed(() => {
  const current = overview.value.billing.currentMonthCost
  const last = overview.value.billing.lastMonthCost
  if (last === 0) return 0
  return ((current - last) / last * 100)
})

const planStatusText = computed(() => {
  const status = overview.value.user.planStatus
  const statusMap: Record<PlanStatus, string> = {
    [PlanStatus.ACTIVE]: '活跃中',
    [PlanStatus.INACTIVE]: '未激活',
    [PlanStatus.EXPIRED]: '已过期',
    [PlanStatus.CANCELLED]: '已取消'
  }
  return statusMap[status] || status
})

// 模拟API密钥数据
const recentApiKeys = ref<ApiKey[]>([
  {
    id: '1',
    name: '主要API密钥',
    key: 'sk-*********************',
    status: ApiKeyStatus.ACTIVE,
    permissions: [ApiKeyPermission.READ, ApiKeyPermission.WRITE],
    createdAt: new Date(Date.now() - 86400000 * 5),
    lastUsed: new Date(Date.now() - 3600000),
    usageCount: 850,
    rateLimit: 100
  },
  {
    id: '2',
    name: '测试密钥',
    key: 'sk-*********************',
    status: ApiKeyStatus.ACTIVE,
    permissions: [ApiKeyPermission.READ],
    createdAt: new Date(Date.now() - 86400000 * 12),
    lastUsed: new Date(Date.now() - 86400000 * 2),
    usageCount: 234,
    rateLimit: 50
  }
])

const updateChartData = async () => {
  try {
    // 模拟图表数据更新
    const mockData = Array.from({ length: chartPeriod.value }, (_, i) => ({
      date: new Date(Date.now() - (chartPeriod.value - i) * 86400000).toISOString().split('T')[0],
      timestamp: Date.now() - (chartPeriod.value - i) * 86400000,
      requests: Math.floor(Math.random() * 100) + 20,
      errors: Math.floor(Math.random() * 5),
      responseTime: Math.floor(Math.random() * 200) + 50,
      errorRate: 0
    }))
    chartData.value = mockData
  } catch (error) {
    console.error('Failed to update chart data:', error)
  }
}

const downloadReport = async () => {
  try {
    // 模拟下载报告
    console.log('Downloading usage report...')
    alert('使用报告下载功能开发中...')
  } catch (error) {
    console.error('Failed to download report:', error)
  }
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
  return num.toString()
}

const getPlanDisplayName = (planType: PlanType): string => {
  const planNames: Record<PlanType, string> = {
    [PlanType.FREE]: '免费版',
    [PlanType.BASIC]: '基础版',
    [PlanType.PRO]: '专业版',
    [PlanType.ENTERPRISE]: '企业版'
  }
  return planNames[planType] || planType
}

onMounted(async () => {
  // 初始化数据
  await updateChartData()
  console.log('Dashboard initialized with mock data')
})
</script>

<style scoped>
.dashboard-main {
  padding: 0;
  max-width: 100%;
}

.dashboard-header {
  margin-bottom: 20px;
  padding: 20px;
}

.section-title {
  font-size: 22px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 14px;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dashboard-row {
  display: flex;
  gap: 20px;
}

.dashboard-col-8 {
  flex: 0 0 calc(72% - 12px);
}

.dashboard-col-6 {
  flex: 0 0 calc(50% - 10px);
}

.dashboard-col-4 {
  flex: 0 0 calc(28% - 12px);
}

.dashboard-col-12 {
  flex: 1;
}

.usage-chart-section,
.api-keys-preview {
  padding: 20px;
}

.quick-actions {
  padding: 16px;
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header .section-title {
  margin-bottom: 0;
  font-size: 18px;
}

.chart-controls select {
  padding: 6px 10px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  background: white;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 10px 14px;
  background: var(--bg-muted-color);
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  color: var(--color-text-primary);
  text-decoration: none;
  transition: all 0.2s;
  cursor: pointer;
  font-size: 14px;
}

.action-btn:hover {
  background: var(--bg-text-color);
  border-color: var(--theme-color);
  color: var(--theme-color);
}

.action-btn i {
  margin-right: 8px;
}

.view-all-link {
  color: var(--theme-color);
  text-decoration: none;
  font-size: 14px;
}

.view-all-link:hover {
  text-decoration: underline;
}

.api-keys-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.no-subscription {
  text-align: center;
  padding: 20px;
  color: var(--color-text-secondary);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .dashboard-col-8 {
    flex: 0 0 calc(70% - 12px);
  }
  
  .dashboard-col-4 {
    flex: 0 0 calc(30% - 12px);
  }
}

@media (max-width: 768px) {
  .dashboard-row {
    flex-direction: column;
  }
  
  .dashboard-col-8,
  .dashboard-col-6,
  .dashboard-col-4,
  .dashboard-col-12 {
    flex: 1;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-header,
  .usage-chart-section,
  .quick-actions,
  .api-keys-preview {
    padding: 16px;
  }
}
</style>