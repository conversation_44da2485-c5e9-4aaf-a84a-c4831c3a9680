/**
 * XSS防护和数据清理工具
 * 防止跨站脚本攻击，确保用户输入数据安全
 */

// ==================== 基础清理函数 ====================

/**
 * HTML编码特殊字符
 */
export const escapeHtml = (text: string): string => {
  if (!text || typeof text !== 'string') return ''
  
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

/**
 * 移除HTML标签
 */
export const stripHtmlTags = (html: string): string => {
  if (!html || typeof html !== 'string') return ''
  
  const doc = new DOMParser().parseFromString(html, 'text/html')
  return doc.body.textContent || ''
}

/**
 * 清理并验证URL
 */
export const sanitizeUrl = (url: string): string => {
  if (!url || typeof url !== 'string') return ''
  
  // 移除危险协议
  const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:', 'about:']
  const lowercaseUrl = url.toLowerCase().trim()
  
  for (const protocol of dangerousProtocols) {
    if (lowercaseUrl.startsWith(protocol)) {
      return ''
    }
  }
  
  // 确保URL格式正确
  try {
    const parsed = new URL(url, window.location.origin)
    return parsed.href
  } catch {
    // 如果不是完整URL，可能是相对路径
    if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
      return url
    }
    return ''
  }
}

/**
 * 清理用户名
 */
export const sanitizeUsername = (username: string): string => {
  if (!username || typeof username !== 'string') return ''
  
  return username
    .trim()
    .replace(/[<>\"'&]/g, '') // 移除基本的HTML字符
    .substring(0, 50) // 限制长度
}

/**
 * 清理邮箱地址
 */
export const sanitizeEmail = (email: string): string => {
  if (!email || typeof email !== 'string') return ''
  
  return email
    .trim()
    .toLowerCase()
    .replace(/[<>\"'&]/g, '')
    .substring(0, 254) // RFC 5321标准邮箱最大长度
}

/**
 * 清理文本内容
 */
export const sanitizeText = (text: string, maxLength = 1000): string => {
  if (!text || typeof text !== 'string') return ''
  
  return escapeHtml(text.trim()).substring(0, maxLength)
}

// ==================== 高级清理工具 ====================

/**
 * 深度清理对象中的所有字符串字段
 */
export const sanitizeObject = <T extends Record<string, any>>(obj: T, options?: {
  textFields?: (keyof T)[]
  emailFields?: (keyof T)[]
  urlFields?: (keyof T)[]
  usernameFields?: (keyof T)[]
}): T => {
  if (!obj || typeof obj !== 'object') return obj
  
  const result = { ...obj } as any
  const opts = options || {}
  
  for (const [key, value] of Object.entries(result)) {
    if (typeof value === 'string') {
      if (opts.emailFields?.includes(key)) {
        result[key] = sanitizeEmail(value)
      } else if (opts.urlFields?.includes(key)) {
        result[key] = sanitizeUrl(value)
      } else if (opts.usernameFields?.includes(key)) {
        result[key] = sanitizeUsername(value)
      } else if (opts.textFields?.includes(key)) {
        result[key] = sanitizeText(value)
      } else {
        result[key] = sanitizeText(value, 500) // 默认限制
      }
    } else if (Array.isArray(value)) {
      result[key] = value.map(item => 
        typeof item === 'string' ? sanitizeText(item) : item
      )
    } else if (value && typeof value === 'object') {
      result[key] = sanitizeObject(value as any, options) as any
    }
  }
  
  return result as T
}

/**
 * 验证和清理用户输入的对象
 */
export const sanitizeUserInput = (input: any): any => {
  if (!input) return input
  
  if (typeof input === 'string') {
    return sanitizeText(input)
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeUserInput)
  }
  
  if (typeof input === 'object') {
    return sanitizeObject(input, {
      emailFields: ['email', 'contactEmail', 'replyTo'],
      urlFields: ['website', 'avatar', 'profileUrl', 'callback'],
      usernameFields: ['username', 'displayName', 'name'],
      textFields: ['description', 'bio', 'content', 'message', 'note']
    })
  }
  
  return input
}

// ==================== 验证工具 ====================

/**
 * 验证邮箱格式
 */
export const isValidEmail = (email: string): boolean => {
  if (!email || typeof email !== 'string') return false
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 254
}

/**
 * 验证用户名格式
 */
export const isValidUsername = (username: string): boolean => {
  if (!username || typeof username !== 'string') return false
  
  return username.length >= 2 && 
         username.length <= 50 && 
         /^[a-zA-Z0-9_-]+$/.test(username)
}

/**
 * 验证URL格式
 */
export const isValidUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false
  
  try {
    new URL(url)
    return !url.toLowerCase().includes('javascript:')
  } catch {
    return false
  }
}

// ==================== Vue组合式函数 ====================

/**
 * Vue组合式函数：安全的用户数据处理
 */
export const useSafeUserData = () => {
  return {
    sanitizeText,
    sanitizeEmail,
    sanitizeUsername,
    sanitizeUrl,
    sanitizeObject,
    sanitizeUserInput,
    escapeHtml,
    stripHtmlTags,
    isValidEmail,
    isValidUsername,
    isValidUrl
  }
}

// ==================== 常量定义 ====================

/**
 * 常见的危险字符模式
 */
export const DANGEROUS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
  /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
  /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /on\w+\s*=/gi // 事件处理器如onclick等
]

/**
 * 检查文本是否包含危险模式
 */
export const containsDangerousContent = (text: string): boolean => {
  if (!text || typeof text !== 'string') return false
  
  return DANGEROUS_PATTERNS.some(pattern => pattern.test(text))
}

// ==================== 导出所有功能 ====================

export default {
  escapeHtml,
  stripHtmlTags,
  sanitizeUrl,
  sanitizeUsername,
  sanitizeEmail,
  sanitizeText,
  sanitizeObject,
  sanitizeUserInput,
  isValidEmail,
  isValidUsername,
  isValidUrl,
  containsDangerousContent,
  useSafeUserData,
  DANGEROUS_PATTERNS
}