<template>
  <div class="admin-dashboard">
    <div class="dashboard-header mg-b">
      <h1 class="dashboard-title">管理员仪表盘</h1>
      <p class="dashboard-subtitle">系统运行状态和关键指标概览</p>
    </div>

    <!-- 统计卡片 - 使用原生StatsCard组件 -->
    <div class="stats-grid mg-b">
      <StatsCard
        v-for="stat in stats"
        :key="stat.title"
        :title="stat.title"
        :value="stat.value"
        :icon="stat.icon"
        :color="stat.color"
        :change="stat.change"
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid mg-b">
      <!-- API 调用趋势 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">API 调用趋势</h3>
        </div>
        <div class="chart-content">
          <canvas ref="apiChart" class="chart-canvas"></canvas>
        </div>
      </div>

      <!-- 用户增长趋势 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">用户增长趋势</h3>
        </div>
        <div class="chart-content">
          <canvas ref="userChart" class="chart-canvas"></canvas>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="activity-card">
      <div class="card-header">
        <h3 class="card-title">最近活动</h3>
      </div>
      <div class="activity-list">
        <div v-for="(activity, index) in activities" :key="activity.id" class="activity-item">
          <div class="activity-icon" :class="`activity-icon--${activity.type}`">
            <i :class="getActivityIcon(activity.type)"></i>
          </div>
          <div class="activity-content">
            <div class="activity-text">{{ activity.content }}</div>
            <div class="activity-time">{{ activity.time }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import StatsCard from '@/components/dashboard/cards/StatsCard.vue'
import Chart from 'chart.js/auto'

// 统计数据
const stats = ref<Array<{
  title: string
  value: string
  icon: string
  color: 'primary' | 'success' | 'warning' | 'info' | 'danger'
  change: number
}>>([
  { 
    title: '总用户数', 
    value: '12,485', 
    icon: 'ri-user-line',
    color: 'primary',
    change: 12
  },
  { 
    title: '活跃 API 密钥', 
    value: '8,735', 
    icon: 'ri-key-line',
    color: 'success',
    change: 8
  },
  { 
    title: '今日 API 调用', 
    value: '1.2M', 
    icon: 'ri-bar-chart-line',
    color: 'info',
    change: -3
  },
  { 
    title: '付费订阅', 
    value: '3,842', 
    icon: 'ri-vip-crown-line',
    color: 'warning',
    change: 15
  }
])

// 最近活动
const activities = ref([
  {
    id: 1,
    type: 'user',
    content: '新用户注册: <EMAIL>',
    time: '5分钟前'
  },
  {
    id: 2,
    type: 'api',
    content: 'API密钥创建: sk_test_****3f4a',
    time: '10分钟前'
  },
  {
    id: 3,
    type: 'subscription',
    content: '用户升级到专业版套餐',
    time: '25分钟前'
  },
  {
    id: 4,
    type: 'user',
    content: '用户账户被禁用: <EMAIL>',
    time: '1小时前'
  },
  {
    id: 5,
    type: 'api',
    content: 'API调用限制触发: sk_test_****8b2c',
    time: '2小时前'
  }
])

// 图表引用
const apiChart = ref<HTMLCanvasElement>()
const userChart = ref<HTMLCanvasElement>()

let apiChartInstance: Chart | null = null
let userChartInstance: Chart | null = null

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    user: 'ri-user-line',
    api: 'ri-key-line',
    subscription: 'ri-vip-crown-line',
    system: 'ri-settings-line'
  }
  return iconMap[type] || 'ri-information-line'
}

const initCharts = () => {
  // API调用趋势图
  if (apiChart.value) {
    apiChartInstance = new Chart(apiChart.value, {
      type: 'line',
      data: {
        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        datasets: [{
          label: 'API调用量',
          data: [1200, 1900, 3000, 5000, 4200, 3800, 2100],
          borderColor: 'var(--theme-color)',
          backgroundColor: 'rgba(56, 88, 246, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'var(--border-color-base)'
            }
          },
          x: {
            grid: {
              color: 'var(--border-color-base)'
            }
          }
        }
      }
    })
  }

  // 用户增长趋势图
  if (userChart.value) {
    userChartInstance = new Chart(userChart.value, {
      type: 'bar',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
          label: '新增用户',
          data: [450, 620, 780, 890, 1200, 950],
          backgroundColor: 'rgba(34, 197, 94, 0.8)',
          borderRadius: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,  
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'var(--border-color-base)'
            }
          },
          x: {
            grid: {
              color: 'var(--border-color-base)'
            }
          }
        }
      }
    })
  }
}

onMounted(() => {
  initCharts()
})

onBeforeUnmount(() => {
  apiChartInstance?.destroy()
  userChartInstance?.destroy()
})
</script>

<style scoped>
.admin-dashboard {
  padding: 24px;
  max-width: none;
}

.dashboard-header {
  text-align: left;
  margin-bottom: 32px;
}

.dashboard-title {
  font-size: 32px;
  font-weight: 800;
  color: var(--color-text-primary);
  margin-bottom: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-subtitle {
  font-size: 18px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(480px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card, .activity-card {
  background: var(--bg-main-color);
  border: 1px solid var(--border-color-base);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.chart-card:hover, .activity-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.chart-header, .card-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color-base);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02), rgba(118, 75, 162, 0.02));
}

.chart-title, .card-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0;
}

.chart-content {
  padding: 24px;
  height: 350px;
}

.chart-canvas {
  width: 100% !important;
  height: 100% !important;
}

.activity-list {
  padding: 0;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color-base);
  transition: background-color 0.2s ease;
}

.activity-item:hover {
  background: rgba(102, 126, 234, 0.02);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18px;
  font-size: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-icon--user {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.25));
  color: #3b82f6;
}

.activity-icon--api {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.25));
  color: #22c55e;
}

.activity-icon--subscription {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.25));
  color: #f59e0b;
}

.activity-icon--system {
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.15), rgba(156, 163, 175, 0.25));
  color: #9ca3af;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 15px;
  color: var(--color-text-primary);
  margin-bottom: 6px;
  font-weight: 500;
}

.activity-time {
  font-size: 13px;
  color: var(--color-text-secondary);
  font-weight: 400;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .chart-content {
    height: 300px;
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;
  }
  
  .dashboard-title {
    font-size: 28px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-content {
    height: 250px;
  }
  
  .activity-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}
</style>