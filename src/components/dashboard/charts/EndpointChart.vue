<template>
  <div class="endpoint-chart">
    <div v-if="loading" class="chart-loading">
      <div class="loading-spinner"></div>
      <div>加载端点数据...</div>
    </div>
    
    <div v-else-if="chartData.length === 0" class="chart-empty">
      <i class="ri-route-line"></i>
      <div>暂无端点数据</div>
    </div>
    
    <div v-else class="chart-container">
      <div class="endpoint-list">
        <div 
          v-for="(item, index) in topEndpoints"
          :key="item.endpoint"
          class="endpoint-item"
        >
          <div class="endpoint-info">
            <div class="endpoint-name">{{ item.endpoint }}</div>
            <div class="endpoint-method" :class="item.method.toLowerCase()">
              {{ item.method }}
            </div>
          </div>
          
          <div class="endpoint-stats">
            <div class="usage-bar">
              <div 
                class="usage-fill" 
                :style="{ width: `${(item.usage / maxUsage) * 100}%` }"
              ></div>
            </div>
            <div class="usage-text">
              {{ formatNumber(item.usage) }}次
              <span class="usage-percentage">({{ item.percentage.toFixed(1) }}%)</span>
            </div>
          </div>
          
          <div class="endpoint-metrics">
            <div class="metric">
              <span class="metric-label">成功率</span>
              <span class="metric-value" :class="{ 
                'high': item.successRate > 0.95,
                'medium': item.successRate > 0.8,
                'low': item.successRate <= 0.8
              }">
                {{ (item.successRate * 100).toFixed(1) }}%
              </span>
            </div>
            <div class="metric">
              <span class="metric-label">响应时间</span>
              <span class="metric-value">{{ item.avgResponseTime }}ms</span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-if="chartData.length > 5" class="show-more">
        <button @click="showAll = !showAll" class="show-more-btn">
          {{ showAll ? '收起' : `显示全部 ${chartData.length} 个端点` }}
          <i :class="showAll ? 'ri-arrow-up-s-line' : 'ri-arrow-down-s-line'"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface EndpointData {
  endpoint: string
  method: string
  usage: number
  percentage: number
  successRate: number
  avgResponseTime: number
}

interface Props {
  data: EndpointData[]
  loading: boolean
}

const props = defineProps<Props>()

const showAll = ref(false)

const chartData = computed(() => props.data || [])

const maxUsage = computed(() => {
  if (chartData.value.length === 0) return 1
  return Math.max(...chartData.value.map(item => item.usage))
})

const topEndpoints = computed(() => {
  const sorted = [...chartData.value].sort((a, b) => b.usage - a.usage)
  return showAll.value ? sorted : sorted.slice(0, 5)
})

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
  return num.toString()
}
</script>

<style scoped>
.endpoint-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-loading,
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color-base);
  border-top: 3px solid var(--theme-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-empty i {
  font-size: 48px;
  margin-bottom: 16px;
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.endpoint-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.endpoint-item {
  padding: 16px;
  background: var(--bg-muted-color);
  border-radius: var(--radius);
  transition: all 0.2s;
}

.endpoint-item:hover {
  background: rgba(var(--rgb-theme-color, 56, 88, 246), 0.05);
  border: 1px solid rgba(var(--rgb-theme-color, 56, 88, 246), 0.2);
}

.endpoint-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.endpoint-name {
  font-weight: 600;
  color: var(--color-text-primary);
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.endpoint-method {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.endpoint-method.get {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.endpoint-method.post {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.endpoint-method.put {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.endpoint-method.delete {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.endpoint-method.patch {
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.endpoint-stats {
  margin-bottom: 12px;
}

.usage-bar {
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.usage-fill {
  height: 100%;
  background: var(--theme-color);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.usage-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.usage-percentage {
  font-weight: 400;
  color: var(--color-text-secondary);
}

.endpoint-metrics {
  display: flex;
  gap: 24px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.metric-value.high {
  color: #22c55e;
}

.metric-value.medium {
  color: #f59e0b;
}

.metric-value.low {
  color: #ef4444;
}

.show-more {
  margin-top: 16px;
  text-align: center;
}

.show-more-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  color: var(--color-text-regular);
  cursor: pointer;
  transition: all 0.2s;
}

.show-more-btn:hover {
  border-color: var(--theme-color);
  color: var(--theme-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .endpoint-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .endpoint-name {
    font-size: 12px;
  }
  
  .endpoint-metrics {
    flex-direction: column;
    gap: 12px;
  }
  
  .usage-text {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>