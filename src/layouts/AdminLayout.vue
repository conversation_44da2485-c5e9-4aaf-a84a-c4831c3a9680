<template>
  <div class="dashboard-container">
    <div id="page" class="site">
      <AdminHeader />
      
      <div id="content" class="site-content">
        <div class="content-wrapper">
          <div id="account" class="account wrapper">
            <div class="account-header mg-b account-mobile-hidden box">
              <div class="mask-wrapper" style="background-image: url(/src/assets/dashboard/image/fill_w1440_h288_g0_mark_20201223055552.png);"></div>
              <div class="account-panel">
                <div class="account-profile">
                  <div class="left-user-info">
                    <div class="user-avatar">
                      <img :src="adminUser?.avatar || '/src/assets/dashboard/picture/a6e.png'" class="avatar-face w-h" alt="管理员头像" />
                    </div>
                    <div class="user-info">
                      <div class="user-info-name">
                        <span class="user-name">{{ adminUser?.username || '管理员' }}</span>
                        <span class="user-lv"><img src="/src/assets/dashboard/picture/LV2.png" class="lv-img-icon"></span>
                      </div>
                      <div class="desc text-ellipsis">
                        <div class="verify">
                          <span>{{ getRoleName(adminUser?.role) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="right-user-action">
                    <div class="profile-primary-button no-hover">管理后台</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="account-page-content">
              <AdminSidebar />
              
              <div class="account-page-right">
                <router-view />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAdminAuthStore } from '@/stores/adminAuth'
import AdminHeader from '@/components/admin/layout/AdminHeader.vue'
import AdminSidebar from '@/components/admin/layout/AdminSidebar.vue'

const adminAuthStore = useAdminAuthStore()
const adminUser = computed(() => adminAuthStore.adminUser)

const getRoleName = (role?: string): string => {
  const roleMap: Record<string, string> = {
    'super_admin': '超级管理员',
    'admin': '管理员', 
    'operator': '操作员'
  }
  return roleMap[role || ''] || '管理员'
}
</script>

<style scoped>
/* Dashboard布局优化 - 管理员版本 */
.dashboard-container {
  min-height: 100vh;
}

/* 修复header在dashboard中的位置问题 */
.dashboard-container :deep(.header .header-top) {
  position: relative !important;
  top: auto !important;
  max-width: 100% !important;
  width: 100% !important;
  z-index: auto !important;
  background: var(--bg-main-color);
  border-bottom: 1px solid var(--border-color-base);
}

.dashboard-container :deep(.header .header-top-wrap) {
  max-width: 1560px !important;
  padding: 0 24px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--top-menu-height);
}

.dashboard-container :deep(.left-entry) {
  display: flex;
  align-items: center;
  gap: 24px;
}

.dashboard-container :deep(.right-entry) {
  display: flex;
  align-items: center;
}

/* 调整账户页面内容布局比例 - 增加30%宽度 */
.dashboard-container :deep(.account-page-content) {
  display: flex;
  gap: 24px;
  max-width: 1560px; /* 从1200px增加30%到1560px */
  margin: 0 auto;
}

/* 优化左侧边栏宽度 - 调整比例 */
.dashboard-container :deep(.account-page-left) {
  width: 20% !important;
  min-width: 280px !important;
  max-width: 320px;
  flex-shrink: 0;
}

/* 确保右侧内容区域充分利用空间 */
.dashboard-container :deep(.account-page-right) {
  flex: 1;
  width: auto !important;
}

/* 优化左侧边栏内部间距和内容 */
.dashboard-container :deep(.account-page-left > div) {
  padding: 20px !important;
}

/* 优化头部区域 */
.dashboard-container :deep(.account-header) {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 优化用户信息显示 */
.dashboard-container :deep(.account-profile) {
  padding: 30px 24px;
}

/* 优化统计卡片布局 */
.dashboard-container :deep(.user-assets) {
  gap: 12px;
}

.dashboard-container :deep(.admin-stats-card),
.dashboard-container :deep(.admin-stats-card2) {
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 优化VIP卡片高度 - 管理员不需要VIP */
.dashboard-container :deep(.admin-card) {
  height: 75px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 优化统计项目的显示 */
.dashboard-container :deep(.counts-item .single-count-item) {
  min-width: auto;
  padding: 2px;
}

/* 优化导航链接的间距和显示 */
.dashboard-container :deep(.tab-links) {
  margin-top: 16px;
}

.dashboard-container :deep(.tab-links .link-item) {
  padding: 0 20px !important;
  height: 48px !important;
  line-height: 48px !important;
  margin-bottom: 8px;
  font-size: 15px;
  border-radius: 8px;
  justify-content: space-between;
  display: flex;
  align-items: center;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.dashboard-container :deep(.tab-links .link-item:hover) {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 确保导航文字和图标正确显示 */
.dashboard-container :deep(.link-title) {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: visible;
}

.dashboard-container :deep(.link-title i) {
  margin-right: 12px !important;
  font-size: 20px !important;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.dashboard-container :deep(.tab-links .link-item.active .link-title i) {
  opacity: 1;
  transform: scale(1.1);
}

/* 用户头像相关样式 - 增大尺寸 */
.user-avatar {
  --avatar-size: 128px;
  width: var(--avatar-size);
  height: var(--avatar-size);
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 4px solid rgba(255, 255, 255, 0.2);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.lv-img-icon {
  height: 18px;
  width: auto;
  margin-left: 8px;
}

.user-name {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.profile-primary-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  color: white;
  cursor: pointer;
  line-height: 44px;
  height: 44px;
  text-align: center;
  width: 100%;
  display: block;
  padding: 0 20px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s ease;
}

.profile-primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .dashboard-container :deep(.account-page-left) {
    width: 24% !important;
    min-width: 220px !important;
  }
}

@media (max-width: 1200px) {
  .dashboard-container :deep(.account-page-left) {
    width: 26% !important;
    min-width: 200px !important;
  }
}

@media (max-width: 768px) {
  .dashboard-container :deep(.account-page-content) {
    flex-direction: column;
  }
  
  .dashboard-container :deep(.account-page-left) {
    width: 100% !important;
    min-width: auto !important;
    max-width: none;
  }
  
  .dashboard-container :deep(.header .header-top-wrap) {
    padding: 0 16px;
  }
}
</style>