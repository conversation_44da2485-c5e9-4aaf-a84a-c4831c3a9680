<template>
  <div class="p-6">
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">使用统计监控</h1>
      <p class="mt-1 text-sm text-gray-600">系统使用情况和性能监控</p>
    </div>

    <!-- 时间范围选择 -->
    <div class="mb-6 flex justify-between items-center">
      <div class="flex space-x-2">
        <button
          v-for="period in periods"
          :key="period.value"
          @click="selectedPeriod = period.value"
          :class="[
            'px-4 py-2 text-sm font-medium rounded-md',
            selectedPeriod === period.value
              ? 'bg-indigo-600 text-white'
              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
          ]"
        >
          {{ period.label }}
        </button>
      </div>
      <button
        @click="exportData"
        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        <ArrowDownTrayIcon class="-ml-1 mr-2 h-5 w-5" />
        导出数据
      </button>
    </div>

    <!-- 统计概览 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      <div v-for="stat in overviewStats" :key="stat.name" class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <component :is="stat.icon" class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  {{ stat.name }}
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    {{ stat.value }}
                  </div>
                  <p
                    v-if="stat.trend"
                    :class="[
                      stat.trend > 0 ? 'text-green-600' : 'text-red-600',
                      'ml-2 flex items-baseline text-sm font-semibold'
                    ]"
                  >
                    {{ stat.trend > 0 ? '+' : '' }}{{ stat.trend }}%
                  </p>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 gap-6 mb-8">
      <!-- API 调用趋势图 -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">API 调用趋势</h3>
        <div class="h-64">
          <canvas ref="apiCallsChart"></canvas>
        </div>
      </div>

      <!-- 响应时间分布 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">响应时间分布</h3>
          <div class="h-64">
            <canvas ref="responseTimeChart"></canvas>
          </div>
        </div>

        <!-- 错误率统计 -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">错误率统计</h3>
          <div class="h-64">
            <canvas ref="errorRateChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Top 用户列表 -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Top 10 活跃用户</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  用户
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  API调用次数
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  平均响应时间
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  错误率
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  套餐
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="user in topUsers" :key="user.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img
                        class="h-10 w-10 rounded-full"
                        :src="user.avatar || `https://ui-avatars.com/api/?name=${user.name}&background=random`"
                        :alt="user.name"
                      >
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">
                        {{ user.name }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ user.email }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ user.apiCalls.toLocaleString() }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ user.avgResponseTime }}ms
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    :class="[
                      'text-sm',
                      user.errorRate < 1 ? 'text-green-600' :
                      user.errorRate < 5 ? 'text-yellow-600' :
                      'text-red-600'
                    ]"
                  >
                    {{ user.errorRate }}%
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    :class="[
                      'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                      user.plan === 'enterprise' ? 'bg-green-100 text-green-800' :
                      user.plan === 'pro' ? 'bg-purple-100 text-purple-800' :
                      user.plan === 'basic' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    ]"
                  >
                    {{ getPlanName(user.plan) }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import { 
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowDownTrayIcon
} from '@/utils/icons'
import Chart from 'chart.js/auto'

// 时间范围选项
const periods = [
  { label: '今天', value: '1d' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
]

const selectedPeriod = ref('7d')

// 统计概览数据
const overviewStats = ref([
  {
    name: '总API调用',
    value: '12.5M',
    icon: ChartBarIcon,
    trend: 15
  },
  {
    name: '平均响应时间',
    value: '124ms',
    icon: ClockIcon,
    trend: -8
  },
  {
    name: '错误率',
    value: '0.12%',
    icon: ExclamationTriangleIcon,
    trend: -25
  },
  {
    name: '成功率',
    value: '99.88%',
    icon: CheckCircleIcon,
    trend: 2
  }
])

// Top 用户数据
const topUsers = ref([
  {
    id: '1',
    name: '科技公司A',
    email: '<EMAIL>',
    avatar: '',
    apiCalls: 2456789,
    avgResponseTime: 98,
    errorRate: 0.05,
    plan: 'enterprise'
  },
  {
    id: '2',
    name: '创业公司B',
    email: '<EMAIL>',
    avatar: '',
    apiCalls: 1234567,
    avgResponseTime: 135,
    errorRate: 0.12,
    plan: 'pro'
  },
  {
    id: '3',
    name: '个人开发者C',
    email: '<EMAIL>',
    avatar: '',
    apiCalls: 876543,
    avgResponseTime: 156,
    errorRate: 0.23,
    plan: 'basic'
  },
  {
    id: '4',
    name: '企业D',
    email: '<EMAIL>',
    avatar: '',
    apiCalls: 654321,
    avgResponseTime: 112,
    errorRate: 0.08,
    plan: 'enterprise'
  },
  {
    id: '5',
    name: '工作室E',
    email: '<EMAIL>',
    avatar: '',
    apiCalls: 543210,
    avgResponseTime: 143,
    errorRate: 0.15,
    plan: 'pro'
  }
])

// 图表引用
const apiCallsChart = ref<HTMLCanvasElement>()
const responseTimeChart = ref<HTMLCanvasElement>()
const errorRateChart = ref<HTMLCanvasElement>()

let apiCallsChartInstance: Chart | null = null
let responseTimeChartInstance: Chart | null = null
let errorRateChartInstance: Chart | null = null

const getPlanName = (plan: string) => {
  const planNames: Record<string, string> = {
    free: '免费版',
    basic: '基础版',
    pro: '专业版',
    enterprise: '企业版'
  }
  return planNames[plan] || plan
}

const initCharts = () => {
  // API 调用趋势图
  if (apiCallsChart.value) {
    apiCallsChartInstance = new Chart(apiCallsChart.value, {
      type: 'line',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        datasets: [{
          label: 'API调用次数',
          data: [1200000, 1900000, 3000000, 5000000, 8200000, 10500000, 12500000],
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }

  // 响应时间分布
  if (responseTimeChart.value) {
    responseTimeChartInstance = new Chart(responseTimeChart.value, {
      type: 'bar',
      data: {
        labels: ['0-50ms', '50-100ms', '100-200ms', '200-500ms', '500ms+'],
        datasets: [{
          label: '请求数量',
          data: [45, 30, 20, 4, 1],
          backgroundColor: 'rgba(99, 102, 241, 0.8)'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }

  // 错误率统计
  if (errorRateChart.value) {
    errorRateChartInstance = new Chart(errorRateChart.value, {
      type: 'doughnut',
      data: {
        labels: ['成功', '4xx错误', '5xx错误'],
        datasets: [{
          data: [99.88, 0.08, 0.04],
          backgroundColor: [
            'rgba(34, 197, 94, 0.8)',
            'rgba(251, 146, 60, 0.8)',
            'rgba(239, 68, 68, 0.8)'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}

const exportData = () => {
  console.log('导出数据')
  // 这里添加导出数据的逻辑
}

onMounted(() => {
  initCharts()
})

onBeforeUnmount(() => {
  apiCallsChartInstance?.destroy()
  responseTimeChartInstance?.destroy()
  errorRateChartInstance?.destroy()
})

watch(selectedPeriod, () => {
  // 更新图表数据
  console.log('Period changed:', selectedPeriod.value)
})
</script>