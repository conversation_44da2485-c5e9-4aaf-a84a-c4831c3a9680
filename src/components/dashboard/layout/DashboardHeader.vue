<template>
  <header class="bg-white/95 backdrop-blur-xl shadow-lg border-b border-white/20 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-r from-blue-50/60 to-indigo-50/60"></div>
    
    <div class="relative flex h-16 items-center justify-between px-6">
      <!-- 左侧：菜单按钮和页面标题 -->
      <div class="flex items-center space-x-4">
        <button
          @click="$emit('toggle-sidebar')"
          class="lg:hidden p-2 rounded-xl text-gray-500 hover:text-indigo-600 hover:bg-white/60 backdrop-blur-sm transition-all duration-200 shadow-md hover:shadow-lg"
        >
          <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
        
        <div>
          <h1 class="text-xl font-bold text-gray-900">{{ pageTitle }}</h1>
          <p class="text-sm text-gray-500 font-medium">{{ getCurrentTimeGreeting() }}</p>
        </div>
      </div>

      <!-- 右侧：功能按钮和用户菜单 -->
      <div class="flex items-center space-x-3">
        <!-- 快速统计 -->
        <div class="hidden md:flex items-center space-x-4 px-4 py-2 bg-white/80 backdrop-blur-xl rounded-2xl border border-white/50 shadow-lg">
          <div class="text-center">
            <div class="text-lg font-bold text-indigo-600">1,234</div>
            <div class="text-xs text-gray-500">今日调用</div>
          </div>
          <div class="w-px h-8 bg-gray-200"></div>
          <div class="text-center">
            <div class="text-lg font-bold text-green-600">98.5%</div>
            <div class="text-xs text-gray-500">成功率</div>
          </div>
        </div>

        <!-- 通知按钮 -->
        <button class="relative p-2 text-gray-500 hover:text-indigo-600 hover:bg-white/60 backdrop-blur-sm rounded-xl transition-all duration-200 shadow-md hover:shadow-lg">
          <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.5-3.5a5.99 5.99 0 000-7l1.5-1.5a1 1 0 00-1.4-1.4L15 5.5a5.99 5.99 0 00-7 0L6.5 4a1 1 0 00-1.4 1.4L6.5 7a5.99 5.99 0 000 7L3 17h5m7 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
          <span class="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full animate-pulse"></span>
        </button>

        <!-- 快速操作 -->
        <button class="hidden sm:flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-blue-600 rounded-2xl hover:from-indigo-600 hover:to-blue-700 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:scale-105 backdrop-blur-sm">
          <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <span>新建API密钥</span>
        </button>

        <!-- 用户头像和下拉菜单 -->
        <div class="relative" ref="userMenuRef">
          <button
            @click="userMenuOpen = !userMenuOpen"
            class="flex items-center space-x-3 p-2 rounded-2xl hover:bg-white/80 backdrop-blur-xl transition-all duration-200 border border-transparent hover:border-white/50 hover:shadow-lg"
          >
            <div class="relative">
              <img 
                :src="user?.avatar" 
                :alt="user?.name"
                class="h-8 w-8 rounded-xl object-cover shadow-lg border-2 border-white"
              />
              <div class="absolute -bottom-1 -right-1 h-3 w-3 bg-green-400 rounded-full border-2 border-white shadow-sm"></div>
            </div>
            <div class="hidden sm:block text-left">
              <p class="text-sm font-semibold text-gray-900">{{ user?.name }}</p>
              <p class="text-xs text-gray-500">在线</p>
            </div>
            <svg 
              :class="['h-4 w-4 text-gray-400 transition-transform duration-200', userMenuOpen ? 'rotate-180' : '']"
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          <!-- 用户下拉菜单 -->
          <div
            v-show="userMenuOpen"
            class="absolute right-0 mt-2 w-56 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl ring-1 ring-black ring-opacity-5 z-50 border border-white/20 overflow-hidden"
          >
            <!-- 用户信息头部 -->
            <div class="px-4 py-3 bg-gradient-to-r from-indigo-50/60 to-blue-50/60 border-b border-gray-100">
              <div class="flex items-center space-x-3">
                <img 
                  :src="user?.avatar" 
                  :alt="user?.name"
                  class="h-10 w-10 rounded-xl object-cover shadow-lg"
                />
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-semibold text-gray-900 truncate">{{ user?.name }}</p>
                  <p class="text-xs text-gray-500 truncate">{{ user?.email }}</p>
                </div>
              </div>
            </div>

            <!-- 菜单项 -->
            <div class="py-2">
              <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-200">
                <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                个人资料
              </a>
              <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-200">
                <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                设置
              </a>
              <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-200">
                <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                帮助中心
              </a>
              <hr class="my-2 border-gray-100">
              <button 
                @click="handleLogout"
                class="flex w-full items-center px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
              >
                <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

interface Props {
  pageTitle: string
}

defineProps<Props>()
defineEmits<{
  'toggle-sidebar': []
}>()

const router = useRouter()
const authStore = useAuthStore()

const userMenuOpen = ref(false)
const userMenuRef = ref<HTMLElement>()

const user = computed(() => authStore.user)

const getCurrentTimeGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
  userMenuOpen.value = false
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    userMenuOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>