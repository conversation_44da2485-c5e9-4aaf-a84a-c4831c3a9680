import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import AdminAPI, { type AdminUser, type AdminLoginData } from '@/api/admin'
import { sanitizeObject, sanitizeUsername } from '@/utils/security'
import { AppError, ERROR_CODES, errorLogger, safeAsync } from '@/utils/errorHandler'

// ==================== 接口定义 ====================

interface AdminAuthState {
  user: AdminUser | null
  token: string | null
  refreshToken: string | null
  isLoading: boolean
  isInitialized: boolean
  loginAttempts: number
  lastLoginAttempt: number
}

// ==================== Store 定义 ====================

export const useAdminAuthStore = defineStore('adminAuth', () => {
  // ========== 状态 ==========
  const adminUser = ref<AdminUser | null>(null)
  const adminToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const isLoading = ref(true)
  const isInitialized = ref(false)
  const loginAttempts = ref(0)
  const lastLoginAttempt = ref(0)

  // ========== 计算属性 ==========
  const isAuthenticated = computed(() => !!adminUser.value && !!adminToken.value)
  const isSuperAdmin = computed(() => adminUser.value?.role === 'super_admin')
  const isAdmin = computed(() => adminUser.value?.role === 'admin' || adminUser.value?.role === 'super_admin')

  // ========== 权限检查 ==========
  const hasPermission = (permission: string): boolean => {
    if (!adminUser.value) return false
    if (adminUser.value.role === 'super_admin') return true
    return adminUser.value.permissions.includes(permission) || adminUser.value.permissions.includes('*')
  }

  const hasRole = (role: string): boolean => {
    if (!adminUser.value) return false
    return adminUser.value.role === role || (role === 'admin' && adminUser.value.role === 'super_admin')
  }

  // ========== 安全工具 ==========
  
  /**
   * 清理和验证用户输入
   */
  const sanitizeLoginData = (loginData: AdminLoginData): AdminLoginData => {
    return {
      username: sanitizeUsername(loginData.username),
      password: loginData.password // 密码不进行HTML转义，但应该进行长度验证
    }
  }

  /**
   * 验证登录数据
   */
  const validateLoginData = (loginData: AdminLoginData): string[] => {
    const errors: string[] = []
    
    if (!loginData.username || loginData.username.length < 2) {
      errors.push('用户名长度至少为2个字符')
    }
    
    if (!loginData.password || loginData.password.length < 6) {
      errors.push('密码长度至少为6个字符')
    }
    
    if (loginData.password && loginData.password.length > 128) {
      errors.push('密码长度不能超过128个字符')
    }
    
    return errors
  }

  /**
   * 检查登录频率限制
   */
  const checkRateLimit = (): boolean => {
    const now = Date.now()
    const timeSinceLastAttempt = now - lastLoginAttempt.value
    
    // 如果尝试次数超过5次，需要等待5分钟
    if (loginAttempts.value >= 5 && timeSinceLastAttempt < 5 * 60 * 1000) {
      return false
    }
    
    // 重置计数器（如果超过1小时）
    if (timeSinceLastAttempt > 60 * 60 * 1000) {
      loginAttempts.value = 0
    }
    
    return true
  }

  // ========== 存储管理 ==========
  
  /**
   * 安全地保存认证数据
   */
  const saveAuthData = (userData: AdminUser, authToken: string, refreshTokenValue?: string) => {
    try {
      // 清理用户数据
      const cleanUserData = sanitizeObject(userData, {
        emailFields: ['email'],
        usernameFields: ['username']
      })
      
      adminUser.value = cleanUserData
      adminToken.value = authToken
      refreshToken.value = refreshTokenValue || null
      
      // 存储到localStorage
      localStorage.setItem('admin_token', authToken)
      localStorage.setItem('admin_user', JSON.stringify(cleanUserData))
      
      if (refreshTokenValue) {
        localStorage.setItem('admin_refresh_token', refreshTokenValue)
      }
      
      // 记录成功登录
      errorLogger.log(
        new AppError('Admin login successful', 'AUTH_SUCCESS', 'low', {
          component: 'AdminAuthStore',
          action: 'login',
          userId: userData.id
        })
      )
    } catch (error) {
      const appError = new AppError(
        '保存认证数据失败',
        ERROR_CODES.DATA_CORRUPTION_ERROR,
        'high',
        { component: 'AdminAuthStore', action: 'saveAuthData' }
      )
      errorLogger.log(appError)
      throw appError
    }
  }

  /**
   * 清除认证数据
   */
  const clearAuthData = () => {
    adminUser.value = null
    adminToken.value = null
    refreshToken.value = null
    
    // 清除本地存储
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
    localStorage.removeItem('admin_refresh_token')
    
    // 重置登录尝试计数
    loginAttempts.value = 0
    lastLoginAttempt.value = 0
  }

  // ========== 认证操作 ==========
  
  /**
   * 初始化认证状态
   */
  const initAuth = async (): Promise<void> => {
    if (isInitialized.value) return
    
    isLoading.value = true
    
    try {
      const savedToken = localStorage.getItem('admin_token')
      const savedUser = localStorage.getItem('admin_user')
      const savedRefreshToken = localStorage.getItem('admin_refresh_token')
      
      if (savedToken && savedUser) {
        try {
          const userData = JSON.parse(savedUser)
          
          // 清理恢复的用户数据
          const cleanUserData = sanitizeObject(userData, {
            emailFields: ['email'],
            usernameFields: ['username']
          })
          
          adminToken.value = savedToken
          adminUser.value = cleanUserData
          refreshToken.value = savedRefreshToken
          
          console.log('Admin auth restored:', { username: cleanUserData.username })
        } catch (parseError) {
          console.error('Failed to parse saved admin data:', parseError)
          clearAuthData()
          
          errorLogger.log(
            new AppError(
              '解析保存的认证数据失败',
              ERROR_CODES.DATA_CORRUPTION_ERROR,
              'medium',
              { component: 'AdminAuthStore', action: 'initAuth' }
            )
          )
        }
      }
    } catch (error) {
      console.error('Failed to initialize admin auth:', error)
      clearAuthData()
      
      errorLogger.log(
        new AppError(
          '初始化管理员认证失败',
          ERROR_CODES.UNKNOWN_ERROR,
          'medium',
          { component: 'AdminAuthStore', action: 'initAuth' }
        )
      )
    } finally {
      isLoading.value = false
      isInitialized.value = true
    }
  }

  /**
   * 管理员登录
   */
  const login = async (loginData: AdminLoginData): Promise<AdminUser> => {
    // 速率限制检查
    if (!checkRateLimit()) {
      const error = new AppError(
        '登录尝试过于频繁，请稍后再试',
        ERROR_CODES.RATE_LIMIT_ERROR,
        'medium',
        { component: 'AdminAuthStore', action: 'login' }
      )
      errorLogger.log(error)
      throw error
    }

    // 更新尝试记录
    loginAttempts.value++
    lastLoginAttempt.value = Date.now()

    // 验证输入数据
    const validationErrors = validateLoginData(loginData)
    if (validationErrors.length > 0) {
      const error = new AppError(
        validationErrors.join(', '),
        ERROR_CODES.VALIDATION_ERROR,
        'low',
        { component: 'AdminAuthStore', action: 'login' }
      )
      errorLogger.log(error)
      throw error
    }

    // 清理输入数据
    const cleanLoginData = sanitizeLoginData(loginData)

    isLoading.value = true
    
    try {
      const response = await AdminAPI.login(cleanLoginData)
      
      if (response.success && response.data) {
        // 重置登录尝试计数
        loginAttempts.value = 0
        
        // 保存认证数据
        saveAuthData(response.data.user, response.data.token, response.data.refreshToken)
        
        console.log('Admin login successful')
        return response.data.user
      } else {
        throw new AppError(
          response.message || '登录失败',
          ERROR_CODES.AUTHENTICATION_ERROR,
          'medium'
        )
      }
    } catch (error: any) {
      const appError = error instanceof AppError 
        ? error 
        : new AppError(
            error.message || '登录过程中发生错误',
            ERROR_CODES.AUTHENTICATION_ERROR,
            'medium',
            { component: 'AdminAuthStore', action: 'login' }
          )
      
      errorLogger.log(appError)
      throw appError
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 验证权限（服务端验证）
   */
  const verifyPermission = async (permission: string): Promise<boolean> => {
    const result = await safeAsync(
      () => AdminAPI.verifyPermission(permission).then(res => res.data),
      { component: 'AdminAuthStore', action: 'verifyPermission', permission },
      false
    )
    return result ?? false
  }

  /**
   * 刷新访问令牌
   */
  const refreshAccessToken = async (): Promise<boolean> => {
    if (!refreshToken.value) return false

    const result = await safeAsync(async () => {
      const response = await AdminAPI.refreshToken(refreshToken.value!)
      
      if (response.success) {
        adminToken.value = response.data.token
        localStorage.setItem('admin_token', response.data.token)
        return true
      }
      
      return false
    }, { component: 'AdminAuthStore', action: 'refreshToken' }, false)
    
    return result ?? false
  }

  /**
   * 登出
   */
  const logout = () => {
    console.log('Admin logging out')
    
    errorLogger.log(
      new AppError('Admin logout', 'AUTH_LOGOUT', 'low', {
        component: 'AdminAuthStore',
        action: 'logout',
        userId: adminUser.value?.id
      })
    )
    
    clearAuthData()
  }

  /**
   * 更新管理员信息
   */
  const updateAdminUser = (userData: Partial<AdminUser>) => {
    if (adminUser.value) {
      // 清理更新数据
      const cleanUserData = sanitizeObject(userData, {
        emailFields: ['email'],
        usernameFields: ['username']
      })
      
      adminUser.value = { ...adminUser.value, ...cleanUserData }
      localStorage.setItem('admin_user', JSON.stringify(adminUser.value))
    }
  }

  // ========== 初始化 ==========
  
  // 自动初始化认证状态
  initAuth()

  // ========== 返回 ==========
  
  return {
    // 状态
    adminUser: computed(() => adminUser.value),
    adminToken: computed(() => adminToken.value),
    isLoading: computed(() => isLoading.value),
    isInitialized: computed(() => isInitialized.value),
    isAuthenticated,
    isSuperAdmin,
    isAdmin,
    
    // 方法
    initAuth,
    login,
    logout,
    updateAdminUser,
    clearAuthData,
    hasPermission,
    hasRole,
    verifyPermission,
    refreshAccessToken
  }
})