import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'cursor-landing',
      component: () => import('../views/CursorLandingView.vue')
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        requiresGuest: true
      }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
      meta: {
        requiresGuest: true
      }
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: () => import('../views/ForgotPasswordView.vue'),
      meta: {
        requiresGuest: true
      }
    },
    // 用户中心路由组 - 恢复动态导入以优化性能
    {
      path: '/dashboard',
      component: () => import('../components/dashboard/layout/DashboardLayout.vue'),
      meta: {
        requiresAuth: true
      },
      children: [
        {
          path: '',
          name: 'dashboard',
          component: () => import('../views/dashboard/DashboardView.vue'),
          meta: { title: '控制台概览' }
        },
        {
          path: 'api-management',
          name: 'api-management',
          component: () => import('../views/dashboard/ApiManagementView.vue'),
          meta: { title: 'API管理' }
        },
        {
          path: 'usage-stats',
          name: 'usage-stats',
          component: () => import('../views/dashboard/UsageStatsView.vue'),
          meta: { title: '使用统计' }
        },
        {
          path: 'subscription',
          name: 'subscription',
          component: () => import('../views/dashboard/SubscriptionView.vue'),
          meta: { title: '套餐管理' }
        }
      ]
    }
  ]
})

// 改进的路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 等待认证状态初始化完成
  if (!authStore.isInitialized) {
    console.log('Waiting for auth initialization...')
    await authStore.initAuth()
  }

  const isAuthenticated = authStore.isAuthenticated
  const requiresAuth = to.meta.requiresAuth
  const requiresGuest = to.meta.requiresGuest

  console.log('Route guard check:', {
    to: to.path,
    isAuthenticated,
    requiresAuth,
    requiresGuest,
    userEmail: authStore.user?.email
  })

  // 检查需要认证的路由
  if (requiresAuth && !isAuthenticated) {
    console.log('Redirecting to login - auth required but not authenticated')
    // 保存用户原本想访问的页面，登录后跳转回去
    const redirectPath = to.fullPath
    next({
      path: '/login',
      query: redirectPath !== '/dashboard' ? { redirect: redirectPath } : undefined
    })
    return
  }

  // 检查需要访客身份的路由（已登录用户不应该访问）
  if (requiresGuest && isAuthenticated) {
    console.log('Redirecting to dashboard - already authenticated')
    // 从登录页跳转过来的话，根据redirect参数决定去向
    const redirectPath = to.query?.redirect as string
    next(redirectPath || '/dashboard')
    return
  }

  console.log('Route guard passed, proceeding to:', to.path)
  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('Router error:', error)
  // 可以在这里添加错误上报逻辑
})

export default router
