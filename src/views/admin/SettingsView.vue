<template>
  <div class="p-6">
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">系统配置</h1>
      <p class="mt-1 text-sm text-gray-600">管理系统全局设置和配置</p>
    </div>

    <!-- 配置选项卡 -->
    <div class="mb-6">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              activeTab === tab.id
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
              'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
            ]"
          >
            {{ tab.name }}
          </button>
        </nav>
      </div>
    </div>

    <!-- 基础设置 -->
    <div v-if="activeTab === 'general'" class="space-y-6">
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            网站信息
          </h3>
          <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <label class="block text-sm font-medium text-gray-700">
                网站名称
              </label>
              <input
                v-model="settings.siteName"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="sm:col-span-3">
              <label class="block text-sm font-medium text-gray-700">
                网站域名
              </label>
              <input
                v-model="settings.siteDomain"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="sm:col-span-6">
              <label class="block text-sm font-medium text-gray-700">
                网站描述
              </label>
              <textarea
                v-model="settings.siteDescription"
                rows="3"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <div class="sm:col-span-6">
              <label class="block text-sm font-medium text-gray-700">
                联系邮箱
              </label>
              <input
                v-model="settings.contactEmail"
                type="email"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            系统设置
          </h3>
          <div class="mt-6 space-y-4">
            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="settings.maintenanceMode"
                  type="checkbox"
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                >
              </div>
              <div class="ml-3 text-sm">
                <label class="font-medium text-gray-700">维护模式</label>
                <p class="text-gray-500">启用后，用户将无法访问网站</p>
              </div>
            </div>

            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="settings.registrationEnabled"
                  type="checkbox"
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                >
              </div>
              <div class="ml-3 text-sm">
                <label class="font-medium text-gray-700">开放注册</label>
                <p class="text-gray-500">允许新用户注册账号</p>
              </div>
            </div>

            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="settings.emailVerification"
                  type="checkbox"
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                >
              </div>
              <div class="ml-3 text-sm">
                <label class="font-medium text-gray-700">邮箱验证</label>
                <p class="text-gray-500">新用户注册后需要验证邮箱</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- API 设置 -->
    <div v-if="activeTab === 'api'" class="space-y-6">
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            API 配置
          </h3>
          <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <label class="block text-sm font-medium text-gray-700">
                API 速率限制（请求/分钟）
              </label>
              <input
                v-model.number="settings.apiRateLimit"
                type="number"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="sm:col-span-3">
              <label class="block text-sm font-medium text-gray-700">
                API 超时时间（秒）
              </label>
              <input
                v-model.number="settings.apiTimeout"
                type="number"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="sm:col-span-6">
              <label class="block text-sm font-medium text-gray-700">
                OpenAI API Key
              </label>
              <input
                v-model="settings.openaiApiKey"
                type="password"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="sk-..."
              >
            </div>

            <div class="sm:col-span-6">
              <label class="block text-sm font-medium text-gray-700">
                允许的 CORS 域名
              </label>
              <textarea
                v-model="settings.corsOrigins"
                rows="3"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="每行一个域名"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 邮件设置 -->
    <div v-if="activeTab === 'email'" class="space-y-6">
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            SMTP 配置
          </h3>
          <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            <div class="sm:col-span-4">
              <label class="block text-sm font-medium text-gray-700">
                SMTP 服务器
              </label>
              <input
                v-model="settings.smtpHost"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="sm:col-span-2">
              <label class="block text-sm font-medium text-gray-700">
                端口
              </label>
              <input
                v-model.number="settings.smtpPort"
                type="number"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="sm:col-span-3">
              <label class="block text-sm font-medium text-gray-700">
                用户名
              </label>
              <input
                v-model="settings.smtpUser"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="sm:col-span-3">
              <label class="block text-sm font-medium text-gray-700">
                密码
              </label>
              <input
                v-model="settings.smtpPassword"
                type="password"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="sm:col-span-6">
              <label class="block text-sm font-medium text-gray-700">
                发件人名称
              </label>
              <input
                v-model="settings.emailFromName"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="sm:col-span-6">
              <label class="block text-sm font-medium text-gray-700">
                发件人邮箱
              </label>
              <input
                v-model="settings.emailFromAddress"
                type="email"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>
          </div>

          <div class="mt-6">
            <button
              @click="testEmail"
              type="button"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              发送测试邮件
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全设置 -->
    <div v-if="activeTab === 'security'" class="space-y-6">
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            安全配置
          </h3>
          <div class="mt-6 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">
                密码最小长度
              </label>
              <input
                v-model.number="settings.minPasswordLength"
                type="number"
                min="6"
                max="32"
                class="mt-1 block w-full sm:w-32 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="settings.requireStrongPassword"
                  type="checkbox"
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                >
              </div>
              <div class="ml-3 text-sm">
                <label class="font-medium text-gray-700">强密码要求</label>
                <p class="text-gray-500">密码必须包含大小写字母、数字和特殊字符</p>
              </div>
            </div>

            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="settings.twoFactorEnabled"
                  type="checkbox"
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                >
              </div>
              <div class="ml-3 text-sm">
                <label class="font-medium text-gray-700">双因素认证</label>
                <p class="text-gray-500">允许用户启用双因素认证</p>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">
                登录失败锁定阈值
              </label>
              <input
                v-model.number="settings.loginAttemptLimit"
                type="number"
                min="3"
                max="10"
                class="mt-1 block w-full sm:w-32 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
              <p class="mt-1 text-sm text-gray-500">连续失败次数后锁定账号</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">
                锁定时长（分钟）
              </label>
              <input
                v-model.number="settings.lockoutDuration"
                type="number"
                min="5"
                max="60"
                class="mt-1 block w-full sm:w-32 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 保存按钮 -->
    <div class="flex justify-end mt-6">
      <button
        @click="saveSettings"
        type="button"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        保存设置
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const tabs = [
  { id: 'general', name: '基础设置' },
  { id: 'api', name: 'API 设置' },
  { id: 'email', name: '邮件设置' },
  { id: 'security', name: '安全设置' }
]

const activeTab = ref('general')

const settings = reactive({
  // 基础设置
  siteName: 'XiangluAI',
  siteDomain: 'xiangluai.com',
  siteDescription: '智能AI服务平台',
  contactEmail: '<EMAIL>',
  maintenanceMode: false,
  registrationEnabled: true,
  emailVerification: true,
  
  // API 设置
  apiRateLimit: 60,
  apiTimeout: 30,
  openaiApiKey: '',
  corsOrigins: '',
  
  // 邮件设置
  smtpHost: 'smtp.example.com',
  smtpPort: 587,
  smtpUser: '',
  smtpPassword: '',
  emailFromName: 'XiangluAI',
  emailFromAddress: '<EMAIL>',
  
  // 安全设置
  minPasswordLength: 8,
  requireStrongPassword: true,
  twoFactorEnabled: true,
  loginAttemptLimit: 5,
  lockoutDuration: 15
})

const saveSettings = () => {
  console.log('保存设置:', settings)
  // 这里添加保存设置的逻辑
  alert('设置已保存')
}

const testEmail = () => {
  console.log('发送测试邮件')
  // 这里添加发送测试邮件的逻辑
  alert('测试邮件已发送')
}
</script>