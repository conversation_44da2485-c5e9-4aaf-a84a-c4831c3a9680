<template>
  <div class="account-page-left mg-r account-mobile-hidden">
    <div class="user-assets-item mg-b">
      <div class="title">系统概览</div>
      <div class="user-assets qk-flex">
        <div class="admin-stats-card">
          <div class="user-assets-name">总用户<i class="ri-arrow-right-s-line"></i></div>
          <div class="user-assets-num">{{ adminStats.totalUsers || 0 }}</div>
        </div>
        <div class="admin-stats-card2">
          <div class="user-assets-name">API调用<i class="ri-arrow-right-s-line"></i></div>
          <div class="user-assets-num">{{ formatNumber(adminStats.totalApiCalls || 0) }}</div>
        </div>
      </div>
    </div>
    
    <div class="tab-links">
      <router-link 
        v-for="item in navigationItems" 
        :key="item.path"
        :to="item.path" 
        class="link-item"
        :class="{ active: $route.path === item.path }"
      >
        <div class="link-title qk-flex">
          <i :class="item.icon"></i>
          <span>{{ item.name }}</span>
        </div>
        <i class="ri-arrow-right-s-line"></i>
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useAdminAuthStore } from '@/stores/adminAuth'

const route = useRoute()
const adminAuthStore = useAdminAuthStore()

// 管理员统计数据
const mockAdminStats = ref({
  totalUsers: 12485,
  totalApiCalls: 1200000
})

const adminStats = computed(() => mockAdminStats.value)

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 管理员导航菜单
const navigationItems = computed(() => {
  const items = [
    { name: '管理概览', path: '/admin', icon: 'ri-dashboard-line' },
    { name: '用户管理', path: '/admin/users', icon: 'ri-user-line' },
    { name: 'API密钥管理', path: '/admin/api-keys', icon: 'ri-key-line' },
    { name: '使用统计', path: '/admin/usage', icon: 'ri-bar-chart-line' },
    { name: '订阅管理', path: '/admin/subscriptions', icon: 'ri-vip-crown-line' },
  ]
  
  // 只有超级管理员可以看到系统配置和财务报表
  if (adminAuthStore.isSuperAdmin) {
    items.push(
      { name: '系统配置', path: '/admin/settings', icon: 'ri-settings-line' },
      { name: '财务报表', path: '/admin/finance', icon: 'ri-file-list-line' }
    )
  }
  
  return items
})
</script>

<style scoped>
/* 管理员统计卡片样式 - 优化版本 */
.admin-stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 18px;
  flex: 1;
  margin-right: 8px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.admin-stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.admin-stats-card2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-radius: 12px;
  padding: 18px;
  flex: 1;
  margin-left: 8px;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
  transition: all 0.3s ease;
}

.admin-stats-card2:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.admin-stats-card .user-assets-name,
.admin-stats-card2 .user-assets-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
}

.admin-stats-card .user-assets-num,
.admin-stats-card2 .user-assets-num {
  color: white;
  font-weight: 700;
  font-size: 20px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 系统概览标题优化 */
.title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 16px;
}

/* 导航项目的管理员主题色 - 优化版本 */
.tab-links .link-item.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  color: #667eea;
  border-left: 4px solid #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.tab-links .link-item.active i {
  color: #667eea;
}

.tab-links .link-item:hover {
  background: rgba(102, 126, 234, 0.08);
  transform: translateX(2px);
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .admin-stats-card,
  .admin-stats-card2 {
    padding: 14px;
  }
  
  .admin-stats-card .user-assets-num,
  .admin-stats-card2 .user-assets-num {
    font-size: 18px;
  }
}
</style>