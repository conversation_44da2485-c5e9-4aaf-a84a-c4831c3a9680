// API 基础配置和工具函数

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'

// 延迟加载错误处理工具
async function loadErrorHandler() {
  try {
    return await import('@/utils/errorHandler')
  } catch (error) {
    console.warn('Error handler not available:', error)
    return null
  }
}

// Token验证工具函数
const validateToken = (token: string): boolean => {
  if (!token) return false
  
  try {
    // 简单的JWT格式验证
    const parts = token.split('.')
    if (parts.length !== 3) return false
    
    // 解析payload检查过期时间
    const payload = JSON.parse(atob(parts[1]))
    const now = Math.floor(Date.now() / 1000)
    
    return payload.exp > now
  } catch {
    return false
  }
}

// 清除认证信息的工具函数
const clearAuthData = () => {
  localStorage.removeItem('auth_token')
  localStorage.removeItem('auth_user')
  sessionStorage.removeItem('auth_token')
  sessionStorage.removeItem('auth_user')
}

// 获取有效token
const getValidToken = (): string | null => {
  const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
  
  if (!token || !validateToken(token)) {
    clearAuthData()
    return null
  }
  
  return token
}

// 请求拦截器和响应处理
class ApiClient {
  private baseURL: string
  
  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    // 获取有效的认证token
    const token = getValidToken()
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        if (response.status === 401) {
          // Token过期或无效，清除认证信息
          clearAuthData()
          
          // 使用window.location进行跳转避免循环依赖
          if (typeof window !== 'undefined') {
            window.location.href = '/login'
          }
          
          throw new ApiError('认证已过期，请重新登录', 401, 'AUTH_EXPIRED')
        }
        
        const errorData = await response.json().catch(() => ({}))
        throw new ApiError(
          errorData.message || `HTTP error! status: ${response.status}`,
          response.status,
          errorData.code
        )
      }

      return await response.json()
    } catch (error) {
      if (error instanceof ApiError) {
        // 延迟加载错误处理工具
        this.handleApiError('请求失败', error)
        throw error
      }
      
      console.error('API request failed:', error)
      const networkError = new ApiError(
        error instanceof Error ? error.message : '网络请求失败',
        0,
        'NETWORK_ERROR'
      )
      
      this.handleApiError('网络错误', networkError)
      throw networkError
    }
  }

  // 延迟加载错误处理功能避免循环依赖
  private async handleApiError(title: string, error: ApiError) {
    try {
      const errorHandler = await loadErrorHandler()
      if (errorHandler) {
        errorHandler.showApiError(title, error.code, error.message)
      } else {
        console.error(`${title}: ${error.message}`)
      }
    } catch (loadError) {
      console.error(`${title}: ${error.message}`)
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }
}

// 创建全局API客户端实例
export const apiClient = new ApiClient()

// 响应数据类型定义
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 增强的错误处理类
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public retryable = false
  ) {
    super(message)
    this.name = 'ApiError'
    
    // 设置可重试的错误类型
    if (status && [408, 429, 502, 503, 504].includes(status)) {
      this.retryable = true
    }
  }

  // 判断是否为认证错误
  isAuthError(): boolean {
    return this.status === 401 || this.code === 'AUTH_EXPIRED'
  }

  // 判断是否为网络错误
  isNetworkError(): boolean {
    return this.code === 'NETWORK_ERROR' || this.status === 0
  }

  // 判断是否为服务器错误
  isServerError(): boolean {
    return this.status ? this.status >= 500 : false
  }
}