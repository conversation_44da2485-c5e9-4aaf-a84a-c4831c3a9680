import{c as M,r as w,_ as O,q as E,V as R,x as o,y as n,z as t,L as T,R as y,J as U,P as b,C as V,Y as $,O as v,T as D,K as k,aq as _,Q as N,aT as q,ac as F,o as J,av as H,aU as Q,I as A,aV as Y,M as G}from"./vue-vendor-CKToUHZx.js";import{_ as L,u as W,a as j}from"./index-Ch5822Og.js";import{s as g}from"./toast-BvwA7Mwb.js";import{L as X}from"./LogoTitle-C8aMR_x8.js";import"./element-plus-B8Fs_0jW.js";import"./vendor-BDiMbLwQ.js";/* empty css                                                                  */const Z={class:"glass-strong rounded-3xl p-6 mb-8 shadow-xl",style:{"z-index":"10",position:"relative"}},ee={class:"flex flex-col md:flex-row justify-between items-center gap-4"},te={class:"flex items-center gap-4"},se={class:"flex items-center gap-2"},ae={class:"text-sm text-gray-400 font-mono"},ne=["href"],oe={class:"relative user-menu-container"},le={class:"px-4 py-3 border-b border-gray-100"},re={class:"flex items-center justify-between text-sm"},ie={class:"font-mono text-gray-700"},ue={key:0,class:"mt-2"},de={class:"flex items-center justify-between text-sm mb-2"},ce={class:"font-mono text-green-600"},me=["href"],fe={key:1,class:"mt-2 text-center text-xs text-gray-500"},pe={key:2,class:"mt-2 text-center"},ve={key:"message",class:"px-3 py-1.5 bg-green-100 border border-green-200 rounded-lg inline-block"},xe={key:0,class:"fixed inset-0 modal z-50 flex items-center justify-center p-4"},ge={class:"modal-content w-full max-w-md p-8 mx-auto max-h-[90vh] flex flex-col"},be=["value"],ye={class:"flex gap-3 pt-4"},we=["disabled"],he={key:0,class:"loading-spinner mr-2"},ke={key:1,class:"fas fa-save mr-2"},_e={__name:"AppHeader",setup(C){const x=q(),d=W(),m=M(()=>d.user||{username:"Admin"}),l=M(()=>d.oemSettings||{}),h=M(()=>d.oemLoading),s=w({current:"...",latest:"",hasUpdate:!1,checkingUpdate:!1,lastChecked:null,releaseInfo:null,noUpdateMessage:!1}),r=w(!1),f=w(!1),p=w(!1),a=O({currentPassword:"",newPassword:"",confirmPassword:"",newUsername:""}),P=async()=>{if(!s.value.checkingUpdate){s.value.checkingUpdate=!0;try{const u=await j.get("/admin/check-updates");if(u.success){const e=u.data;s.value.current=e.current,s.value.latest=e.latest,s.value.hasUpdate=e.hasUpdate,s.value.releaseInfo=e.releaseInfo,s.value.lastChecked=new Date,localStorage.setItem("versionInfo",JSON.stringify({current:e.current,latest:e.latest,lastChecked:s.value.lastChecked,hasUpdate:e.hasUpdate,releaseInfo:e.releaseInfo})),e.hasUpdate||(s.value.noUpdateMessage=!0,setTimeout(()=>{s.value.noUpdateMessage=!1},3e3))}}catch(u){console.error("Error checking for updates:",u);const e=localStorage.getItem("versionInfo");if(e){const c=JSON.parse(e);s.value.current=c.current||s.value.current,s.value.latest=c.latest,s.value.hasUpdate=c.hasUpdate,s.value.releaseInfo=c.releaseInfo,s.value.lastChecked=new Date(c.lastChecked)}}finally{s.value.checkingUpdate=!1}}},z=()=>{a.currentPassword="",a.newPassword="",a.confirmPassword="",a.newUsername="",f.value=!0,r.value=!1},I=()=>{f.value=!1},B=async()=>{if(a.newPassword!==a.confirmPassword){g("两次输入的密码不一致","error");return}if(a.newPassword.length<8){g("新密码长度至少8位","error");return}p.value=!0;try{const u=await j.post("/admin/change-password",{currentPassword:a.currentPassword,newPassword:a.newPassword,newUsername:a.newUsername||void 0});if(u.success){const e=a.newUsername?"账户信息修改成功，请重新登录":"密码修改成功，请重新登录";g(e,"success"),I(),setTimeout(()=>{d.logout(),x.push("/login")},1500)}else g(u.message||"修改失败","error")}catch{g("修改密码失败","error")}finally{p.value=!1}},K=()=>{confirm("确定要退出登录吗？")&&(d.logout(),x.push("/login"),g("已安全退出","success")),r.value=!1},S=u=>{!u.target.closest(".user-menu-container")&&r.value&&(r.value=!1)};return E(()=>{P(),setInterval(()=>{P()},36e5),document.addEventListener("click",S)}),R(()=>{document.removeEventListener("click",S)}),(u,e)=>{var c;return n(),o(N,null,[t("div",Z,[t("div",ee,[t("div",te,[y(X,{loading:h.value,title:l.value.siteName,subtitle:"管理后台","logo-src":l.value.siteIconData||l.value.siteIcon,"title-class":"text-white"},{"after-title":U(()=>{var i;return[t("div",se,[t("span",ae,"v"+b(s.value.current||"..."),1),s.value.hasUpdate?(n(),o("a",{key:0,href:((i=s.value.releaseInfo)==null?void 0:i.htmlUrl)||"#",target:"_blank",class:"inline-flex items-center gap-1 px-2 py-0.5 bg-green-500 border border-green-600 rounded-full text-xs text-white hover:bg-green-600 transition-colors animate-pulse",title:"有新版本可用"},e[7]||(e[7]=[t("i",{class:"fas fa-arrow-up text-[10px]"},null,-1),t("span",null,"新版本",-1)]),8,ne)):T("",!0)])]}),_:1},8,["loading","title","logo-src"])]),t("div",oe,[t("button",{class:"btn btn-primary px-4 py-3 flex items-center gap-2 relative",onClick:e[0]||(e[0]=i=>r.value=!r.value)},[e[8]||(e[8]=t("i",{class:"fas fa-user-circle"},null,-1)),t("span",null,b(m.value.username||"Admin"),1),t("i",{class:V(["fas fa-chevron-down text-xs transition-transform duration-200",{"rotate-180":r.value}])},null,2)]),r.value?(n(),o("div",{key:0,class:"absolute right-0 top-full mt-2 w-56 bg-white rounded-xl shadow-xl border border-gray-200 py-2 user-menu-dropdown",style:{"z-index":"999999"},onClick:e[2]||(e[2]=$(()=>{},["stop"]))},[t("div",le,[t("div",re,[e[9]||(e[9]=t("span",{class:"text-gray-500"},"当前版本",-1)),t("span",ie,"v"+b(s.value.current||"..."),1)]),s.value.hasUpdate?(n(),o("div",ue,[t("div",de,[e[10]||(e[10]=t("span",{class:"text-green-600 font-medium"},[t("i",{class:"fas fa-arrow-up mr-1"}),v("有新版本 ")],-1)),t("span",ce,"v"+b(s.value.latest),1)]),t("a",{href:((c=s.value.releaseInfo)==null?void 0:c.htmlUrl)||"#",target:"_blank",class:"block w-full text-center px-3 py-1.5 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors"},e[11]||(e[11]=[t("i",{class:"fas fa-external-link-alt mr-1"},null,-1),v("查看更新 ",-1)]),8,me)])):s.value.checkingUpdate?(n(),o("div",fe,e[12]||(e[12]=[t("i",{class:"fas fa-spinner fa-spin mr-1"},null,-1),v("检查更新中... ",-1)]))):(n(),o("div",pe,[y(D,{name:"fade",mode:"out-in"},{default:U(()=>[s.value.noUpdateMessage?(n(),o("div",ve,e[13]||(e[13]=[t("p",{class:"text-xs text-green-700 font-medium"},[t("i",{class:"fas fa-check-circle mr-1"}),v("当前已是最新版本 ")],-1)]))):(n(),o("button",{key:"button",class:"text-xs text-blue-500 hover:text-blue-700 transition-colors",onClick:e[1]||(e[1]=i=>P())},e[14]||(e[14]=[t("i",{class:"fas fa-sync-alt mr-1"},null,-1),v("检查更新 ",-1)])))]),_:1})]))]),t("button",{class:"w-full px-4 py-3 text-left text-gray-700 hover:bg-gray-50 transition-colors flex items-center gap-3",onClick:z},e[15]||(e[15]=[t("i",{class:"fas fa-key text-blue-500"},null,-1),t("span",null,"修改账户信息",-1)])),e[17]||(e[17]=t("hr",{class:"my-2 border-gray-200"},null,-1)),t("button",{class:"w-full px-4 py-3 text-left text-gray-700 hover:bg-gray-50 transition-colors flex items-center gap-3",onClick:K},e[16]||(e[16]=[t("i",{class:"fas fa-sign-out-alt text-red-500"},null,-1),t("span",null,"退出登录",-1)]))])):T("",!0)])])]),f.value?(n(),o("div",xe,[t("div",ge,[t("div",{class:"flex items-center justify-between mb-6"},[e[19]||(e[19]=t("div",{class:"flex items-center gap-3"},[t("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[t("i",{class:"fas fa-key text-white"})]),t("h3",{class:"text-xl font-bold text-gray-900"}," 修改账户信息 ")],-1)),t("button",{class:"text-gray-400 hover:text-gray-600 transition-colors",onClick:I},e[18]||(e[18]=[t("i",{class:"fas fa-times text-xl"},null,-1)]))]),t("form",{class:"space-y-6 modal-scroll-content custom-scrollbar flex-1",onSubmit:$(B,["prevent"])},[t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-semibold text-gray-700 mb-3"},"当前用户名",-1)),t("input",{value:m.value.username||"Admin",type:"text",disabled:"",class:"form-input w-full bg-gray-100 cursor-not-allowed"},null,8,be),e[21]||(e[21]=t("p",{class:"text-xs text-gray-500 mt-2"}," 当前用户名，输入新用户名以修改 ",-1))]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-semibold text-gray-700 mb-3"},"新用户名",-1)),k(t("input",{"onUpdate:modelValue":e[3]||(e[3]=i=>a.newUsername=i),type:"text",class:"form-input w-full",placeholder:"输入新用户名（留空保持不变）"},null,512),[[_,a.newUsername]]),e[23]||(e[23]=t("p",{class:"text-xs text-gray-500 mt-2"}," 留空表示不修改用户名 ",-1))]),t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-semibold text-gray-700 mb-3"},"当前密码",-1)),k(t("input",{"onUpdate:modelValue":e[4]||(e[4]=i=>a.currentPassword=i),type:"password",required:"",class:"form-input w-full",placeholder:"请输入当前密码"},null,512),[[_,a.currentPassword]])]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-semibold text-gray-700 mb-3"},"新密码",-1)),k(t("input",{"onUpdate:modelValue":e[5]||(e[5]=i=>a.newPassword=i),type:"password",required:"",class:"form-input w-full",placeholder:"请输入新密码"},null,512),[[_,a.newPassword]]),e[26]||(e[26]=t("p",{class:"text-xs text-gray-500 mt-2"}," 密码长度至少8位 ",-1))]),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-semibold text-gray-700 mb-3"},"确认新密码",-1)),k(t("input",{"onUpdate:modelValue":e[6]||(e[6]=i=>a.confirmPassword=i),type:"password",required:"",class:"form-input w-full",placeholder:"请再次输入新密码"},null,512),[[_,a.confirmPassword]])]),t("div",ye,[t("button",{type:"button",class:"flex-1 px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition-colors",onClick:I}," 取消 "),t("button",{type:"submit",disabled:p.value,class:"btn btn-primary flex-1 py-3 px-6 font-semibold"},[p.value?(n(),o("div",he)):(n(),o("i",ke)),v(" "+b(p.value?"保存中...":"保存修改"),1)],8,we)])],32)])])):T("",!0)],64)}}},Ue=L(_e,[["__scopeId","data-v-590374fc"]]),Ce={class:"flex flex-wrap gap-2 mb-6 bg-white/10 rounded-2xl p-2 backdrop-blur-sm"},Pe=["onClick"],Ie={__name:"TabBar",props:{activeTab:{type:String,required:!0}},emits:["tab-change"],setup(C){const x=[{key:"dashboard",name:"仪表板",icon:"fas fa-tachometer-alt"},{key:"apiKeys",name:"API Keys",icon:"fas fa-key"},{key:"accounts",name:"账户管理",icon:"fas fa-user-circle"},{key:"tutorial",name:"使用教程",icon:"fas fa-graduation-cap"},{key:"settings",name:"其他设置",icon:"fas fa-cogs"}];return(d,m)=>(n(),o("div",Ce,[(n(),o(N,null,F(x,l=>t("button",{key:l.key,class:V(["tab-btn flex-1 py-3 px-6 text-sm font-semibold transition-all duration-300",C.activeTab===l.key?"active":"text-gray-700 hover:bg-white/10 hover:text-gray-900"]),onClick:h=>d.$emit("tab-change",l.key)},[t("i",{class:V(l.icon+" mr-2")},null,2),v(b(l.name),1)],10,Pe)),64))]))}},Me=L(Ie,[["__scopeId","data-v-aabb6896"]]),Te={class:"min-h-screen p-6"},Ve={class:"glass-strong rounded-3xl p-6 shadow-xl",style:{"z-index":"1","min-height":"calc(100vh - 240px)"}},Le={class:"tab-content"},Se={__name:"MainLayout",setup(C){const x=Q(),d=q(),m=w("dashboard"),l={dashboard:"/dashboard",apiKeys:"/api-keys",accounts:"/accounts",tutorial:"/tutorial",settings:"/settings"};J(()=>x.path,s=>{const r=Object.keys(l).find(f=>l[f]===s);r&&(m.value=r)},{immediate:!0});const h=s=>{m.value=s,d.push(l[s])};return(s,r)=>{const f=H("router-view");return n(),o("div",Te,[y(Ue),t("div",Ve,[y(Me,{"active-tab":m.value,onTabChange:h},null,8,["active-tab"]),t("div",Le,[y(f,null,{default:U(({Component:p})=>[y(D,{name:"slide-up",mode:"out-in"},{default:U(()=>[(n(),A(Y,{include:["DashboardView","ApiKeysView"]},[(n(),A(G(p)))],1024))]),_:2},1024)]),_:1})])])])}}},Be=L(Se,[["__scopeId","data-v-59f33a38"]]);export{Be as default};
