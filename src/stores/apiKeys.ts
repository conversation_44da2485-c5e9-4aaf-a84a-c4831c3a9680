import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api<PERSON>eysApi } from '@/api'
import type { Api<PERSON>ey } from '@/types/dashboard'
import { ApiKeyStatus } from '@/types/dashboard'

export const useApiKeysStore = defineStore('apiKeys', () => {
  // 状态
  const apiKeys = ref<ApiKey[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const selectedKeys = ref<string[]>([])
  const searchQuery = ref('')
  const statusFilter = ref<'all' | 'active' | 'inactive' | 'expired'>('all')

  // 分页状态
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })

  // 计算属性
  const filteredKeys = computed(() => {
    let filtered = apiKeys.value

    // 按搜索关键词筛选
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(key => 
        key.name.toLowerCase().includes(query) ||
        key.key.toLowerCase().includes(query)
      )
    }

    // 按状态筛选
    if (statusFilter.value !== 'all') {
      filtered = filtered.filter(key => key.status === statusFilter.value)
    }

    return filtered
  })

  const activeKeysCount = computed(() => 
    apiKeys.value.filter(key => key.status === 'active').length
  )

  const totalUsage = computed(() => 
    apiKeys.value.reduce((total, key) => total + key.usageCount, 0)
  )

  const hasSelectedKeys = computed(() => selectedKeys.value.length > 0)

  // 操作方法
  const fetchApiKeys = async (page = 1, pageSize = 10) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await apiKeysApi.getApiKeys({
        page,
        pageSize,
        status: statusFilter.value !== 'all' ? statusFilter.value : undefined
      })
      
      apiKeys.value = response.items
      pagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取API密钥失败'
      console.error('Failed to fetch API keys:', err)
    } finally {
      isLoading.value = false
    }
  }

  const createApiKey = async (data: {
    name: string
    permissions: string[]
    rateLimit: number
    description?: string
  }) => {
    isLoading.value = true
    error.value = null

    try {
      const newKey = await apiKeysApi.createApiKey(data)
      apiKeys.value.unshift(newKey)
      pagination.value.total += 1
      return newKey
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建API密钥失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateApiKey = async (id: string, data: {
    name?: string
    permissions?: string[]
    rateLimit?: number
    status?: 'active' | 'inactive'
    description?: string
  }) => {
    try {
      const updatedKey = await apiKeysApi.updateApiKey(id, data)
      const index = apiKeys.value.findIndex(key => key.id === id)
      if (index !== -1) {
        apiKeys.value[index] = updatedKey
      }
      return updatedKey
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新API密钥失败'
      throw err
    }
  }

  const resetApiKey = async (id: string) => {
    try {
      const result = await apiKeysApi.resetApiKey(id)
      const index = apiKeys.value.findIndex(key => key.id === id)
      if (index !== -1) {
        apiKeys.value[index].key = result.key
      }
      return result.key
    } catch (err) {
      error.value = err instanceof Error ? err.message : '重置API密钥失败'
      throw err
    }
  }

  const deleteApiKey = async (id: string) => {
    try {
      await apiKeysApi.deleteApiKey(id)
      const index = apiKeys.value.findIndex(key => key.id === id)
      if (index !== -1) {
        apiKeys.value.splice(index, 1)
        pagination.value.total -= 1
      }
      // 从选中列表中移除
      selectedKeys.value = selectedKeys.value.filter(keyId => keyId !== id)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除API密钥失败'
      throw err
    }
  }

  const toggleApiKey = async (id: string) => {
    try {
      const key = apiKeys.value.find(k => k.id === id)
      if (!key) return

      const newStatus = key.status === 'active' ? 'inactive' : 'active'
      await updateApiKey(id, { status: newStatus })
    } catch (err) {
      error.value = err instanceof Error ? err.message : '切换API密钥状态失败'
      throw err
    }
  }

  const batchUpdateKeys = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedKeys.value.length === 0) return

    try {
      await apiKeysApi.batchUpdateApiKeys({
        ids: selectedKeys.value,
        action
      })

      if (action === 'delete') {
        apiKeys.value = apiKeys.value.filter(key => !selectedKeys.value.includes(key.id))
        pagination.value.total -= selectedKeys.value.length
      } else {
        const newStatus = action === 'activate' ? ApiKeyStatus.ACTIVE : ApiKeyStatus.INACTIVE
        selectedKeys.value.forEach(id => {
          const key = apiKeys.value.find(k => k.id === id)
          if (key) {
            key.status = newStatus
          }
        })
      }

      selectedKeys.value = []
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量操作失败'
      throw err
    }
  }

  // 选择操作
  const selectKey = (id: string) => {
    if (!selectedKeys.value.includes(id)) {
      selectedKeys.value.push(id)
    }
  }

  const unselectKey = (id: string) => {
    selectedKeys.value = selectedKeys.value.filter(keyId => keyId !== id)
  }

  const toggleKeySelection = (id: string) => {
    if (selectedKeys.value.includes(id)) {
      unselectKey(id)
    } else {
      selectKey(id)
    }
  }

  const selectAllKeys = () => {
    selectedKeys.value = filteredKeys.value.map(key => key.id)
  }

  const clearSelection = () => {
    selectedKeys.value = []
  }

  const toggleSelectAll = () => {
    if (selectedKeys.value.length === filteredKeys.value.length) {
      clearSelection()
    } else {
      selectAllKeys()
    }
  }

  // 搜索和筛选
  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  const setStatusFilter = (status: typeof statusFilter.value) => {
    statusFilter.value = status
    // 重新获取数据
    fetchApiKeys(1, pagination.value.pageSize)
  }

  // 分页操作
  const goToPage = (page: number) => {
    fetchApiKeys(page, pagination.value.pageSize)
  }

  const changePageSize = (pageSize: number) => {
    fetchApiKeys(1, pageSize)
  }

  // 清除数据
  const clearData = () => {
    apiKeys.value = []
    selectedKeys.value = []
    error.value = null
    searchQuery.value = ''
    statusFilter.value = 'all'
    pagination.value = {
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    }
  }

  return {
    // 状态
    apiKeys,
    isLoading,
    error,
    selectedKeys,
    searchQuery,
    statusFilter,
    pagination,

    // 计算属性
    filteredKeys,
    activeKeysCount,
    totalUsage,
    hasSelectedKeys,

    // 操作方法
    fetchApiKeys,
    createApiKey,
    updateApiKey,
    resetApiKey,
    deleteApiKey,
    toggleApiKey,
    batchUpdateKeys,

    // 选择操作
    selectKey,
    unselectKey,
    toggleKeySelection,
    selectAllKeys,
    clearSelection,
    toggleSelectAll,

    // 搜索和筛选
    setSearchQuery,
    setStatusFilter,

    // 分页操作
    goToPage,
    changePageSize,

    // 工具方法
    clearData
  }
})