# API转发服务数据库配置
version: '3.8'

services:
  # PostgreSQL 主数据库
  postgres:
    image: postgres:15-alpine
    container_name: xiangluai-postgres
    environment:
      POSTGRES_DB: xiangluai_api
      POSTGRES_USER: xiangluai
      POSTGRES_PASSWORD: your_secure_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - xiangluai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U xiangluai -d xiangluai_api"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存数据库
  redis:
    image: redis:7-alpine
    container_name: xiangluai-redis
    command: redis-server --appendonly yes --requirepass your_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./database/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - xiangluai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # ClickHouse 分析数据库 (可选)
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    container_name: xiangluai-clickhouse
    environment:
      CLICKHOUSE_DB: xiangluai_analytics
      CLICKHOUSE_USER: xiangluai
      CLICKHOUSE_PASSWORD: your_clickhouse_password
    ports:
      - "8123:8123"  # HTTP接口
      - "9000:9000"  # TCP接口
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./database/clickhouse:/docker-entrypoint-initdb.d
    networks:
      - xiangluai-network
    restart: unless-stopped
    ulimits:
      nofile:
        soft: 262144
        hard: 262144

  # 数据库管理工具
  adminer:
    image: adminer:latest
    container_name: xiangluai-adminer
    ports:
      - "8080:8080"
    networks:
      - xiangluai-network
    restart: unless-stopped
    depends_on:
      - postgres

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  clickhouse_data:
    driver: local

networks:
  xiangluai-network:
    driver: bridge
    external: true
