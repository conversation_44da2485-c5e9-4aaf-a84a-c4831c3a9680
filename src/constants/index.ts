// 应用常量定义

// API 相关常量
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile'
  },
  API_KEYS: {
    LIST: '/api-keys',
    CREATE: '/api-keys',
    UPDATE: '/api-keys/:id',
    DELETE: '/api-keys/:id',
    REGENERATE: '/api-keys/:id/regenerate'
  },
  USAGE: {
    STATS: '/usage/stats',
    CHARTS: '/usage/charts',
    EXPORT: '/usage/export',
    REAL_TIME: '/usage/real-time'
  },
  BILLING: {
    RECORDS: '/billing/records',
    CURRENT: '/billing/current',
    PLANS: '/billing/plans',
    UPGRADE: '/billing/upgrade',
    CANCEL: '/billing/cancel'
  },
  SUBSCRIPTION: {
    INFO: '/subscription/info',
    PLANS: '/subscription/plans',
    UPGRADE: '/subscription/upgrade',
    CANCEL: '/subscription/cancel'
  }
} as const

// HTTP 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
} as const

// 存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_PROFILE: 'user_profile',
  APP_SETTINGS: 'app_settings',
  THEME_MODE: 'theme_mode',
  LANGUAGE: 'language'
} as const

// 应用配置
export const APP_CONFIG = {
  NAME: '第三方API中继服务用户中心',
  VERSION: '1.0.0',
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  TIMEOUT: 30000, // 30秒
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1秒
  PAGE_SIZE: 20,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  DATE_FORMAT: 'YYYY-MM-DD',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',
  CURRENCY: 'CNY'
} as const

// 主题配置
export const THEME_CONFIG = {
  COLORS: {
    PRIMARY: '#3B82F6',
    SUCCESS: '#10B981',
    WARNING: '#F59E0B',
    ERROR: '#EF4444',
    INFO: '#6B7280'
  },
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536
  }
} as const

// 验证规则
export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: {
    MIN_LENGTH: 8,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
  },
  API_KEY_NAME: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 50,
    PATTERN: /^[a-zA-Z0-9\s\-_]+$/
  },
  PHONE: /^1[3-9]\d{9}$/,
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[a-zA-Z0-9_]+$/
  }
} as const

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_SIZE: 20,
  SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_SIZE: 100
} as const

// 上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: {
    IMAGE: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    DOCUMENT: ['application/pdf', 'text/plain', 'application/msword'],
    EXCEL: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
  },
  CHUNK_SIZE: 1024 * 1024 // 1MB chunks for large files
} as const

// 缓存配置
export const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  USER_PROFILE_TTL: 30 * 60 * 1000, // 30分钟
  API_KEYS_TTL: 10 * 60 * 1000, // 10分钟
  USAGE_STATS_TTL: 2 * 60 * 1000, // 2分钟
  BILLING_TTL: 60 * 60 * 1000 // 1小时
} as const

// 实时更新配置
export const REAL_TIME_CONFIG = {
  USAGE_STATS_INTERVAL: 30000, // 30秒
  API_KEYS_INTERVAL: 60000, // 1分钟
  BILLING_INTERVAL: 300000, // 5分钟
  HEARTBEAT_INTERVAL: 15000, // 15秒
  RECONNECT_ATTEMPTS: 5,
  RECONNECT_DELAY: 2000 // 2秒
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN: '登录成功',
  LOGOUT: '退出登录成功',
  REGISTER: '注册成功',
  PROFILE_UPDATE: '个人信息更新成功',
  API_KEY_CREATE: 'API密钥创建成功',
  API_KEY_UPDATE: 'API密钥更新成功',
  API_KEY_DELETE: 'API密钥删除成功',
  API_KEY_REGENERATE: 'API密钥重新生成成功',
  PLAN_UPGRADE: '订阅计划升级成功',
  PLAN_CANCEL: '订阅计划取消成功',
  DATA_EXPORT: '数据导出成功',
  SETTINGS_SAVE: '设置保存成功'
} as const

// 特性标识
export const FEATURE_FLAGS = {
  ENABLE_REAL_TIME_UPDATES: true,
  ENABLE_DATA_EXPORT: true,
  ENABLE_ADVANCED_ANALYTICS: true,
  ENABLE_MULTI_LANGUAGE: false,
  ENABLE_DARK_MODE: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_BETA_FEATURES: false
} as const