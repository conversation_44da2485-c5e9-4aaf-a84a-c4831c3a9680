<template>
  <header class="header">
    <div class="header-top fixed">
      <div class="header-top-wrap wrapper">
        <div class="left-entry">
          <div class="mobile-show">
            <div id="mobile-menu-button" @click="showMenu" class="menu-icon">
              <i class="ri-menu-2-line"></i>
            </div>
          </div>
          <div class="header-logo">
            <router-link to="/" class="logo">
              <img itemprop="logo" src="/src/assets/dashboard/picture/组-107.svg" alt="API服务平台" />
            </router-link>
          </div>
          <div id="top-menu" class="menu-container">
            <ul class="menu">
              <li><router-link to="/">首页</router-link></li>
              <li><router-link to="/dashboard">仪表板</router-link></li>
              <li><router-link to="/dashboard/api-management">API管理</router-link></li>
              <li><router-link to="/dashboard/usage-stats">使用统计</router-link></li>
              <li><router-link to="/dashboard/subscription">套餐管理</router-link></li>
            </ul>
          </div>
        </div>
        
        <div class="center-entry">
          <div class="menu-search-mask"></div>
        </div>
        
        <div class="right-entry">
          <div class="menu-search mobile-show">
            <div id="mobile-search-button" @click="showSearch" class="search-icon">
              <i class="ri-search-line"></i>
            </div>
          </div>
          
          <!-- 用户菜单 -->
          <div class="user-menu">
            <div class="user-avatar" @click="toggleUserMenu">
              <img :src="userStore.user?.avatar || '/api/placeholder/32/32'" alt="用户头像" />
              <i class="ri-arrow-down-s-line"></i>
            </div>
            
            <div v-show="showUserMenu" class="user-dropdown">
              <div class="user-info">
                <div class="user-email">{{ userStore.user?.email }}</div>
                <div class="user-plan">{{ currentPlan }}</div>
              </div>
              <hr>
              <router-link to="/dashboard" class="dropdown-item">
                <i class="ri-dashboard-line"></i>
                仪表板
              </router-link>
              <router-link to="/dashboard/profile" class="dropdown-item">
                <i class="ri-user-line"></i>
                个人资料
              </router-link>
              <router-link to="/dashboard/subscription" class="dropdown-item">
                <i class="ri-vip-crown-line"></i>
                套餐管理
              </router-link>
              <hr>
              <button @click="handleLogout" class="dropdown-item logout">
                <i class="ri-logout-box-line"></i>
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 使用mock数据，避免store依赖导致的循环依赖问题
const mockUser = ref({
  email: '<EMAIL>',
  avatar: '/src/assets/dashboard/picture/a6e.png'
})

const mockCurrentPlan = ref({
  name: '免费版'
})

const userStore = computed(() => ({
  user: mockUser.value,
  logout: async () => {
    // 简单的登出逻辑，清除本地数据
    localStorage.removeItem('auth_token')
    localStorage.removeItem('auth_user')
    sessionStorage.removeItem('auth_token')
    sessionStorage.removeItem('auth_user')
  }
}))

const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const showMobileSearch = ref(false)

const currentPlan = computed(() => mockCurrentPlan.value.name)

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const showMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const showSearch = () => {
  showMobileSearch.value = !showMobileSearch.value
}

const handleLogout = async () => {
  try {
    await userStore.value.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event: MouseEvent) => {
  const userMenu = document.querySelector('.user-menu')
  if (userMenu && !userMenu.contains(event.target as Node)) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* 修复logo大小问题 */
.header-logo img {
  height: calc(var(--top-menu-height) - 25px) !important;
  width: auto !important;
  max-height: 31px !important;
}

.header-logo {
  max-height: calc(var(--top-menu-height) - 10px) !important;
}

.user-menu {
  position: relative;
  display: flex;
  align-items: center;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.user-avatar:hover {
  background-color: var(--bg-muted-color);
}

.user-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
  padding: 8px 0;
}

.user-info {
  padding: 12px 16px;
}

.user-email {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.user-plan {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: var(--color-text-primary);
  text-decoration: none;
  transition: background-color 0.2s;
  border: none;
  background: none;
  width: 100%;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: var(--bg-muted-color);
}

.dropdown-item i {
  margin-right: 8px;
  width: 16px;
}

.dropdown-item.logout {
  color: #e74c3c;
}

hr {
  margin: 8px 0;
  border: none;
  border-top: 1px solid var(--border-color-base);
}
</style>