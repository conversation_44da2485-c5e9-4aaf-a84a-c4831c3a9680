<template>
  <div class="p-6">
    <div class="mb-8 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
        <p class="mt-1 text-sm text-gray-600">管理系统所有用户账户</p>
      </div>
      <button
        @click="showAddModal = true"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        <PlusIcon class="-ml-1 mr-2 h-5 w-5" />
        添加用户
      </button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-4">
      <div class="sm:col-span-2">
        <label for="search" class="sr-only">搜索用户</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
          </div>
          <input
            id="search"
            v-model="searchQuery"
            type="text"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="搜索用户名、邮箱..."
          >
        </div>
      </div>
      <div>
        <select
          v-model="filterStatus"
          class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        >
          <option value="">所有状态</option>
          <option value="active">活跃</option>
          <option value="inactive">未激活</option>
          <option value="banned">已封禁</option>
        </select>
      </div>
      <div>
        <select
          v-model="filterPlan"
          class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        >
          <option value="">所有套餐</option>
          <option value="free">免费版</option>
          <option value="basic">基础版</option>
          <option value="pro">专业版</option>
          <option value="enterprise">企业版</option>
        </select>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <ul class="divide-y divide-gray-200">
        <li v-for="user in filteredUsers" :key="user.id">
          <div class="px-4 py-4 sm:px-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <img
                    class="h-10 w-10 rounded-full"
                    :src="user.avatar || `https://ui-avatars.com/api/?name=${user.name}&background=random`"
                    :alt="user.name"
                  >
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">
                    {{ user.name }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ user.email }}
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-6">
                <div class="text-sm text-gray-900">
                  <span class="font-medium">套餐:</span>
                  <span
                    :class="[
                      'ml-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                      user.plan === 'free' ? 'bg-gray-100 text-gray-800' :
                      user.plan === 'basic' ? 'bg-blue-100 text-blue-800' :
                      user.plan === 'pro' ? 'bg-purple-100 text-purple-800' :
                      'bg-green-100 text-green-800'
                    ]"
                  >
                    {{ getPlanName(user.plan) }}
                  </span>
                </div>
                <div class="text-sm text-gray-900">
                  <span class="font-medium">API调用:</span>
                  <span class="ml-1">{{ user.apiCalls.toLocaleString() }}</span>
                </div>
                <div>
                  <span
                    :class="[
                      'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                      user.status === 'active' ? 'bg-green-100 text-green-800' :
                      user.status === 'inactive' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ getStatusName(user.status) }}
                  </span>
                </div>
                <div>
                  <button
                    @click="editUser(user)"
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    编辑
                  </button>
                </div>
              </div>
            </div>
            <div class="mt-2 sm:flex sm:justify-between">
              <div class="sm:flex">
                <p class="flex items-center text-sm text-gray-500">
                  <CalendarIcon class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                  注册于 {{ formatDate(user.createdAt) }}
                </p>
                <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                  <ClockIcon class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                  最后活跃 {{ formatDate(user.lastActive) }}
                </p>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>

    <!-- 编辑用户模态框 -->
    <div v-if="showEditModal" class="fixed z-10 inset-0 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeEditModal"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              编辑用户信息
            </h3>
            
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">用户名</label>
                <input
                  v-model="editingUser.name"
                  type="text"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">邮箱</label>
                <input
                  v-model="editingUser.email"
                  type="email"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">套餐</label>
                <select
                  v-model="editingUser.plan"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="free">免费版</option>
                  <option value="basic">基础版</option>
                  <option value="pro">专业版</option>
                  <option value="enterprise">企业版</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">状态</label>
                <select
                  v-model="editingUser.status"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="active">活跃</option>
                  <option value="inactive">未激活</option>
                  <option value="banned">已封禁</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="saveUser"
              type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              保存
            </button>
            <button
              @click="closeEditModal"
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  MagnifyingGlassIcon,
  PlusIcon,
  CalendarIcon,
  ClockIcon
} from '@heroicons/vue/24/outline'

interface User {
  id: string
  name: string
  email: string
  avatar?: string
  plan: 'free' | 'basic' | 'pro' | 'enterprise'
  status: 'active' | 'inactive' | 'banned'
  apiCalls: number
  createdAt: Date
  lastActive: Date
}

// 模拟用户数据
const users = ref<User[]>([
  {
    id: '1',
    name: '张三',
    email: '<EMAIL>',
    plan: 'pro',
    status: 'active',
    apiCalls: 125420,
    createdAt: new Date('2024-01-15'),
    lastActive: new Date('2025-01-30')
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    plan: 'basic',
    status: 'active',
    apiCalls: 54300,
    createdAt: new Date('2024-03-20'),
    lastActive: new Date('2025-01-29')
  },
  {
    id: '3',
    name: '王五',
    email: '<EMAIL>',
    plan: 'free',
    status: 'inactive',
    apiCalls: 1200,
    createdAt: new Date('2024-06-10'),
    lastActive: new Date('2024-12-15')
  },
  {
    id: '4',
    name: '赵六',
    email: '<EMAIL>',
    plan: 'enterprise',
    status: 'active',
    apiCalls: 892000,
    createdAt: new Date('2023-11-05'),
    lastActive: new Date('2025-01-30')
  },
  {
    id: '5',
    name: '钱七',
    email: '<EMAIL>',
    plan: 'pro',
    status: 'banned',
    apiCalls: 0,
    createdAt: new Date('2024-02-28'),
    lastActive: new Date('2024-10-20')
  }
])

const searchQuery = ref('')
const filterStatus = ref('')
const filterPlan = ref('')
const showEditModal = ref(false)
const showAddModal = ref(false)
const editingUser = ref<Partial<User>>({})

const filteredUsers = computed(() => {
  return users.value.filter(user => {
    const matchesSearch = !searchQuery.value || 
      user.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesStatus = !filterStatus.value || user.status === filterStatus.value
    const matchesPlan = !filterPlan.value || user.plan === filterPlan.value
    
    return matchesSearch && matchesStatus && matchesPlan
  })
})

const getPlanName = (plan: string) => {
  const planNames: Record<string, string> = {
    free: '免费版',
    basic: '基础版',
    pro: '专业版',
    enterprise: '企业版'
  }
  return planNames[plan] || plan
}

const getStatusName = (status: string) => {
  const statusNames: Record<string, string> = {
    active: '活跃',
    inactive: '未激活',
    banned: '已封禁'
  }
  return statusNames[status] || status
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date)
}

const editUser = (user: User) => {
  editingUser.value = { ...user }
  showEditModal.value = true
}

const closeEditModal = () => {
  showEditModal.value = false
  editingUser.value = {}
}

const saveUser = () => {
  // 这里添加保存用户的逻辑
  const index = users.value.findIndex(u => u.id === editingUser.value.id)
  if (index !== -1) {
    users.value[index] = { ...users.value[index], ...editingUser.value } as User
  }
  closeEditModal()
}
</script>