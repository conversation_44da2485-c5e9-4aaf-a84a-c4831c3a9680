<!doctype html>
<html lang="zh-CN" class="avgrund-ready">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta name="renderer" content="webkit">
    <meta name="force-rendering" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <link rel="profile" href="http://gmpg.org/xfn/11">
    <meta name="theme-color" content="#3858f6">
    <title>*********的个人中心 - qkua七夸主题</title>
<meta name='robots' content='max-image-preview:large'>
<meta name="referrer" content="no-referrer">
    <meta property="og:locale" content="zh_CN">
    <meta property="og:type" content="article">
    <meta property="og:site_name" content="qkua七夸主题">
    <meta property="og:title" content="*********的个人中心 - qkua七夸主题">
    <meta property="og:url" content="/account/distribution">
    <link rel='stylesheet' id='parent-style-main-css' href='static/css/style-1.3.6.css' type='text/css' media='all'>
<link rel='stylesheet' id='parent-style-css' href='static/css/style-1.3.61.css' type='text/css' media='all'>
<style id='parent-style-inline-css' type='text/css'>
    [v-cloak]{
        display: none!important
    }
    :root{
        --site-width:2560px;
        --wrapper-width:1200px;
        --sidebar-width:300px;
        --radius:4px;
        --btn-radius:20px;
        --gap:16px;
        
        --top-menu-width:2560px;
        --top-menu-height:56px;
        --top-menu-bg-color:#ffffff;
        --top-menu-text-color:#333333;
        
        --theme-color:#3858f6;
        --color-primary:var(--theme-color);
        --color-text-primary: #333333; /**主要文字色**/
        --color-text-regular: #61666D; /**常规文字色**/
        --color-text-secondary: #9499A0; /**次要文字色**/
        --color-text-placeholder: #C9CCD0; /****占位文字色*****/
        --border-color-base: #f7f7f7; /**边框色**/
        --border-color-muted: #f7f7f7;
        --color-white: #FFFFFF;
        --bg-body-color:#f6f7f9;
        --bg-main-color:var(--color-white); /**box**/
        --bg-text-color:rgba(56, 88, 246, var(--opacity,0.1));
        --bg-muted-color: var(--bg-body-color);
    }
</style>
<link rel='stylesheet' id='flickity-css' href='static/css/flickity.css' type='text/css' media='all'>
<link rel='stylesheet' id='fancybox-css' href='static/css/fancybox.css' type='text/css' media='all'>
<link rel='stylesheet' id='qk-fonts-css' href='static/css/remixicon.css' type='text/css' media='all'>
<link rel='stylesheet' id='qk-account-css' href='static/css/account.css' type='text/css' media='all'>
<link rel='stylesheet' id='qk-mobile-css' href='static/css/mobile.css' type='text/css' media='all'>
<link rel='stylesheet' id='child-style-css' href='static/css/style-1.3.62.css' type='text/css' media='all'>


<link rel="apple-touch-icon" href="/wp-content/uploads/2023/10/qkua.png">
<meta name="msapplication-TileImage" content="/wp-content/uploads/2023/10/qkua.png">
</head>

<body class="">
    
    <div id="page" class="site">
        
        
            <header class="header">
                <div class="header-top fixed">
                <div class="header-top-wrap wrapper">
                    <div class="left-entry">
                        
            <div class="mobile-show">
                <div id="mobile-menu-button" @click="showMenu" class="menu-icon">
                    <i class="ri-menu-2-line"></i>
                </div>
            </div>
                        <div class="header-logo"><a rel="home" class="logo" href=""><img itemprop="logo" src="static/picture/组-107.svg" alt="qkua七夸主题"></a></div>
                        <div id="top-menu" class="menu-%e8%8f%9c%e5%8d%95-container"><ul id="menu-%e8%8f%9c%e5%8d%95" class="menu"><li><a href="/">首页</a></li>
<li><a href="#">页面演示</a></li>
<li><a href="/ztgn">特色功能</a></li>
<li><a href="/qkua">购买主题<b class="badge">更新</b></a></li>
<li><a href="/vip">开通会员</a></li>
</ul></div>
                    </div>
                    
        <div class="center-entry">
            <div class="menu-search-mask" onclick="mobileSearch.showSearch()"></div>
            <search></search>
        </div>
                    <div class="right-entry">
                        
        <div class="menu-search mobile-show">
            <div id="mobile-search-button" @click="showSearch" class="search-icon">
                <i class="ri-search-line"></i>
            </div>
        </div>
                        <div class="menu-message">
            <a class="no-hover" rel="nofollow" href="/message">
                <b class="badge red" v-if="count > 0" v-text="count" v-cloak="">1</b>
                <i class="ri-notification-3-line"></i>
            </a>
        </div>
                        
                        
    <div class="menu-check-in mobile-hidden">
        <div class="check-in-btn" v-text="isCheckIn ? '已签到' : '签到'">签到</div>
        <div class="check-in-menu-wrap">
            <calendar-checkin :is-check-in="isCheckIn" :consecutive-days="consecutiveDays" @checkin-success="checkin"></calendar-checkin>
        </div>
    </div>
        
                        
    <div class="menu-publish-box mobile-hidden">
        <div class="menu-publish-btn bg-text">发布</div>
        <div class="publish-menu-wrap">
            <div class="publish-menu-container box">
                <div class="publish-list">
                
            <a href="/write" class="publish-item qk-flex" rel="nofollow">
                <div class="img-icon">
                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAARVBMVEUAAAD6rTD6rC/8rzH7rjL2qi/6qy/7rjH6rC/80Yz7yXn6rjT8zoX6tkn6uVD80In7zH77xnD7wWT6sj77xGz6sTv7vluPQmV9AAAACHRSTlMAZt5ZQhvrjU1b8gYAAADpSURBVEjH7dXJEsIgDIBhu6hhLdDl/R/VICrtgQzpjDM98J+4fAbSg7dW69vYQ6l+pCC6sqQgUF0PSvFLHmDXdcOdgGJXEsZLFWGUjIlqEcKliVj9G1eHv2D4cNboNqiBcr8YFectUAUPi/F40MGv3Ik2nbVivlHpBA0wYUjOQhWU+QtO8TgZqIN5NVt6oJ6ZE5XOq2G9EVfKuWrOvZ2fgQlNZA7HceGCt7QAbIjfws3AgXk10fGhFwFYMN90PQWt8HAKBmHPQROAD3P/hz3hHhQc+7J7JkjUFbogHAg3UPBelvj/2Gp9egEBOSF8OsSejgAAAABJRU5ErkJggg==" alt="发布文章">
                </div>
                <div class="link-title">
                    <p class="type-text">发布文章</p>
                </div>
            </a>
            <a href="/moment" class="publish-item qk-flex" rel="nofollow">
                <div class="img-icon">
                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAkFBMVEUAAABJjO5JjO1Jju9LlPRJjO5NkvNIjO5JjO1Ji+5VpPpJjO5JjO1JjO5KjO5JjO5Lje5Lju9qlf9Li+1Jje1Jje1Nj+9JjO1KjO1JjO1KjO5JjO5Ije5LkO5Ij+9PjO9Ji+1KjO9HjO1Ni/BIi+2av/Vim+9Uk+6Ot/NMje6Uu/SJs/N1p/GPuPOArvJ/rvL4Td6/AAAAJHRSTlMAZvJhF+ELwPaWBvnIs5JKRjwHgnRXKufZ2JtbLi0gE+4fgSEbMP8oAAABVElEQVRIx+3TyVLDMAyA4diOs+9p0r1QEE034P3fDnBw2xRZCcOBS/5bD99I7ijW2Nj/F2S+O+N85vpZ8Atm+xwucd8eyIpSM03LYtC4CH4UDRi6loAk173zlENkz8wiAkMR/c4SjJXkotwMObWsD0Q+cS+cgjwwwgzIMmJTstgxQZeGc7ExwBkNp4wZDojT0GOM4TO9fijQd8q+VT/LMRjRcM6+wkYmNHQVrBFYQdvupdMO2hIFUwQ6nIIeU1UW0gJUh/2t23c2ZQKD2xBUzeuNa0AVTjQkRsLhTbv3g75U1rbsudbmfDoeT+dG/5bsuxRzDyEYepxouMKgMLon7USAwdjgZDvPeHKOunIe3y0cxuyabXiiTLLA2i74lXluO456olXXenaVRFOPy7mbsG6pNaiNYPcVw6STX4TIn/Pl9bPqp3VaCbFMV4H6G2xrbOyPfQBb3nOcbPZhjgAAAABJRU5ErkJggg==" alt="发布动态">
                </div>
                <div class="link-title">
                    <p class="type-text">发布动态</p>
                </div>
            </a>
            <a href="" class="publish-item qk-flex" rel="nofollow">
                <div class="img-icon">
                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAdVBMVEUAAAB2yEF2yUB2ykJ4yEWA0FB3yUJ3ykJ4yUJ3yUN2ykJ6yER3y0d9yEZ3yUJ2yEN5y0X///92yD91yEJ2yEJ7zkJ2ykF1yUCz4ZaW1m6t342g2nyl3IOT1WqDzlN9zEuu34+a13SO0mOI0Fp6y0eq3Yma13NU+B35AAAAF3RSTlMAZuNIFAhWQDRRLSIaDE06JwHuX1wfkVo4AEIAAAEGSURBVEjH7dHbjoIwEIBhobQcFVh1mRYUT7vv/4gLzaKBaTuGxMRE/otezZdO2tXS0rvF10GWFkkUsk28CVyTpR5NorwbFTtv1K40Gx7GYuu54mYYelSWXQUJMzOMSbhHhvmVs69vDQsEkUNSwwTBikzDaC7M58LQDKVS0g2ZEUrokk4YG6HqoXJCMRdu567qlc88Tl1jyBHEnQHOCK5peIWu6xQGJLydeni6TeDeAeum7U/Q1ROY2qEEaKrqAv9dxjCxwiN0/bZwrx3ByAp/oE89YDOCuQ1KQB3oG/WiuOMDCm6BDWbDyxZpFgzfj+EBwLYrIgN09BLoU863QOYTjq2Wlj6rP/GqVBQ6EojHAAAAAElFTkSuQmCC" alt="发布图片">
                </div>
                <div class="link-title">
                    <p class="type-text">发布图片</p>
                </div>
            </a>
            <a href="" class="publish-item qk-flex" rel="nofollow">
                <div class="img-icon">
                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4BAMAAABaqCYtAAAAMFBMVEUAAAD/aW7/am//anD/bHD/a3L/aW//paj/lJj/fIH/n6L/mp7/jpP/h4z/dHr/b3U57ICnAAAABnRSTlMAZuNgSSYHf9E7AAAAZUlEQVQ4y2MYBQMLggWRgCmapCGypDCapCAKoJIki1oaCkhyQJJkSkMDCkiSbOiSCUNAsnsaHsnyimd4JMtrvuGRLK/FJ1m+Ep9kFbk6a/G6ljx/dk8bZFFGhiRmusWf4kcBxQAAyuvAN+S9kwMAAAAASUVORK5CYII=" alt="发布视频">
                </div>
                <div class="link-title">
                    <p class="type-text">发布视频</p>
                </div>
            </a>
                </div>
            </div>
        </div>
    </div>
        
                        
        <div class="menu-user-box">
            
        <div class="user-avatar">
            <img src="static/picture/a6e.png" class="avatar-face w-h" alt="*********的头像">
            
        </div><div class="user-menu-wrap">
                <div class="user-menu-container box">
                    <div class="user-menu-content">
                        <div class="user-info-item qk-flex">
                            <div class="user-info qk-flex">
                                 <a href="/users/329">
                                    
        <div class="user-avatar">
            <img src="static/picture/a6e.png" class="avatar-face w-h" alt="*********的头像">
            
        </div>
                                </a>
                                <div class="user-name">
                                    <div class="user-info-name"><a target="_blank" class="user-name no-hover" href="/users/329">*********</a><span class="user-lv"><img src="static/picture/LV2.png" class="lv-img-icon" alt="1"></span></div>
                                    <div class="desc text-ellipsis">这个人很懒什么都没有留下</div>
                                </div>
                            </div>
                            <!--<a class="user-info-btn" href="/users/329">我的主页</a>-->
                        </div>
                        <div class="vip-panel-item qk-flex">
                            <a href="/vip">
                                <div class="vip-panel qk-flex">
                                    <div class="vip-panel-info qk-flex">
                                        <div class="vip-icon"><i class="ri-vip-crown-2-fill"></i></div>
                                        <div class="vip-name">会员</div>
                                        <div class="divider"></div>
                                        <div class="vip-expire-time">未开通</div>
                                    </div>
                                    <button class="vip-btn" @click.stop.prevent="payVip()">去开通</button>
                                </div>
                            </a>
                        </div>
                        <div class="user-assets-item">
                            <!--<div class="user-assets-title">我的资产</div>-->
                            <div class="user-assets qk-flex">
                                <a href="/account/assets" class="user-money-card" @click.stop.prevent="recharge('balance')">
                                    <div class="user-assets-name">余额<i class="ri-arrow-right-s-line"></i></div>
                                    <div class="user-assets-num">0</div>
                                    <div class="assets-icon"></div>
                                </a>
                                <a href="/account/assets" class="user-credit-card" @click.stop.prevent="recharge('credit')">
                                    <div class="user-assets-name">积分<i class="ri-arrow-right-s-line"></i></div>
                                    <div class="user-assets-num">241</div>
                                    <div class="assets-icon"></div>
                                </a>
                            </div>
                        </div>
                        <div class="links-item">
                            
            <a href="/account" class="link-item qk-flex" rel="nofollow">
                <div class="link-title qk-flex">
                    <i class="ri-user-line"></i>
                    <span>个人中心</span>
                </div>
                <i class="ri-arrow-right-s-line"></i>
            </a>
            <a href="/account/post" class="link-item qk-flex" rel="nofollow">
                <div class="link-title qk-flex">
                    <i class="ri-draft-line"></i>
                    <span>投稿管理</span>
                </div>
                <i class="ri-arrow-right-s-line"></i>
            </a>
            <a href="/account/settings" class="link-item qk-flex" rel="nofollow">
                <div class="link-title qk-flex">
                    <i class="ri-user-settings-line"></i>
                    <span>账号设置</span>
                </div>
                <i class="ri-arrow-right-s-line"></i>
            </a>
                        </div>
                        <div class="split-line"></div>
                        <div class="logout-item qk-flex" @click="loginOut()">
                            <i class="ri-logout-circle-r-line"></i>
                            <span>退出登录</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    </div>
                </div>
                </div>
                
            </header>
                
    <div id="content" class="site-content">
    
        
        <div class="sidebar-menu">
            <div class="sidebar-menu-inner">
                <div id="channel-menu" class="menu-%e8%8f%9c%e5%8d%952-container"><ul id="menu-%e8%8f%9c%e5%8d%952" class="menu"><li><a href=""><i class="ri-bard-line"></i>为你推荐</a></li>
<li><a href="#"><i class="ri-pages-line"></i>热门动态</a></li>
<li class="menu-sub-title"><span>快捷访问</span></li>
<li><a href="/search"><i class="ri-planet-line"></i>发现</a></li>
<li><a href="/circle"><i class="ri-donut-chart-line"></i>社区</a></li>
<li class="menu-sub-title"><span>主题相关</span></li>
<li><a href="/moe-theme"><i class="ri-file-text-line"></i>主题文档</a></li>
<li><a href="/qkua"><i class="ri-shopping-cart-line"></i>购买主题<b class="badge gradient">特惠</b></a></li>
<li><a href="/verify"><i class="ri-verified-badge-line"></i>认证<b class="badge">1.2.5</b></a></li>
</ul></div><div id="channel-menu-bottom" class="menu-%e5%b7%a6%e4%be%a7%e5%ba%95%e9%83%a8-container"><ul id="menu-%e5%b7%a6%e4%be%a7%e5%ba%95%e9%83%a8" class="menu"><li><a href="/account/product">授权管理与下载</a></li></ul>
        <div class="more-menu-container" @click.stop="">
            <div class="more-information" @click.stop="show = !show">
                <i class="ri-menu-line"></i>更多
            </div>
            <div class="more-menu-wrap" v-show="show" v-cloak="">
                <div class="box">
                    <div class="more-menu-links">
                        
            <a href="" rel="nofollow" class="no-hover">
                <span>关于Qkua主题</span>
                <i class="ri-arrow-right-s-line"></i>
            </a>
            <a href="" rel="nofollow" class="no-hover">
                <span>隐私、协议</span>
                <i class="ri-arrow-right-s-line"></i>
            </a>
                        <div class="menu-sub-title"><span>设置</span></div>
                        <div class="qk-flex">
                            <span>切换主题</span>
                            <div class="menu-theme-switch">
                                <label class="theme-toggle dark-mode">
                                    <input type="checkbox">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>
            </div>
            <div class="sidebar-menu-mask mobile-show" onclick="mobileMenu.showMenu()"></div>
        </div>        
        <div class="content-wrapper">
<div id="account" class="account wrapper">
    <div class="back-warp box mobile-show">
        <div class="back-box">
            <div class="back" onclick="history.back()"><i class="ri-arrow-left-s-line"></i></div>
            <div class="page-title">推广中心</div>
        </div>
    </div>
    <div class="account-header mg-b account-mobile-hidden">
        <div class="mask-wrapper" style="background-image: url(static/image/fill_w1440_h288_g0_mark_20201223055552.png);"></div>
        <div class="account-panel box">
            <div class="account-profile">
                <div class="left-user-info">
                    
        <div class="user-avatar">
            <img src="static/picture/a6e.png" class="avatar-face w-h" alt="*********的头像">
            
        </div>                    <div class="user-info">
                        <div class="user-info-name">
                            <span class="user-name">*********</span>
                                                            <span class="user-lv"><img src="static/picture/LV2.png" class="lv-img-icon"></span>
                                                                                    <!--<span class="user-auth">Qk主题官方</span>-->
                        </div>
                        <div class="desc text-ellipsis">
                            <div class="verify">
                                                                                                    <a href="/verify">
                                        暂未认证，去认证 <i class="ri-arrow-right-s-line"></i>
                                    </a>
                                                            </div>
                        </div>
                    </div>
                </div>
                <div class="right-user-action">
                    <a href="/users/329" class="profile-primary-button no-hover">个人主页</a>
                </div>
            </div>
        </div>
    </div>
    <!---#account-header----->
    <div class="account-page-content">
        <div class="account-page-left mg-r account-mobile-hidden">
            <div class="vip-card">
                <a class="vip-info qk-flex no-hover" href="/account/vip">
                    <div class="vip-name">会员</div>
                    <div class="vip-expire-time qk-flex">开通会员<i class="ri-arrow-right-s-line"></i></div>
                </a>
            </div>
            <div class="counts-item qk-flex">
                <a class="single-count-item" href="/users/329/dynamic">
                    <div class="count-num">0</div>
                    <div class="count-text">动态</div>
                </a>
                <a class="single-count-item" href="/users/329/comments">
                    <div class="count-num">1</div>
                    <div class="count-text">评论</div>
                </a>
                <a class="single-count-item" href="/users/329/favorite">
                    <div class="count-num">1</div>
                    <div class="count-text">收藏</div>
                </a>
                <a class="single-count-item" href="/users/329/fans/follow">
                    <div class="count-num">2</div>
                    <div class="count-text">关注</div>
                </a>
                <a class="single-count-item" href="/users/329/fans/fans">
                    <div class="count-num">0</div>
                    <div class="count-text">粉丝</div>
                </a>
            </div>
            <div class="user-assets-item">
                <div class="title">我的钱包</div>
                <div class="user-assets qk-flex">
                    <a href="/account/assets" class="user-money-card">
                        <div class="user-assets-name">余额<i class="ri-arrow-right-s-line"></i></div>
                        <div class="user-assets-num">0</div>
                    </a>
                    <a href="/account/assets" class="user-credit-card">
                        <div class="user-assets-name">积分<i class="ri-arrow-right-s-line"></i></div>
                        <div class="user-assets-num">241</div>
                    </a>
                </div>
            </div>
            <div class="distribution-card">
                <div class="title">推广返佣</div>
                <a href="">
                    <div class="income-info">
                        <div class="total">
                            <span class="money">
                                <span class="unit">￥</span>0                            </span>
                            <i class="ri-arrow-right-s-line"></i>
                        </div>
                        <div class="withdraw">
                            <div class="left">累计收益 
                                <span class="money">￥0</span>
                            </div>
                            <div class="right">已提现 
                                <span class="money">￥0</span>
                            </div>
                        </div>
                    </div>
                    <!--<div class="bar"><div class="bar-sub" style="width: 55.4776%;background-color: rgb(20, 196, 191);"></div><div class="bar-sub" style="width: 45.5224%; background-color: rgb(197, 242, 104);"></div><div class="bar-sub" style="width: 22%;background-color: rgb(97, 136, 255);"></div></div>-->
                    <!--<div class="income-count">-->
                    <!--    <div class="item">-->
                    <!--        <div class="item-line">-->
                    <!--            <div class="dot"></div>-->
                    <!--            <div class="name">一级收益</div>-->
                    <!--        </div>-->
                    <!--        <div class="count">￥84.90</div>-->
                    <!--    </div>-->
                    <!--    <div class="item">-->
                    <!--        <div class="item-line">-->
                    <!--            <div class="dot"></div>-->
                    <!--            <div class="name">二级收益</div>-->
                    <!--        </div>-->
                    <!--        <div class="count">￥84.90</div>-->
                    <!--    </div>-->
                    <!--    <div class="item">-->
                    <!--        <div class="item-line">-->
                    <!--            <div class="dot"></div>-->
                    <!--            <div class="name">三级收益</div>-->
                    <!--        </div>-->
                    <!--        <div class="count">￥84.90</div>-->
                    <!--    </div>-->
                    <!--</div>-->
                </a>
            </div>
            <div class="quick-panel">
                                    <a href="/account/growth" class="panel-item">
                        <i class="ri-pulse-line"></i>
                        <div>我的等级</div>
                    </a>
                                    <a href="/account/post" class="panel-item">
                        <i class="ri-draft-line"></i>
                        <div>投稿管理</div>
                    </a>
                                    <a href="/account/order" class="panel-item">
                        <i class="ri-file-list-3-line"></i>
                        <div>我的订单</div>
                    </a>
                                    <a href="/vip" class="panel-item">
                        <i class="ri-vip-crown-2-line"></i>
                        <div>会员中心</div>
                    </a>
                                    <a href="/account/task" class="panel-item">
                        <i class="ri-task-line"></i>
                        <div>任务中心</div>
                    </a>
                            </div>
            <div class="tab-links">
                                <a href="/account/product" class="link-item">
                    <div class="link-title qk-flex">
                        <i class="ri-shield-check-line"></i> 
                        <span>授权管理</span>
                    </div> 
                    <i class="ri-arrow-right-s-line"></i>
                </a>
                                <a href="/account/secure" class="link-item">
                    <div class="link-title qk-flex">
                        <i class="ri-settings-6-line"></i> 
                        <span>账号安全</span>
                    </div> 
                    <i class="ri-arrow-right-s-line"></i>
                </a>
                                <a href="/account/settings" class="link-item">
                    <div class="link-title qk-flex">
                        <i class="ri-user-settings-line"></i> 
                        <span>资料设置</span>
                    </div> 
                    <i class="ri-arrow-right-s-line"></i>
                </a>
                            </div>
            <!---#account-tabsbar----->
        </div>
        <div class="account-page-right ">
            <div class="distribution-page" ref="distributionPage">
    <div class="distribution-header box qk-radius">
        <div class="section-title">佣金收益数据</div>
        <div class="income-box">
            <div class="income-info">
                <div class="income-total">
                    <div class="name">总收益</div>
                    <div class="money">
                        <span class="unit">￥</span>0                    </div>
                    <div class="income-withdrawn">已提现 
                        <span class="money">￥0</span>
                    </div>
                </div>
                <div class="income-withdraw-amount">
                    <div class="name">可提现</div>
                    <div class="money">
                        <span class="unit">￥</span>0                    </div>
                </div>
                <div class="income-withdraw">
                                            <button onclick='createModal("withdrawal",{"loading":false,"keepAlive":false,"props":{"type":"commission","money":0,"ratio":"5","limit":"45"}})'>立即提现</button>
                                    </div>
            </div>
            <div class="income-count">
                <div class="item">
                    <div class="item-line">
                        <div class="dot"></div>
                        <div class="name">一级收益</div>
                    </div>
                    <div class="count">￥0</div>
                </div>
                <div class="item">
                    <div class="item-line">
                        <div class="dot"></div>
                        <div class="name">二级收益</div>
                    </div>
                    <div class="count">￥0</div>
                </div>
                <div class="item">
                    <div class="item-line">
                        <div class="dot"></div>
                        <div class="name">三级收益</div>
                    </div>
                    <div class="count">￥0</div>
                </div>
            </div>
        </div>
    </div>
    <div class="distribution-content box qk-radius" style=" flex: 1; ">
        <div id="tabs" class="tabs">
            <ul class="tabs-nav">
                <li class="active">推广详情</li>
                <li>佣金明细</li>
                <li>关联用户</li>
                <div class="active-bar"></div>
            </ul>
            <div class="tabs-content">
                <div class="tab-item article-content" v-if="index == 0">
                    <div class="distribution-info">
                        <div class="row">
                            <span>推广链接：</span>
                            <span class="bg-text" @click="copyText('?ref=329')">?ref=329</span>
                            <button class="bg-text" @click="copyText('?ref=329')">复制链接</button>
                            <!--<button>推广海报</button>-->
                        </div>
                        <div class="row">
                            <span>佣金比例：</span>
                            <table>
                                <thead>
                                    <tr>
                                        <td>分销等级</td>
                                        <td>一级关联</td>
                                        <td>二级关联</td>
                                        <td>三级关联</td>
                                    </tr>
                                </thead>
                                <tbody>
                                                                            <tr style="background: var(--bg-text-color); color: var(--color-primary);">
                                                                                <td>普通用户</td>
                                                                                <td>10%</td>
                                            <td>0%</td>
                                            <td>0%</td>
                                        </tr>
                                                                            <tr style="">
                                                                                <td>赞助会员</td>
                                                                                <td>20%</td>
                                            <td>10%</td>
                                            <td>5%</td>
                                        </tr>
                                                                            <tr style="">
                                                                                <td>高级会员</td>
                                                                                <td>20%</td>
                                            <td>5%</td>
                                            <td>0%</td>
                                        </tr>
                                                                            <tr style="">
                                                                                <td>专业会员</td>
                                                                                <td>20%</td>
                                            <td>10%</td>
                                            <td>6%</td>
                                        </tr>
                                                                    </tbody>
                            </table>
                        </div>
                        <div class="row">
                            <span>返佣产品：</span>
                            <table>
                                <thead>
                                    <tr>
                                        <td>返佣产品</td>
                                        
                                                                                                                                <td style="background: var(--bg-text-color); color: var(--color-primary);">普通用户</td>
                                                                                                                                    <td style="">赞助会员</td>
                                                                                                                            <td style="">高级会员</td>
                                                                                                                            <td style="">专业会员</td>
                                                                        </tr>
                                </thead>
                                <tbody>
                                                                            <tr>
                                            <td>产品购买</td>
                                                                                            <td style="background: var(--bg-text-color);">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                    </tr>
                                                                            <tr>
                                            <td>文章内购</td>
                                                                                            <td style="background: var(--bg-text-color);">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                    </tr>
                                                                            <tr>
                                            <td>资源下载</td>
                                                                                            <td style="background: var(--bg-text-color);">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                    </tr>
                                                                            <tr>
                                            <td>VIP购买</td>
                                                                                            <td style="background: var(--bg-text-color);">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                    </tr>
                                                                            <tr>
                                            <td>积分购买</td>
                                                                                            <td style="background: var(--bg-text-color);">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                    </tr>
                                                                            <tr>
                                            <td>视频购买</td>
                                                                                            <td style="background: var(--bg-text-color);">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                    </tr>
                                                                            <tr>
                                            <td>支付入圈</td>
                                                                                            <td style="background: var(--bg-text-color);">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-checkbox-circle-fill" style="color:green"></i>
                                                                                                    </td>
                                                                                    </tr>
                                                                            <tr>
                                            <td>认证</td>
                                                                                            <td style="background: var(--bg-text-color);">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                            <td style="">
                                                                                                            <i class="ri-close-circle-fill" style="color:red"></i>
                                                                                                    </td>
                                                                                    </tr>
                                                                    </tbody>
                            </table>
                        </div>
                        <div class="row">
                            <span>订单佣金：</span>
                            <span>(*级)关联用户订单付款金额 x (*级)佣金比例</span>
                        </div>
                    </div>
                    <div class="distribution-guide">
                        <div class="title">
                            <span>新手推广指南</span>
                        </div>
                        <div class="guide">
                            <p>一、获取推广链接：复制其推广链接或推广海报（登录后生成的分享链接都为你的推广链接），放在论坛/博客/QQ/微信/群聊等页面上吸引客户点击。</p>
                            <p>二、建立客户关联：全新客户通过点击您的推广链接进行注册/登录，即与您建立关联，关联期为永久。</p>
                            <p>三、推广有效订单：客户购买指定返佣产品即生成有效订单与推广佣金。</p>
                            <p>四、获得推广佣金：当佣金积累到50元之后，即可申请提现，申请提现后需后台人工处理，一般2-8小时，请耐心等待。</p>
                        </div>
                    </div>
                </div>
                <div class="tab-item" v-else-if="index == 1 && orderList.length" v-cloak="">
                    <div class="record-list" v-cloak="">
                        <div class="record-item" v-for="(item,index) in orderList">
                            <div class="record-type">
                                <div class="record-title">{{item.type_text}}</div>
                                <div class="record-value money" :class="[{red:item.value < 0}]"><b v-text="item.value > 0 ?'+' + item.value : item.value"></b> 元</div>
                            </div>
                            <div class="record-detail">
                                <div class="record-desc" v-text="item.content"></div>
                                <div class="record-date" v-text="item.date"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-item" v-else-if="index == 2 && userList.length" v-cloak="">
                    <ul class="user-list">
                        <li class="list-item" v-for="(item,index) in userList" :key="index">
                            <a :href="item.link" v-html="item.avatar_html"></a>
                            <div class="user-info">
                                <div class="user-info-name" v-html="item.name_html"></div>
                                <div class="desc" v-text="item.partner_lv"></div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="loading empty qk-radius" v-else-if="!data.length && loading && !isDataEmpty"></div>
                <template v-else-if="!data.length && isDataEmpty">
                    
    <div class="empty qk-radius box">
        <img src="static/picture/empty.svg" class="empty-img"> 
        <p class="empty-text">暂无记录</p>
    </div>                </template>
            </div>
            <template v-if="index != 0">
                <div class="qk-pagenav json-nav">
        <page-nav ref="jsonPageNav" paged="1" pages="1" navtype="json" type="page" :selector="selector" :api="api" :param="param" @change="change"></page-nav>
    </div>            </template>
        </div>
    </div>
</div>
<style>

/*.tabs-content .empty {*/
/*    min-height: 100%; */
/*}*/

.distribution-page > div{
    padding: 16px;
}

.distribution-page .income-info {
    padding: 20px;
    background: var(--bg-muted-color);
    border-radius: var(--radius);
    display: flex;
    justify-content: space-between;
    color: var(--color-text-secondary);
    font-size: 14px;
    line-height: 14px;
    gap: 24px;
}

.income-withdraw {
    flex: 4;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.income-total {
    flex: 2;
}

.distribution-page .income-info .name {
}

.distribution-page .income-info > * > .money {
    font-size: 24px;
    font-weight: 600;
    color: var(--color-text-primary);
    line-height: 24px;
    margin: 16px 0;
}

.distribution-page .income-info .money .unit {
    font-size: 18px;
}

.distribution-page .income-count .item .count {
    font-size: 16px;
    line-height: 16px;
    font-weight: 600;
}

.distribution-page .income-box .income-count .item {
    background: none;
}

.distribution-page .income-count {
    max-width: 456px;
}

.distribution-page .income-count .item .item-line {
}

.distribution-info {
    display: flex;
    flex-direction: column;
    grid-gap: 20px;
    padding-top: 12px;
}

.distribution-info table td {
    text-align: center;
}
.distribution-info .row > span:first-of-type {
    color: var(--color-text-primary);
    font-size: 15px;
    font-weight: 600;
}

.distribution-info span.bg-text {
    padding: 5px 12px;
    border-radius: var(--radius);
}

.distribution-guide .title {
    display: inline-flex;
    box-sizing: border-box;
    width: 126px;
    height: 30px;
    position: relative;
    align-items: center;
    background-image: url(static/image/img-cps-guide.png);
    background-size: 100%;
    padding-left: 42px;
    color: var(--color-white);
    font-size: 13px;
    margin-bottom: 16px;
}

.distribution-guide {
    margin-top: 20px;
}

.guide {
    font-size: 14px;
    line-height: 32px;
    color: var(--color-text-primary);
}

.row table {
    margin-top: 12px;
}

.user-list .list-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
}

.user-list .list-item + .list-item{
    border-top: 1px solid var(--border-color-base);
}

.user-list .user-avatar {
    --avatar-size: 42px;
}

.user-list .user-info {
    flex-grow: 1;
    margin: 0 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}

.user-list .user-info .desc {
    font-size: 12px;
    color: var(--color-text-secondary);
    line-height: 18px;
}

.distribution-info .row:first-of-type {
    display: inline-flex;
    align-items: center;
    flex-wrap: wrap;
    grid-gap: 12px;
}
</style>        </div>
    </div>
    <!---#account-page-content----->
</div>
<style>


@media screen and (max-width:768px){
    .account-page-content .account-page-left {
        width: 100%;
        margin: 0;
    }
    
    .account-mobile-hidden{
    	display: none !important;
    }
}

.income-info .total {
    font-size: 18px;
    font-weight: 600;
    color: #1a7af8;
    margin-bottom: 12px;
    line-height: 24px;
}

.income-info {
    margin-top: 12px;
}

.withdraw {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 13px;
    color: var(--color-text-secondary);
    line-height: 13px;
}

.total .unit {
    font-weight: 600;
    font-size: 14px;
}

.income-count {
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
    grid-gap: 8px;
}

span.money {
    color: var(--color-text-primary);
    display: inline-flex;
}

.right {
    cursor: pointer;
}

.income-count .dot {
    border-radius: 4px;
    width: 12px;
    height: 6px;
    margin-right: .17067rem;
    background-color: rgb(20, 196, 191);
}

.item-line {
    font-size: 12px;
    display: flex;
    align-items: flex-start;
    grid-gap: 2px;
    flex-direction: column;
}

.income-count .item {
    background: var(--bg-muted-color);
    padding: 8px;
    border-radius: var(--radius);
    color: var(--color-text-secondary);
    flex: 1;
}

.income-count .item .count {
    color: var(--color-text-primary);
    font-size: 13px;
    line-height: 13px;
    margin-top: 6px;
}

.income-count .item:nth-child(2) .dot {
    background-color: #c5f268;
}

.income-count .item:nth-child(3) .dot {
    background-color: #6188ff;
}

.bar {
    height: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    border-radius: var(--radius);
    margin-top: 12px;
}

.income-info .total .money {
    font-size: 18px;
    font-weight: 600;
    color: #1a7af8;
}

.income-info .total i {
    font-size: 18px;
    color: var(--color-text-secondary);
    line-height: 16px;
}

</style>
        </div>
            </div>
    <footer class="footer">
        <div class="box">
            <div class="wrapper">
                                                <div class="footer-nav">
                    <ul class="footer-links">
                                        <li>友情链接：</li><li><a target="_blank" href="https://www.z4k.cn/">陌路小站</a></li><li><a target="_blank" href="https://www.godoublog.com/">GOdou社区</a></li></ul><ul class="footer-links">
                                        <li>合作伙伴：</li><li><a target="_blank" href="https://baidu.com">百度</a></li><li><a target="_blank" href="https://codestarframework.com/documentation/#/">codestarframework</a></li><li><a target="_blank" href="https://developer.wordpress.org/">wordpress</a></li></ul>                </div>
                <div class="footer-bottom">
                     <div class="footer-bottom-left">Copyright &copy; 2025<a href="" rel="home">&nbsp;qkua七夸主题</a>                        &nbsp;·&nbsp;<a rel="nofollow" target="__blank" href="https://beian.miit.gov.cn">蜀ICP备2024069728号-1</a>                        
                                            </div>
                    <div class="footer-bottom-rigth">
                        查询 53 次，耗时 0.1345 秒                    </div>
                </div>
            </div>
        </div>
    </footer>
    </div>

<script type='text/javascript' src='static/js/flickity.pkgd.min.js' id='flickity-js'></script>
<script type='text/javascript' src='static/js/fancybox.umd.js' id='fancybox-js'></script>
<script type='text/javascript' src='static/js/betterScroll.min.js' id='betterScroll-js'></script>
<script type='text/javascript' src='static/js/qrious.min.js' id='qrious-js'></script>
<script type='text/javascript' id='vue-js-extra'>
/* <![CDATA[ */
var qk_global = {"is_home":"","home_url":"","rest_url":"\/wp-json\/","site_name":"qkua\u4e03\u5938\u4e3b\u9898","login":"<div class=\"login-container\" v-if=\"login\">\n            <div class=\"container-top\">\n                <div class=\"title\" v-text=\"loginTitle\"><\/div>\n                <p style=\" font-size: 14px; color: var(--color-text-secondary); margin-top: 12px; \" v-if=\"loginType == 2 && login.invite_type != 0 && !invitePass\">\u6ca1\u6709\u9080\u8bf7\u7801\uff1f<a href=\"\u83b7\u53d6\u9080\u8bf7\u7801\u5730\u5740\" class=\"active\">\u83b7\u53d6\u9080\u8bf7\u7801<\/a><\/p>\n            <\/div>\n            <div class=\"container-content form-container\">\n                <form @submit.stop.prevent=\"loginSubmit\">\n                    <div class=\"invite-box\" v-if=\"loginType == 2 && login.invite_type != 0 && !invitePass\">\n                        <label class=\"form-item\">\n                            <input type=\"text\" name=\"invite_code\" v-model=\"data.invite_code\" tabindex=\"1\" spellcheck=\"false\" autocomplete=\"off\" placeholder=\"\u8bf7\u8f93\u5165\u9080\u8bf7\u7801\"> \n                        <\/label>\n                        <div class=\"form-button\">\n                            <button>\u63d0\u4ea4<\/button>\n                        <\/div>\n                        <div class=\"invite-skip\" v-if=\"login.invite_type == 2\"><span @click.stop.prevent=\"invitePass = true;\">\u8df3\u8fc7<\/span><\/div>\n                    <\/div>\n                    <div class=\"login-box\" v-else>\n                        <label class=\"form-item nickname\" v-show=\"loginType == 2\">\n                            <input type=\"text\" name=\"nickname\" v-model=\"data.nickname\" tabindex=\"1\" spellcheck=\"false\" autocomplete=\"off\" placeholder=\"\u8bf7\u8f93\u5165\u6635\u79f0\"> \n                        <\/label>\n                        <label class=\"form-item\">\n                            <input type=\"text\" name=\"username\" v-model=\"data.username\" tabindex=\"2\" spellcheck=\"false\" autocomplete=\"off\" placeholder=\"\u8bf7\u8f93\u5165\u90ae\u7bb1\"> \n                        <\/label>\n                        <label class=\"form-item\" v-show=\"loginType == 2 && data.username\" v-if=\"login.check_type\">\n                            <input type=\"text\" name=\"code\" v-model=\"data.code\" tabindex=\"3\" spellcheck=\"false\" autocomplete=\"off\" placeholder=\"\u8bf7\u8f93\u5165\u9a8c\u8bc1\u7801\"> \n                            <div class=\"login-eye text\" @click.stop.prevent=\"countdown == 60 ? getCode() : null\">{{countdown < 60 ? countdown+'\u79d2\u540e\u53ef\u91cd\u53d1' : '\u53d1\u9001\u9a8c\u8bc1\u7801'}}<\/div>\n                        <\/label>\n                        <label class=\"form-item\">\n                            <input type=\"password\" name=\"password\" v-model=\"data.password\" tabindex=\"4\" autocomplete=\"off\" spellcheck=\"false\" placeholder=\"\u8bf7\u8f93\u5165\u5bc6\u7801\">\n                        <\/label>\n                        <div class=\"signin-hint\">\n                            <div class=\"hint-text\" v-if=\"loginType == 1 && login.allow_register == 1\">\u8fd8\u6ca1\u6709\u8d26\u53f7? <span @click=\"loginType = 2\">\u524d\u5f80\u6ce8\u518c<\/span><\/div>\n                            <div class=\"hint-text\" v-if=\"loginType == 2\">\u5df2\u6709\u5e10\u53f7? <span @click=\"loginType = 1\">\u7acb\u5373\u767b\u5f55<\/span><\/div>\n                            <a href=\"\/forgot\" target=\"_blank\" class=\"forget-password\" v-if=\"loginType == 1\">\u5fd8\u8bb0\u5bc6\u7801\uff1f<\/a>\n                        <\/div>\n                        <div class=\"form-button\">\n                            <button v-text=\"buttonText\"><\/button>\n                        <\/div>\n                    <\/div>\n                <\/form>\n            <\/div>\n            <div class=\"social-loginbar\" v-if=\"!(loginType == 2 && login.invite_type != 0 && !invitePass)\">\n                <div class=\"separator-text\" v-if=\"Object.keys(oauths).length\">\u6216<\/div>\n                <div class=\"other-login\" v-if=\"Object.keys(oauths).length\">\n                    <a href=\"javascript:void(0)\" class=\"no-hover\" :class=\"item.type\" v-for=\" (item,index) in oauths\"  @click=\"socialLogin(index)\"  :key=\"item.type\"><i :class=\"item.icon\"><\/i><\/a>\n                <\/div>\n                <div class=\"agreement\" v-show=\"loginType == 1\">\u767b\u5f55\u8868\u793a\u60a8\u5df2\u9605\u8bfb\u5e76\u540c\u610f<span><a href=\"https:\/\/wiki.cosz.com.cn\/\" target=\"_blank\">\u7528\u6237\u534f\u8bae<\/a><\/span>\u548c<span><a href=\"https:\/\/wiki.cosz.com.cn\/\" target=\"_blank\">\u9690\u79c1\u653f\u7b56<\/a><\/span><\/div>\n            <\/div>\n        <\/div>","post_id":"0","author_id":"0","highlightjs_theme":null,"highlightjs_show":null,"password_verify":null,"product":"qkua"};
/* ]]> */
</script>
<script type='text/javascript' src='static/js/vue.min.js' id='vue-js'></script>
<script type='text/javascript' src='static/js/axios.min.js' id='axios-js'></script>
<script type='text/javascript' src='static/js/packery.pkgd.min.js' id='packery-js'></script>
<script type='text/javascript' src='static/js/lazyload.min.js' id='lazyload-js'></script>
<script type='text/javascript' src='static/js/autosize.min.js' id='autosize-js'></script>
<script type='text/javascript' src='static/js/vue-scrollto.js' id='vue-scrollto-js'></script>
<script type='text/javascript' src='static/js/main.min.js' id='qk-main-js'></script>
<script type='text/javascript' id='qk-account-js-extra'>
/* <![CDATA[ */
var qk_account = {"author_id":"329"};
/* ]]> */
</script>
<script type='text/javascript' src='static/js/account.js' id='qk-account-js'></script>
<script type='text/javascript' src='static/js/child.js' id='qk-child-js'></script>


</body>
</html>