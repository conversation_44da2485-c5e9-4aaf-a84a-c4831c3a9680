import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// 导入仪表盘样式系统（用户和管理员通用）
import './assets/dashboard/style-1.3.6.css'
import './assets/dashboard/remixicon.css'
// 导入全局样式
import './styles/main.css'

const app = createApp(App)

// 安装插件
const pinia = createPinia()
app.use(pinia)
app.use(router)

// 基础错误处理
app.config.errorHandler = (error: unknown, instance, info: string) => {
  console.error('Vue Error:', error)
  console.error('Component:', instance)
  console.error('Info:', info)
}

app.mount('#app')
