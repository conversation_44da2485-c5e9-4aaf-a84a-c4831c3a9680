-- API转发服务数据库用户权限配置
-- 创建不同权限的数据库用户

-- 1. 前端服务用户 (用户中心 + Admin)
CREATE USER frontend_service WITH PASSWORD 'frontend_secure_password';

-- 2. API核心服务用户 (模型转发服务)
CREATE USER api_service WITH PASSWORD 'api_secure_password';

-- 3. 只读用户 (监控、报表)
CREATE USER readonly_user WITH PASSWORD 'readonly_password';

-- 创建数据库
CREATE DATABASE xiangluai_api OWNER xiangluai;

-- 连接到业务数据库
\c xiangluai_api;

-- 创建 schema 分离不同业务模块
CREATE SCHEMA IF NOT EXISTS user_management;    -- 用户管理
CREATE SCHEMA IF NOT EXISTS api_management;     -- API管理  
CREATE SCHEMA IF NOT EXISTS billing;            -- 计费相关
CREATE SCHEMA IF NOT EXISTS analytics;          -- 统计分析
CREATE SCHEMA IF NOT EXISTS system;             -- 系统配置

-- 前端服务权限 (完整的用户管理权限)
GRANT USAGE ON SCHEMA user_management TO frontend_service;
GRANT USAGE ON SCHEMA api_management TO frontend_service;
GRANT USAGE ON SCHEMA billing TO frontend_service;
GRANT USAGE ON SCHEMA analytics TO frontend_service;
GRANT USAGE ON SCHEMA system TO frontend_service;

-- 前端服务可以完整操作用户相关数据
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA user_management TO frontend_service;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA api_management TO frontend_service;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA billing TO frontend_service;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA analytics TO frontend_service;
GRANT SELECT ON ALL TABLES IN SCHEMA system TO frontend_service;

-- API服务权限 (只需要验证和统计权限)
GRANT USAGE ON SCHEMA api_management TO api_service;
GRANT USAGE ON SCHEMA analytics TO api_service;
GRANT USAGE ON SCHEMA system TO api_service;

-- API服务只能读取API密钥，写入统计数据
GRANT SELECT ON ALL TABLES IN SCHEMA api_management TO api_service;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA analytics TO api_service;
GRANT SELECT ON ALL TABLES IN SCHEMA system TO api_service;

-- 只读用户权限 (监控和报表)
GRANT USAGE ON ALL SCHEMAS TO readonly_user;
GRANT SELECT ON ALL TABLES IN ALL SCHEMAS TO readonly_user;

-- 为新创建的表自动分配权限
ALTER DEFAULT PRIVILEGES IN SCHEMA user_management GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO frontend_service;
ALTER DEFAULT PRIVILEGES IN SCHEMA api_management GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO frontend_service;
ALTER DEFAULT PRIVILEGES IN SCHEMA billing GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO frontend_service;
ALTER DEFAULT PRIVILEGES IN SCHEMA analytics GRANT SELECT, INSERT, UPDATE ON TABLES TO frontend_service;

ALTER DEFAULT PRIVILEGES IN SCHEMA api_management GRANT SELECT ON TABLES TO api_service;
ALTER DEFAULT PRIVILEGES IN SCHEMA analytics GRANT SELECT, INSERT, UPDATE ON TABLES TO api_service;

ALTER DEFAULT PRIVILEGES IN ALL SCHEMAS GRANT SELECT ON TABLES TO readonly_user;

-- 序列权限
GRANT USAGE, SELECT ON ALL SEQUENCES IN ALL SCHEMAS TO frontend_service;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA analytics TO api_service;
GRANT SELECT ON ALL SEQUENCES IN ALL SCHEMAS TO readonly_user;

ALTER DEFAULT PRIVILEGES IN ALL SCHEMAS GRANT USAGE, SELECT ON SEQUENCES TO frontend_service;
ALTER DEFAULT PRIVILEGES IN SCHEMA analytics GRANT USAGE, SELECT ON SEQUENCES TO api_service;
ALTER DEFAULT PRIVILEGES IN ALL SCHEMAS GRANT SELECT ON SEQUENCES TO readonly_user;
