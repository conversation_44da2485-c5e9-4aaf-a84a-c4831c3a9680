const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/LoginView-Cii5uNDs.js","assets/vue-vendor-CKToUHZx.js","assets/element-plus-B8Fs_0jW.js","assets/vendor-BDiMbLwQ.js","assets/element-plus-CPnoEkWW.css","assets/LoginView-tn0RQdqM.css","assets/LogoTitle-BiOf3Vkp.css","assets/MainLayout-DFawLfK-.js","assets/toast-BvwA7Mwb.js","assets/LogoTitle-C8aMR_x8.js","assets/MainLayout-tWrOHYRR.css","assets/DashboardView-BFp10HLU.js","assets/chart-Cor9iTVD.js","assets/DashboardView-B39lq_zZ.css","assets/ApiKeysView-C8EOBhOD.js","assets/ApiKeysView-CMYLWV4F.css","assets/AccountsView-BNsdtc5P.js","assets/AccountsView-QM0EhO-E.css","assets/TutorialView-CVBo-wV5.js","assets/TutorialView-BM6fz9TT.css","assets/SettingsView-D3eCQqSW.js","assets/SettingsView-D9bl7XvV.css","assets/ApiStatsView-Ct43lQ4U.js","assets/ApiStatsView-C5BOZdu2.css"])))=>i.map(i=>d[i]);
import{aP as K,aQ as W,aR as F,r as _,c as L,q as R,V as D,I as q,z as h,x as b,ac as M,Q as G,a5 as N,y,C as S,L as x,P as T,Y as U,B as H,R as P,J,T as Q,O as X,av as Y,aM as Z,aS as ee}from"./vue-vendor-CKToUHZx.js";import{i as te,z as oe}from"./element-plus-B8Fs_0jW.js";import"./vendor-BDiMbLwQ.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))o(s);new MutationObserver(s=>{for(const a of s)if(a.type==="childList")for(const n of a.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&o(n)}).observe(document,{childList:!0,subtree:!0});function e(s){const a={};return s.integrity&&(a.integrity=s.integrity),s.referrerPolicy&&(a.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?a.credentials="include":s.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function o(s){if(s.ep)return;s.ep=!0;const a=e(s);fetch(s.href,a)}})();const se="modulepreload",ne=function(r){return"/admin-next/"+r},$={},w=function(t,e,o){let s=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),p=(n==null?void 0:n.nonce)||(n==null?void 0:n.getAttribute("nonce"));s=Promise.allSettled(e.map(c=>{if(c=ne(c),c in $)return;$[c]=!0;const l=c.endsWith(".css"),d=l?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${d}`))return;const i=document.createElement("link");if(i.rel=l?"stylesheet":se,l||(i.as="script"),i.crossOrigin="",i.href=c,p&&i.setAttribute("nonce",p),document.head.appendChild(i),l)return new Promise((m,f)=>{i.addEventListener("load",m),i.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${c}`)))})}))}function a(n){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=n,window.dispatchEvent(p),!p.defaultPrevented)throw n}return s.then(n=>{for(const p of n||[])p.status==="rejected"&&a(p.reason);return t().catch(a)})},V={basePath:"/admin-next/",apiPrefix:""};function ae(r=""){return r&&!r.startsWith("/")&&(r="/"+r),V.basePath+(r.startsWith("#")?r:"#"+r)}function re(){return ae("/login")}const ie=()=>w(()=>import("./LoginView-Cii5uNDs.js"),__vite__mapDeps([0,1,2,3,4,5,6])),E=()=>w(()=>import("./MainLayout-DFawLfK-.js"),__vite__mapDeps([7,1,8,9,6,2,3,4,10])),ce=()=>w(()=>import("./DashboardView-BFp10HLU.js"),__vite__mapDeps([11,2,1,3,4,8,12,13])),le=()=>w(()=>import("./ApiKeysView-C8EOBhOD.js"),__vite__mapDeps([14,2,1,3,4,8,15])),ue=()=>w(()=>import("./AccountsView-BNsdtc5P.js"),__vite__mapDeps([16,1,8,2,3,4,17])),de=()=>w(()=>import("./TutorialView-CVBo-wV5.js"),__vite__mapDeps([18,1,2,3,4,19])),fe=()=>w(()=>import("./SettingsView-D3eCQqSW.js"),__vite__mapDeps([20,1,8,2,3,4,21])),he=()=>w(()=>import("./ApiStatsView-Ct43lQ4U.js"),__vite__mapDeps([22,1,9,6,3,18,2,4,19,23])),me=[{path:"/",redirect:"/api-stats"},{path:"/login",name:"Login",component:ie,meta:{requiresAuth:!1}},{path:"/api-stats",name:"ApiStats",component:he,meta:{requiresAuth:!1}},{path:"/dashboard",component:E,meta:{requiresAuth:!0},children:[{path:"",name:"Dashboard",component:ce}]},{path:"/api-keys",component:E,meta:{requiresAuth:!0},children:[{path:"",name:"ApiKeys",component:le}]},{path:"/accounts",component:E,meta:{requiresAuth:!0},children:[{path:"",name:"Accounts",component:ue}]},{path:"/tutorial",component:E,meta:{requiresAuth:!0},children:[{path:"",name:"Tutorial",component:de}]},{path:"/settings",component:E,meta:{requiresAuth:!0},children:[{path:"",name:"Settings",component:fe}]}],C=K({history:W(V.basePath),routes:me});C.beforeEach((r,t,e)=>{const o=B();console.log("路由导航:",{to:r.path,from:t.path,requiresAuth:r.meta.requiresAuth,isAuthenticated:o.isAuthenticated}),r.path==="/api-stats"||r.path.startsWith("/api-stats")?e():r.meta.requiresAuth&&!o.isAuthenticated?e("/login"):r.path==="/login"&&o.isAuthenticated?e("/dashboard"):e()});const z=V.apiPrefix;function A(r){return r.startsWith("/")||(r="/"+r),z+r}class pe{constructor(){this.baseURL=z}getAuthToken(){return localStorage.getItem("authToken")||null}buildConfig(t={}){const e={headers:{"Content-Type":"application/json",...t.headers},...t},o=this.getAuthToken();return o&&(e.headers.Authorization=`Bearer ${o}`),e}async handleResponse(t){if(t.status===401){const o=window.location.pathname+window.location.hash;throw o.includes("/login")||o.endsWith("/")||(localStorage.removeItem("authToken"),window.location.href=re()),new Error("Unauthorized")}const e=t.headers.get("content-type");if(e&&e.includes("application/json")){const o=await t.json();if(!t.ok)throw new Error(o.message||`HTTP ${t.status}`);return o}if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);return t}async get(t,e={}){const o=A(t),s=this.buildConfig({...e,method:"GET"});try{const a=await fetch(o,s);return await this.handleResponse(a)}catch(a){throw console.error("API GET Error:",a),a}}async post(t,e=null,o={}){const s=A(t),a=this.buildConfig({...o,method:"POST",body:e?JSON.stringify(e):void 0});try{const n=await fetch(s,a);return await this.handleResponse(n)}catch(n){throw console.error("API POST Error:",n),n}}async put(t,e=null,o={}){const s=A(t),a=this.buildConfig({...o,method:"PUT",body:e?JSON.stringify(e):void 0});try{const n=await fetch(s,a);return await this.handleResponse(n)}catch(n){throw console.error("API PUT Error:",n),n}}async delete(t,e={}){const o=A(t),s=this.buildConfig({...e,method:"DELETE"});try{const a=await fetch(o,s);return await this.handleResponse(a)}catch(a){throw console.error("API DELETE Error:",a),a}}}const k=new pe,B=F("auth",()=>{const r=_(!1),t=_(localStorage.getItem("authToken")||""),e=_(""),o=_(""),s=_(!1),a=_({siteName:"Claude Relay Service",siteIcon:"",siteIconData:"",faviconData:""}),n=_(!0),p=L(()=>!!t.value&&r.value),c=L(()=>t.value),l=L(()=>({username:e.value}));async function d(u){s.value=!0,o.value="";try{const g=await k.post("/web/auth/login",u);g.success?(t.value=g.token,e.value=g.username||u.username,r.value=!0,localStorage.setItem("authToken",g.token),await C.push("/dashboard")):o.value=g.message||"登录失败"}catch(g){o.value=g.message||"登录失败，请检查用户名和密码"}finally{s.value=!1}}function i(){r.value=!1,t.value="",e.value="",localStorage.removeItem("authToken"),C.push("/login")}function m(){t.value&&(r.value=!0,f())}async function f(){try{const u=await k.get("/web/auth/user");u.success&&u.user&&(e.value=u.user.username),(await k.get("/admin/dashboard")).success||i()}catch{i()}}async function v(){n.value=!0;try{const u=await k.get("/admin/oem-settings");if(u.success&&u.data){if(a.value={...a.value,...u.data},u.data.siteIconData||u.data.siteIcon){const g=document.querySelector("link[rel*='icon']")||document.createElement("link");g.type="image/x-icon",g.rel="shortcut icon",g.href=u.data.siteIconData||u.data.siteIcon,document.getElementsByTagName("head")[0].appendChild(g)}u.data.siteName&&(document.title=`${u.data.siteName} - 管理后台`)}}catch(u){console.error("加载OEM设置失败:",u)}finally{n.value=!1}}return{isLoggedIn:r,authToken:t,username:e,loginError:o,loginLoading:s,oemSettings:a,oemLoading:n,isAuthenticated:p,token:c,user:l,login:d,logout:i,checkAuth:m,loadOemSettings:v}}),O=(r,t)=>{const e=r.__vccOpts||r;for(const[o,s]of t)e[o]=s;return e},_e={class:"toast-container"},ge=["onClick"],ve={class:"toast-content"},ye={class:"toast-icon"},we={class:"toast-body"},be={key:0,class:"toast-title"},Te={class:"toast-message"},Ee=["onClick"],Ae={__name:"ToastNotification",setup(r,{expose:t}){const e=_([]);let o=0;const s=l=>{const d={success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"};return d[l]||d.info},a=(l,d="info",i=null,m=5e3)=>{const f=++o,v={id:f,message:l,type:d,title:i,duration:m,isVisible:!1};return e.value.push(v),setTimeout(()=>{v.isVisible=!0},10),m>0&&setTimeout(()=>{n(f)},m),f},n=l=>{const d=e.value.findIndex(i=>i.id===l);if(d>-1){const i=e.value[d];i.isVisible=!1,setTimeout(()=>{const m=e.value.findIndex(f=>f.id===l);m>-1&&e.value.splice(m,1)},300)}},p=()=>{e.value.forEach(l=>{l.isVisible=!1}),setTimeout(()=>{e.value.length=0},300)},c=(l,d="info",i=null,m=5e3)=>a(l,d,i,m);return R(()=>{window.showToast=c}),D(()=>{window.showToast===c&&delete window.showToast}),t({showToast:c,removeToast:n,clearAllToasts:p}),(l,d)=>(y(),q(N,{to:"body"},[h("div",_e,[(y(!0),b(G,null,M(e.value,i=>(y(),b("div",{key:i.id,class:S(["toast",`toast-${i.type}`,i.isVisible?"toast-show":"toast-hide"]),onClick:m=>n(i.id)},[h("div",ve,[h("div",ye,[h("i",{class:S(s(i.type))},null,2)]),h("div",we,[i.title?(y(),b("div",be,T(i.title),1)):x("",!0),h("div",Te,T(i.message),1)]),h("button",{class:"toast-close",onClick:U(m=>n(i.id),["stop"])},d[0]||(d[0]=[h("i",{class:"fas fa-times"},null,-1)]),8,Ee)]),i.duration>0?(y(),b("div",{key:0,class:"toast-progress",style:H({animationDuration:`${i.duration}ms`})},null,4)):x("",!0)],10,ge))),128))])]))}},ke=O(Ae,[["__scopeId","data-v-1a3ab19b"]]),Pe={class:"modal-content w-full max-w-md p-6 mx-auto"},xe={class:"flex items-start gap-4 mb-6"},Ce={class:"flex-1"},Ie={class:"text-lg font-semibold text-gray-900 mb-2"},Le={class:"text-gray-600 leading-relaxed whitespace-pre-line"},Se={class:"flex items-center justify-end gap-3"},Re=["disabled"],Ve=["disabled"],Oe={key:0,class:"loading-spinner mr-2"},$e={__name:"ConfirmDialog",setup(r,{expose:t}){const e=_(!1),o=_(!1),s=_(""),a=_(""),n=_("确认"),p=_("取消");let c=null;const l=(f,v,u="确认",g="取消")=>new Promise(j=>{s.value=f,a.value=v,n.value=u,p.value=g,e.value=!0,o.value=!1,c=j}),d=()=>{o.value||(o.value=!0,setTimeout(()=>{e.value=!1,o.value=!1,c&&(c(!0),c=null)},200))},i=()=>{o.value||(e.value=!1,c&&(c(!1),c=null))},m=f=>{e.value&&(f.key==="Escape"?i():f.key==="Enter"&&!f.shiftKey&&!f.ctrlKey&&!f.altKey&&d())};return R(()=>{window.showConfirm=l,document.addEventListener("keydown",m)}),D(()=>{window.showConfirm===l&&delete window.showConfirm,document.removeEventListener("keydown",m)}),t({showConfirm:l}),(f,v)=>(y(),q(N,{to:"body"},[P(Q,{name:"modal",appear:""},{default:J(()=>[e.value?(y(),b("div",{key:0,class:"fixed inset-0 modal z-[100] flex items-center justify-center p-4",onClick:U(i,["self"])},[h("div",Pe,[h("div",xe,[v[0]||(v[0]=h("div",{class:"flex-shrink-0 w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center"},[h("i",{class:"fas fa-exclamation-triangle text-white text-lg"})],-1)),h("div",Ce,[h("h3",Ie,T(s.value),1),h("div",Le,T(a.value),1)])]),h("div",Se,[h("button",{class:"btn bg-gray-100 text-gray-700 hover:bg-gray-200 px-6 py-3",disabled:o.value,onClick:i},T(p.value),9,Re),h("button",{class:S(["btn btn-warning px-6 py-3",{"opacity-50 cursor-not-allowed":o.value}]),disabled:o.value,onClick:d},[o.value?(y(),b("div",Oe)):x("",!0),X(" "+T(n.value),1)],10,Ve)])])])):x("",!0)]),_:1})]))}},De=O($e,[["__scopeId","data-v-bdb20ffd"]]),qe={id:"app"},Ne={__name:"App",setup(r){const t=B(),e=_(),o=_();return R(()=>{t.checkAuth(),t.loadOemSettings()}),(s,a)=>{const n=Y("router-view");return y(),b("div",qe,[P(n),P(ke,{ref_key:"toastRef",ref:e},null,512),P(De,{ref_key:"confirmRef",ref:o},null,512)])}}},Ue=O(Ne,[["__scopeId","data-v-5cba45f5"]]),I=Z(Ue),ze=ee();I.use(ze);I.use(C);I.use(te,{locale:oe});I.mount("#app");export{O as _,k as a,B as u};
