<template>
  <div class="antialiased text-body font-body classic-navbar overflow-x-hidden">
    <!-- PayPal Loading Modal -->
    <div class="paypal">
      <div class="paypal__content">
        <div class="paypal__spinner"></div>
        <h4 class="text-2xl font-semibold text-message">Redirecting to PayPal</h4>
      </div>
    </div>

    <!-- Main Content -->
    <div class="min-h-screen">
      <!-- Header Navigation -->
      <AppHeader />
      
      <!-- Hero Section -->
      <HeroSection />
      
      <!-- How It Works Section -->
      <WorkflowSection />
      
      <!-- Benefits Section -->
      <BenefitsSection />
      
      <!-- CTA Section -->
      <CtaSection />
      
      <!-- Pricing Section -->
      <PricingSection />
      
      <!-- Footer -->
      <AppFooter />
    </div>

    <!-- Video Modal -->
    <div class="hidden fixed z-50 inset-0 overflow-y-auto" id="modal-video">
      <div class="flex sm:block items-end justify-center min-h-screen px-4 py-16 text-center">
        <div class="fixed inset-0 close-video" aria-hidden="true">
          <div class="absolute inset-0 bg-brand-text opacity-60"></div>
        </div>

        <div class="relative inline-block align-bottom sm:my-12 sm:align-middle sm:max-w-3xl xl:max-w-4xl sm:w-full bg-white rounded-xl shadow-form transform transition-all" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
          <button class="absolute top-0 right-0 -mt-16 inline-block px-4 py-3 text-lg font-semibold leading-none bg-white hover:bg-blueGray-800 hover:text-white focus:outline-none rounded-4xl close-video">
            close
            <svg class="inline-block w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
          </button>
          <video poster="" :playsinline="true" :autoplay="true" :loop="true" src="" class="rounded-xl max-h-full"></video>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'
import HeroSection from '@/components/sections/HeroSection.vue'
import WorkflowSection from '@/components/sections/WorkflowSection.vue'
import BenefitsSection from '@/components/sections/BenefitsSection.vue'
import CtaSection from '@/components/sections/CtaSection.vue'
import PricingSection from '@/components/sections/PricingSection.vue'
import { useCopyContent, useEnergyAnimation, useVideoModal } from '@/composables/useInteractions'
import { useSmoothScroll } from '@/composables/useSmoothScroll'

// 初始化交互功能
useCopyContent()
useEnergyAnimation()
useVideoModal()
useSmoothScroll()
</script>

<style>
/* 引入原有的样式文件 */
@import url('/css/font.css');
@import url('/css/tailwindcss.65ca56eb.css');  
@import url('/css/shuffle-dev.9b4ff7f2.css');
@import url('/css/before-after.css');

/* 能量点动画 */
.energy-dot {
  animation: energy-flow 2s linear infinite;
}

@keyframes energy-flow {
  0% {
    transform: translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(800px);
    opacity: 0;
  }
}

/* 玻璃效果 */
.glass-effect {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* 复制按钮状态 */
.copy-content {
  position: relative;
}

.copy-content:hover button {
  display: block !important;
}

/* shadow-circle 阴影效果 */
.shadow-circle {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .energy-dot {
    display: none !important;
  }
}
</style>