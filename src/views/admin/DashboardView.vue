<template>
  <div class="p-6">
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">管理员仪表盘</h1>
      <p class="mt-1 text-sm text-gray-600">系统运行状态和关键指标概览</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div v-for="stat in stats" :key="stat.name" class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <component :is="stat.icon" class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  {{ stat.name }}
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    {{ stat.value }}
                  </div>
                  <p
                    v-if="stat.change"
                    :class="[
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600',
                      'ml-2 flex items-baseline text-sm font-semibold'
                    ]"
                  >
                    <component
                      :is="stat.changeType === 'increase' ? ArrowUpIcon : ArrowDownIcon"
                      class="self-center flex-shrink-0 h-5 w-5"
                      :class="[
                        stat.changeType === 'increase' ? 'text-green-500' : 'text-red-500'
                      ]"
                    />
                    <span class="sr-only">
                      {{ stat.changeType === 'increase' ? 'Increased' : 'Decreased' }} by
                    </span>
                    {{ stat.change }}
                  </p>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- API 调用趋势 -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">API 调用趋势</h3>
        <div class="h-64 flex items-center justify-center text-gray-400">
          <ChartBarIcon class="h-12 w-12" />
          <span class="ml-2">图表数据加载中...</span>
        </div>
      </div>

      <!-- 用户增长趋势 -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">用户增长趋势</h3>
        <div class="h-64 flex items-center justify-center text-gray-400">
          <ChartBarIcon class="h-12 w-12" />
          <span class="ml-2">图表数据加载中...</span>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="mt-8">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">最近活动</h3>
          <div class="flow-root">
            <ul class="-mb-8">
              <li v-for="(activity, activityIdx) in activities" :key="activity.id">
                <div class="relative pb-8">
                  <span
                    v-if="activityIdx !== activities.length - 1"
                    class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                    aria-hidden="true"
                  />
                  <div class="relative flex space-x-3">
                    <div>
                      <span
                        :class="[
                          activity.type === 'user' ? 'bg-blue-500' : 
                          activity.type === 'api' ? 'bg-green-500' :
                          activity.type === 'subscription' ? 'bg-purple-500' :
                          'bg-gray-400',
                          'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white'
                        ]"
                      >
                        <component
                          :is="
                            activity.type === 'user' ? UsersIcon :
                            activity.type === 'api' ? KeyIcon :
                            activity.type === 'subscription' ? CreditCardIcon :
                            ExclamationCircleIcon
                          "
                          class="h-5 w-5 text-white"
                        />
                      </span>
                    </div>
                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p class="text-sm text-gray-500">
                          {{ activity.content }}
                        </p>
                      </div>
                      <div class="text-right text-sm whitespace-nowrap text-gray-500">
                        {{ activity.time }}
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  UsersIcon,
  KeyIcon,
  ChartBarIcon,
  CreditCardIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ExclamationCircleIcon
} from '@heroicons/vue/24/outline'

// 统计数据
const stats = ref([
  { 
    name: '总用户数', 
    value: '12,485', 
    icon: UsersIcon,
    change: '12%',
    changeType: 'increase'
  },
  { 
    name: '活跃 API 密钥', 
    value: '8,735', 
    icon: KeyIcon,
    change: '8%',
    changeType: 'increase'
  },
  { 
    name: '今日 API 调用', 
    value: '1.2M', 
    icon: ChartBarIcon,
    change: '3%',
    changeType: 'decrease'
  },
  { 
    name: '付费订阅', 
    value: '3,842', 
    icon: CreditCardIcon,
    change: '15%',
    changeType: 'increase'
  }
])

// 最近活动
const activities = ref([
  {
    id: 1,
    type: 'user',
    content: '新用户注册: <EMAIL>',
    time: '5分钟前'
  },
  {
    id: 2,
    type: 'api',
    content: 'API密钥创建: sk_test_****3f4a',
    time: '10分钟前'
  },
  {
    id: 3,
    type: 'subscription',
    content: '用户升级到专业版套餐',
    time: '25分钟前'
  },
  {
    id: 4,
    type: 'user',
    content: '用户账户被禁用: <EMAIL>',
    time: '1小时前'
  },
  {
    id: 5,
    type: 'api',
    content: 'API调用限制触发: sk_test_****8b2c',
    time: '2小时前'
  }
])

onMounted(() => {
  // 这里可以添加实际的数据加载逻辑
  console.log('Admin dashboard mounted')
})
</script>