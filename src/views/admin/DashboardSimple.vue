<template>
  <div class="p-6">
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">管理员仪表盘</h1>
      <p class="mt-1 text-sm text-gray-600">系统运行状态和关键指标概览</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div v-for="stat in stats" :key="stat.name" class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-12 w-12 bg-gray-400 rounded-full flex items-center justify-center">
                <span class="text-2xl">{{ stat.icon }}</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  {{ stat.name }}
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    {{ stat.value }}
                  </div>
                  <p
                    v-if="stat.change"
                    :class="[
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600',
                      'ml-2 flex items-baseline text-sm font-semibold'
                    ]"
                  >
                    {{ stat.changeType === 'increase' ? '↑' : '↓' }}
                    {{ stat.change }}%
                  </p>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 简单的活动列表 -->
    <div class="mt-8 bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">最近活动</h3>
      <ul class="space-y-3">
        <li v-for="activity in activities" :key="activity.id" class="text-sm text-gray-600">
          • {{ activity.content }} - {{ activity.time }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 统计数据
const stats = ref([
  { 
    name: '总用户数', 
    value: '12,485', 
    icon: '👥',
    change: 12,
    changeType: 'increase'
  },
  { 
    name: '活跃 API 密钥', 
    value: '8,735', 
    icon: '🔑',
    change: 8,
    changeType: 'increase'
  },
  { 
    name: '今日 API 调用', 
    value: '1.2M', 
    icon: '📊',
    change: 3,
    changeType: 'decrease'
  },
  { 
    name: '付费订阅', 
    value: '3,842', 
    icon: '💳',
    change: 15,
    changeType: 'increase'
  }
])

// 最近活动
const activities = ref([
  {
    id: 1,
    content: '新用户注册: <EMAIL>',
    time: '5分钟前'
  },
  {
    id: 2,
    content: 'API密钥创建: sk_test_****3f4a',
    time: '10分钟前'
  },
  {
    id: 3,
    content: '用户升级到专业版套餐',
    time: '25分钟前'
  },
  {
    id: 4,
    content: '用户账户被禁用: <EMAIL>',
    time: '1小时前'
  },
  {
    id: 5,
    content: 'API调用限制触发: sk_test_****8b2c',
    time: '2小时前'
  }
])
</script>