// 错误处理工具函数
import { toast } from '@/composables/useToast'

// 错误码映射
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  CONFLICT_ERROR: 'CONFLICT_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const

// 错误消息
export const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_CODES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ERROR_CODES.VALIDATION_ERROR]: '输入数据格式错误',
  [ERROR_CODES.AUTHENTICATION_ERROR]: '身份验证失败，请重新登录',
  [ERROR_CODES.AUTHORIZATION_ERROR]: '权限不足，无法执行此操作',
  [ERROR_CODES.NOT_FOUND_ERROR]: '请求的资源不存在',
  [ERROR_CODES.CONFLICT_ERROR]: '数据冲突，请刷新后重试',
  [ERROR_CODES.RATE_LIMIT_ERROR]: '请求过于频繁，请稍后重试',
  [ERROR_CODES.SERVER_ERROR]: '服务器内部错误，请联系管理员',
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误，请稍后重试'
} as const

// 显示API错误提示
export const showApiError = (title: string, errorCode?: string, message?: string) => {
  const errorMessage = errorCode && ERROR_MESSAGES[errorCode as keyof typeof ERROR_MESSAGES] 
    ? ERROR_MESSAGES[errorCode as keyof typeof ERROR_MESSAGES]
    : message || '发生未知错误'
  
  toast.error(title, errorMessage)
}

// 显示成功提示
export const showApiSuccess = (title: string, message?: string) => {
  toast.success(title, message)
}

// HTTP状态码到错误码的映射
export const mapStatusToErrorCode = (status: number): string => {
  switch (status) {
    case 400:
      return ERROR_CODES.VALIDATION_ERROR
    case 401:
      return ERROR_CODES.AUTHENTICATION_ERROR
    case 403:
      return ERROR_CODES.AUTHORIZATION_ERROR
    case 404:
      return ERROR_CODES.NOT_FOUND_ERROR
    case 409:
      return ERROR_CODES.CONFLICT_ERROR
    case 429:
      return ERROR_CODES.RATE_LIMIT_ERROR
    case 408:
    case 504:
      return ERROR_CODES.TIMEOUT_ERROR
    case 500:
    case 502:
    case 503:
      return ERROR_CODES.SERVER_ERROR
    default:
      return ERROR_CODES.UNKNOWN_ERROR
  }
}