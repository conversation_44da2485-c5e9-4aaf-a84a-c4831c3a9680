<template>
  <div class="p-6">
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">API密钥管理</h1>
      <p class="mt-1 text-sm text-gray-600">查看和管理所有用户的API密钥</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-4">
      <div class="sm:col-span-2">
        <label for="search" class="sr-only">搜索API密钥</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
          </div>
          <input
            id="search"
            v-model="searchQuery"
            type="text"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="搜索密钥、用户邮箱..."
          >
        </div>
      </div>
      <div>
        <select
          v-model="filterStatus"
          class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        >
          <option value="">所有状态</option>
          <option value="active">活跃</option>
          <option value="revoked">已撤销</option>
          <option value="expired">已过期</option>
        </select>
      </div>
      <div>
        <select
          v-model="sortBy"
          class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        >
          <option value="created">创建时间</option>
          <option value="lastUsed">最后使用</option>
          <option value="usage">使用次数</option>
        </select>
      </div>
    </div>

    <!-- API密钥列表 -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              密钥 / 用户
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              使用情况
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              创建时间
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              最后使用
            </th>
            <th class="relative px-6 py-3">
              <span class="sr-only">操作</span>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="apiKey in filteredApiKeys" :key="apiKey.id">
            <td class="px-6 py-4 whitespace-nowrap">
              <div>
                <div class="text-sm font-medium text-gray-900">
                  {{ maskApiKey(apiKey.key) }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ apiKey.userEmail }}
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                :class="[
                  'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                  apiKey.status === 'active' ? 'bg-green-100 text-green-800' :
                  apiKey.status === 'revoked' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                ]"
              >
                {{ getStatusName(apiKey.status) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">
                <div>{{ apiKey.usage.toLocaleString() }} 次调用</div>
                <div class="text-xs text-gray-500">
                  限制: {{ apiKey.limit === -1 ? '无限制' : apiKey.limit.toLocaleString() + '/月' }}
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDate(apiKey.createdAt) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ apiKey.lastUsed ? formatDate(apiKey.lastUsed) : '从未使用' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <button
                v-if="apiKey.status === 'active'"
                @click="revokeApiKey(apiKey)"
                class="text-red-600 hover:text-red-900"
              >
                撤销
              </button>
              <button
                v-else
                @click="reactivateApiKey(apiKey)"
                class="text-green-600 hover:text-green-900"
              >
                恢复
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
      <div class="flex-1 flex justify-between sm:hidden">
        <button
          class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          上一页
        </button>
        <button
          class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          下一页
        </button>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            显示第
            <span class="font-medium">1</span>
            到
            <span class="font-medium">10</span>
            条，共
            <span class="font-medium">{{ totalApiKeys }}</span>
            条
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <button
              class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            >
              <span class="sr-only">Previous</span>
              <ChevronLeftIcon class="h-5 w-5" />
            </button>
            <button
              v-for="page in [1, 2, 3, 4, 5]"
              :key="page"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              {{ page }}
            </button>
            <button
              class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            >
              <span class="sr-only">Next</span>
              <ChevronRightIcon class="h-5 w-5" />
            </button>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@/utils/icons'

interface ApiKey {
  id: string
  key: string
  userEmail: string
  status: 'active' | 'revoked' | 'expired'
  usage: number
  limit: number
  createdAt: Date
  lastUsed?: Date
}

// 模拟API密钥数据
const apiKeys = ref<ApiKey[]>([
  {
    id: '1',
    key: '********************************',
    userEmail: '<EMAIL>',
    status: 'active',
    usage: 125420,
    limit: 1000000,
    createdAt: new Date('2024-12-01'),
    lastUsed: new Date('2025-01-30')
  },
  {
    id: '2',
    key: 'sk_test_8bC48KqMyjTEprktS2yen8ef',
    userEmail: '<EMAIL>',
    status: 'active',
    usage: 54300,
    limit: 100000,
    createdAt: new Date('2024-11-15'),
    lastUsed: new Date('2025-01-29')
  },
  {
    id: '3',
    key: '********************************',
    userEmail: '<EMAIL>',
    status: 'revoked',
    usage: 0,
    limit: 10000,
    createdAt: new Date('2024-10-20'),
    lastUsed: new Date('2024-12-01')
  },
  {
    id: '4',
    key: '********************************',
    userEmail: '<EMAIL>',
    status: 'active',
    usage: 892000,
    limit: -1,
    createdAt: new Date('2024-08-05'),
    lastUsed: new Date('2025-01-30')
  },
  {
    id: '5',
    key: 'sk_test_2bB72KtPbmWHsuxX5chr1ij',
    userEmail: '<EMAIL>',
    status: 'expired',
    usage: 10000,
    limit: 10000,
    createdAt: new Date('2024-06-01'),
    lastUsed: new Date('2024-12-31')
  },
  {
    id: '6',
    key: '*******************************',
    userEmail: '<EMAIL>',
    status: 'active',
    usage: 234567,
    limit: 500000,
    createdAt: new Date('2024-09-10'),
    lastUsed: new Date('2025-01-28')
  }
])

const searchQuery = ref('')
const filterStatus = ref('')
const sortBy = ref('created')
const totalApiKeys = computed(() => apiKeys.value.length)

const filteredApiKeys = computed(() => {
  let filtered = apiKeys.value.filter(apiKey => {
    const matchesSearch = !searchQuery.value || 
      apiKey.key.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      apiKey.userEmail.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesStatus = !filterStatus.value || apiKey.status === filterStatus.value
    
    return matchesSearch && matchesStatus
  })

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'lastUsed':
        return (b.lastUsed?.getTime() || 0) - (a.lastUsed?.getTime() || 0)
      case 'usage':
        return b.usage - a.usage
      default:
        return b.createdAt.getTime() - a.createdAt.getTime()
    }
  })

  return filtered
})

const maskApiKey = (key: string) => {
  return key.substring(0, 12) + '••••••••' + key.substring(key.length - 4)
}

const getStatusName = (status: string) => {
  const statusNames: Record<string, string> = {
    active: '活跃',
    revoked: '已撤销',
    expired: '已过期'
  }
  return statusNames[status] || status
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const revokeApiKey = (apiKey: ApiKey) => {
  if (confirm(`确定要撤销密钥 ${maskApiKey(apiKey.key)} 吗？`)) {
    apiKey.status = 'revoked'
  }
}

const reactivateApiKey = (apiKey: ApiKey) => {
  if (confirm(`确定要恢复密钥 ${maskApiKey(apiKey.key)} 吗？`)) {
    apiKey.status = 'active'
  }
}
</script>