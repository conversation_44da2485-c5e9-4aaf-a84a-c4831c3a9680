import { apiClient, type ApiResponse, type PaginatedResponse } from './client'
import type { DashboardOverview, UsageChartData } from '@/types/dashboard'

// 仪表板API接口
export const dashboardApi = {
  // 获取仪表板概览数据
  async getOverview(): Promise<DashboardOverview> {
    const response = await apiClient.get<ApiResponse<DashboardOverview>>('/dashboard/overview')
    return response.data
  },

  // 获取使用趋势数据
  async getUsageTrend(period: '7d' | '30d' | '90d' = '7d') {
    const response = await apiClient.get<ApiResponse<any>>(`/dashboard/usage-trend?period=${period}`)
    return response.data
  },

  // 获取实时统计
  async getRealTimeStats() {
    const response = await apiClient.get<ApiResponse<{
      activeUsers: number
      queuedRequests: number
      systemStatus: 'healthy' | 'warning' | 'error'
    }>>('/dashboard/realtime-stats')
    return response.data
  },

  // 获取图表数据
  async getUsageChartData(period: number): Promise<UsageChartData> {
    const response = await apiClient.get<ApiResponse<UsageChartData>>(`/dashboard/usage-chart?period=${period}`)
    return response.data
  },

  // 下载使用报告
  async downloadUsageReport(): Promise<void> {
    const response = await apiClient.get('/dashboard/usage-report', {
      responseType: 'blob'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    
    // 获取文件名
    const contentDisposition = response.headers['content-disposition']
    const fileName = contentDisposition 
      ? contentDisposition.split('filename=')[1]?.replace(/"/g, '') 
      : `usage-report-${new Date().toISOString().split('T')[0]}.pdf`
    
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  }
}