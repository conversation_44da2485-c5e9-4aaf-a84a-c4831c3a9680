import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { subscriptionApi } from '@/api'
import type { SubscriptionPlan, BillingRecord } from '@/types/dashboard'

export const useSubscriptionStore = defineStore('subscription', () => {
  // 状态
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 当前订阅信息
  const currentPlan = ref<SubscriptionPlan | null>(null)
  const availablePlans = ref<SubscriptionPlan[]>([])
  
  // 使用情况
  const usage = ref({
    currentUsage: 0,
    usageLimit: 0,
    usagePercentage: 0,
    resetDate: '',
    projectedUsage: 0,
    overage: null as { amount: number; cost: number } | null
  })

  // 账单信息
  const billingHistory = ref<BillingRecord[]>([])
  const billingPagination = ref({
    total: 0,
    page: 1,
    totalPages: 0
  })

  const upcomingInvoice = ref<{
    amount: number
    currency: string
    periodStart: string
    periodEnd: string
    items: Array<{
      description: string
      amount: number
      quantity: number
      unitPrice: number
    }>
    tax?: { amount: number; rate: number }
    discount?: { amount: number; coupon: string }
  } | null>(null)

  const billingSummary = ref({
    currentMonthSpent: 0,
    lastMonthSpent: 0,
    yearToDateSpent: 0,
    nextBillingDate: '',
    nextBillingAmount: 0,
    paymentStatus: 'current' as 'current' | 'past_due' | 'canceled'
  })

  // 支付方式
  const paymentMethods = ref<Array<{
    id: string
    type: 'card' | 'bank_account'
    last4: string
    brand?: string
    expiryMonth?: number
    expiryYear?: number
    isDefault: boolean
    createdAt: string
  }>>([])

  // 计算属性
  const hasActivePlan = computed(() => 
    currentPlan.value?.status === 'active'
  )

  const isPlanExpiringSoon = computed(() => {
    if (!currentPlan.value?.endDate) return false
    const daysUntilExpiry = Math.ceil(
      (new Date(currentPlan.value.endDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
    )
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0
  })

  const isOverUsage = computed(() => 
    usage.value.usagePercentage > 100
  )

  const usageWarningLevel = computed(() => {
    const percentage = usage.value.usagePercentage
    if (percentage >= 100) return 'danger'
    if (percentage >= 80) return 'warning'
    if (percentage >= 60) return 'info'
    return 'normal'
  })

  const recommendedPlan = computed(() => {
    if (!currentPlan.value || !availablePlans.value.length) return null
    
    const currentIndex = availablePlans.value.findIndex(p => p.id === currentPlan.value?.id)
    if (currentIndex === -1) return null
    
    // 如果使用率超过80%，推荐升级
    if (usage.value.usagePercentage > 80 && currentIndex < availablePlans.value.length - 1) {
      return availablePlans.value[currentIndex + 1]
    }
    
    return null
  })

  const hasOutstandingInvoices = computed(() => 
    billingHistory.value.some(bill => bill.status === 'pending' || bill.status === 'failed')
  )

  // 操作方法
  const fetchCurrentSubscription = async () => {
    isLoading.value = true
    error.value = null

    try {
      const data = await subscriptionApi.getCurrentSubscription()
      currentPlan.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取订阅信息失败'
      console.error('Failed to fetch current subscription:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchAvailablePlans = async () => {
    try {
      const data = await subscriptionApi.getAvailablePlans()
      availablePlans.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取可用套餐失败'
      console.error('Failed to fetch available plans:', err)
    }
  }

  const fetchSubscriptionUsage = async (period?: '7d' | '30d' | '90d') => {
    try {
      const data = await subscriptionApi.getSubscriptionUsage(period)
      usage.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取使用情况失败'
      console.error('Failed to fetch subscription usage:', err)
    }
  }

  const fetchBillingHistory = async (params?: {
    page?: number
    pageSize?: number
    status?: 'paid' | 'pending' | 'failed'
    startDate?: string
    endDate?: string
  }) => {
    try {
      const data = await subscriptionApi.getBillingHistory(params)
      billingHistory.value = data.items
      billingPagination.value = {
        total: data.total,
        page: data.page,
        totalPages: data.totalPages
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取账单历史失败'
      console.error('Failed to fetch billing history:', err)
    }
  }

  const fetchUpcomingInvoice = async () => {
    try {
      const data = await subscriptionApi.getUpcomingInvoice()
      upcomingInvoice.value = data
    } catch (err) {
      console.error('Failed to fetch upcoming invoice:', err)
      // 不设置错误，因为可能没有即将到来的发票
    }
  }

  const fetchBillingSummary = async () => {
    try {
      const data = await subscriptionApi.getBillingSummary()
      billingSummary.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取账单摘要失败'
      console.error('Failed to fetch billing summary:', err)
    }
  }

  const fetchPaymentMethods = async () => {
    try {
      const data = await subscriptionApi.getPaymentMethods()
      paymentMethods.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取支付方式失败'
      console.error('Failed to fetch payment methods:', err)
    }
  }

  // 订阅操作
  const createSubscription = async (data: {
    planId: string
    billingCycle: 'monthly' | 'annual'
    paymentMethodId?: string
    couponCode?: string
  }) => {
    isLoading.value = true
    error.value = null

    try {
      const result = await subscriptionApi.createSubscription(data)
      
      if (result.status === 'active') {
        // 刷新当前订阅信息
        await fetchCurrentSubscription()
      }
      
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建订阅失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateSubscription = async (data: {
    planId: string
    billingCycle?: 'monthly' | 'annual'
    immediateChange?: boolean
  }) => {
    isLoading.value = true
    error.value = null

    try {
      const updatedPlan = await subscriptionApi.updateSubscription(data)
      currentPlan.value = updatedPlan
      return updatedPlan
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新订阅失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const cancelSubscription = async (data: {
    reason?: string
    cancelAtPeriodEnd?: boolean
  }) => {
    isLoading.value = true
    error.value = null

    try {
      const result = await subscriptionApi.cancelSubscription(data)
      
      // 更新当前订阅状态
      if (currentPlan.value) {
        currentPlan.value.status = data.cancelAtPeriodEnd ? 'active' : 'cancelled'
      }
      
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '取消订阅失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const resumeSubscription = async () => {
    isLoading.value = true
    error.value = null

    try {
      const updatedPlan = await subscriptionApi.resumeSubscription()
      currentPlan.value = updatedPlan
      return updatedPlan
    } catch (err) {
      error.value = err instanceof Error ? err.message : '恢复订阅失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 支付相关操作
  const updatePaymentMethod = async (data: {
    paymentMethodId: string
    makeDefault?: boolean
  }) => {
    try {
      await subscriptionApi.updatePaymentMethod(data)
      await fetchPaymentMethods()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新支付方式失败'
      throw err
    }
  }

  const applyCoupon = async (couponCode: string) => {
    try {
      const result = await subscriptionApi.applyCoupon(couponCode)
      // 刷新即将到来的发票以显示折扣
      await fetchUpcomingInvoice()
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '应用优惠码失败'
      throw err
    }
  }

  const downloadInvoice = async (invoiceId: string) => {
    try {
      const blob = await subscriptionApi.downloadInvoice(invoiceId)
      
      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `invoice-${invoiceId}.pdf`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '下载发票失败'
      throw err
    }
  }

  // 兼容方法
  const fetchCurrentPlan = async () => {
    return fetchCurrentSubscription()
  }

  const changePlan = async (planId: string, billingCycle: 'monthly' | 'annual') => {
    return updateSubscription({ planId, billingCycle, immediateChange: true })
  }

  const downloadBillingHistory = async () => {
    try {
      const blob = await subscriptionApi.exportBillingHistory()
      
      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `billing-history-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '导出账单历史失败'
      throw err
    }
  }

  // 初始化数据
  const loadAllData = async () => {
    await Promise.all([
      fetchCurrentSubscription(),
      fetchAvailablePlans(),
      fetchSubscriptionUsage(),
      fetchBillingHistory({ pageSize: 10 }),
      fetchUpcomingInvoice(),
      fetchBillingSummary(),
      fetchPaymentMethods()
    ])
  }

  const refreshData = async () => {
    await loadAllData()
  }

  // 清除数据
  const clearData = () => {
    currentPlan.value = null
    availablePlans.value = []
    usage.value = {
      currentUsage: 0,
      usageLimit: 0,
      usagePercentage: 0,
      resetDate: '',
      projectedUsage: 0,
      overage: null
    }
    billingHistory.value = []
    upcomingInvoice.value = null
    billingSummary.value = {
      currentMonthSpent: 0,
      lastMonthSpent: 0,
      yearToDateSpent: 0,
      nextBillingDate: '',
      nextBillingAmount: 0,
      paymentStatus: 'current'
    }
    paymentMethods.value = []
    error.value = null
  }

  return {
    // 状态
    isLoading,
    error,
    currentPlan,
    availablePlans,
    usage,
    billingHistory,
    billingPagination,
    upcomingInvoice,
    billingSummary,
    paymentMethods,

    // 计算属性
    hasActivePlan,
    isPlanExpiringSoon,
    isOverUsage,
    usageWarningLevel,
    recommendedPlan,
    hasOutstandingInvoices,

    // 方法
    fetchCurrentSubscription,
    fetchAvailablePlans,
    fetchSubscriptionUsage,
    fetchBillingHistory,
    fetchUpcomingInvoice,
    fetchBillingSummary,
    fetchPaymentMethods,
    createSubscription,
    updateSubscription,
    cancelSubscription,
    resumeSubscription,
    updatePaymentMethod,
    applyCoupon,
    downloadInvoice,
    loadAllData,
    refreshData,
    clearData,
    
    // 兼容方法
    fetchCurrentPlan,
    changePlan,
    downloadBillingHistory
  }
})