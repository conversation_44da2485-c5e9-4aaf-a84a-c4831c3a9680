<template>
  <div class="status-code-chart">
    <div v-if="loading" class="chart-loading">
      <div class="loading-spinner"></div>
      <div>加载状态码数据...</div>
    </div>
    
    <div v-else-if="chartData.length === 0" class="chart-empty">
      <i class="ri-pie-chart-line"></i>
      <div>暂无状态码数据</div>
    </div>
    
    <div v-else class="chart-container">
      <div class="pie-chart">
        <svg :width="chartSize" :height="chartSize" class="pie-svg">
          <g :transform="`translate(${chartSize / 2}, ${chartSize / 2})`">
            <path
              v-for="(segment, index) in segments"
              :key="segment.code"
              :d="segment.path"
              :fill="segment.color"
              :stroke="'white'"
              :stroke-width="2"
              class="pie-segment"
              @mouseover="showTooltip($event, segment)"
              @mouseout="hideTooltip"
            />
          </g>
        </svg>
      </div>
      
      <div class="status-legend">
        <div 
          v-for="item in chartData"
          :key="item.code"
          class="legend-item"
        >
          <div class="legend-color" :style="{ backgroundColor: getStatusColor(item.code) }"></div>
          <div class="legend-info">
            <div class="status-code">{{ item.code }}</div>
            <div class="status-description">{{ getStatusDescription(item.code) }}</div>
            <div class="status-stats">
              <span class="count">{{ formatNumber(item.count) }}次</span>
              <span class="percentage">({{ item.percentage.toFixed(1) }}%)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 提示框 -->
    <div
      v-show="tooltipVisible"
      ref="tooltip"
      class="chart-tooltip"
      :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
    >
      <div class="tooltip-title">状态码 {{ tooltipData.code }}</div>
      <div class="tooltip-desc">{{ tooltipData.description }}</div>
      <div class="tooltip-stats">
        <div>次数: {{ tooltipData.count }}</div>
        <div>占比: {{ tooltipData.percentage }}%</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface StatusCodeData {
  code: number
  count: number
  percentage: number
}

interface Props {
  data: StatusCodeData[]
  loading: boolean
}

const props = defineProps<Props>()

const tooltipVisible = ref(false)
const tooltipX = ref(0)
const tooltipY = ref(0)
const tooltipData = ref<any>({})

const chartSize = 200
const radius = 80

const chartData = computed(() => props.data || [])

const segments = computed(() => {
  let currentAngle = 0
  
  return chartData.value.map(item => {
    const angle = (item.percentage / 100) * 2 * Math.PI
    const startAngle = currentAngle
    const endAngle = currentAngle + angle
    
    const x1 = Math.cos(startAngle) * radius
    const y1 = Math.sin(startAngle) * radius
    const x2 = Math.cos(endAngle) * radius
    const y2 = Math.sin(endAngle) * radius
    
    const largeArcFlag = angle > Math.PI ? 1 : 0
    
    const pathData = [
      `M 0 0`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ')
    
    currentAngle += angle
    
    return {
      code: item.code,
      count: item.count,
      percentage: item.percentage,
      path: pathData,
      color: getStatusColor(item.code),
      description: getStatusDescription(item.code)
    }
  })
})

const getStatusColor = (code: number): string => {
  if (code >= 200 && code < 300) return '#22c55e' // 绿色 - 成功
  if (code >= 300 && code < 400) return '#3b82f6' // 蓝色 - 重定向
  if (code >= 400 && code < 500) return '#f59e0b' // 橙色 - 客户端错误
  if (code >= 500) return '#ef4444' // 红色 - 服务器错误
  return '#9ca3af' // 灰色 - 其他
}

const getStatusDescription = (code: number): string => {
  const descriptions: { [key: number]: string } = {
    200: 'OK',
    201: 'Created',
    204: 'No Content',
    301: 'Moved Permanently',
    302: 'Found',
    304: 'Not Modified',
    400: 'Bad Request',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'Not Found',
    405: 'Method Not Allowed',
    429: 'Too Many Requests',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable',
    504: 'Gateway Timeout'
  }
  
  return descriptions[code] || `HTTP ${code}`
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
  return num.toString()
}

const showTooltip = (event: MouseEvent, segment: any) => {
  tooltipData.value = {
    code: segment.code,
    count: formatNumber(segment.count),
    percentage: segment.percentage.toFixed(1),
    description: segment.description
  }
  
  tooltipX.value = event.clientX + 10
  tooltipY.value = event.clientY - 10
  tooltipVisible.value = true
}

const hideTooltip = () => {
  tooltipVisible.value = false
}
</script>

<style scoped>
.status-code-chart {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-loading,
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color-base);
  border-top: 3px solid var(--theme-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-empty i {
  font-size: 48px;
  margin-bottom: 16px;
}

.chart-container {
  display: flex;
  align-items: center;
  gap: 32px;
  height: 100%;
}

.pie-chart {
  flex-shrink: 0;
}

.pie-svg {
  display: block;
}

.pie-segment {
  cursor: pointer;
  transition: opacity 0.2s;
}

.pie-segment:hover {
  opacity: 0.8;
}

.status-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: var(--radius);
  transition: background-color 0.2s;
}

.legend-item:hover {
  background-color: var(--bg-muted-color);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
}

.status-code {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 2px;
}

.status-description {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 4px;
}

.status-stats {
  display: flex;
  gap: 8px;
  font-size: 14px;
}

.count {
  font-weight: 600;
  color: var(--color-text-primary);
}

.percentage {
  color: var(--color-text-secondary);
}

.chart-tooltip {
  position: fixed;
  background: white;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  z-index: 1000;
  min-width: 150px;
}

.tooltip-title {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.tooltip-desc {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
}

.tooltip-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 14px;
  color: var(--color-text-regular);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-container {
    flex-direction: column;
    gap: 20px;
  }
  
  .status-legend {
    max-height: none;
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
</style>