<template>
  <div class="billing-view">
    <!-- 页面头部 -->
    <div class="page-header box qk-radius">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">账单详情</h1>
          <p class="page-description">管理您的账单信息和消费记录</p>
        </div>
        <div class="header-actions">
          <button class="btn btn-outline">
            <i class="ri-download-line"></i>
            导出账单
          </button>
          <button class="btn btn-primary">
            <i class="ri-add-line"></i>
            充值余额
          </button>
        </div>
      </div>
    </div>

    <!-- 账单概览 -->
    <div class="billing-overview">
      <div class="overview-cards">
        <div class="overview-card box qk-radius">
          <div class="card-icon primary">
            <i class="ri-money-dollar-circle-line"></i>
          </div>
          <div class="card-content">
            <div class="card-title">本月消费</div>
            <div class="card-value">￥{{ currentBilling.thisMonth.toFixed(2) }}</div>
            <div class="card-change negative">
              <i class="ri-arrow-up-line"></i>
              较上月增长 12.5%
            </div>
          </div>
        </div>

        <div class="overview-card box qk-radius">
          <div class="card-icon success">
            <i class="ri-wallet-line"></i>
          </div>
          <div class="card-content">
            <div class="card-title">账户余额</div>
            <div class="card-value">￥{{ currentBilling.balance.toFixed(2) }}</div>
            <div class="card-change" :class="{ 'warning': currentBilling.balance < 50 }">
              {{ currentBilling.balance < 50 ? '余额不足，建议充值' : '余额充足' }}
            </div>
          </div>
        </div>

        <div class="overview-card box qk-radius">
          <div class="card-icon warning">
            <i class="ri-time-line"></i>
          </div>
          <div class="card-content">
            <div class="card-title">未结算费用</div>
            <div class="card-value">￥{{ currentBilling.pending.toFixed(2) }}</div>
            <div class="card-change">
              预计 {{ getNextBillDate() }} 结算
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 账单历史 -->
    <div class="billing-history box qk-radius">
      <div class="section-header">
        <h3 class="section-title">交易记录</h3>
        <div class="section-controls">
          <div class="filter-select">
            <select v-model="selectedPeriod">
              <option value="7">最近7天</option>
              <option value="30">最近30天</option>
              <option value="90">最近90天</option>
            </select>
          </div>
        </div>
      </div>

      <div v-if="billingHistory.length === 0" class="empty-state">
        <i class="ri-bill-line"></i>
        <div class="empty-title">暂无交易记录</div>
        <div class="empty-description">您的交易记录将在这里显示</div>
      </div>

      <div v-else class="billing-table">
        <div class="table-header">
          <div class="table-col date-col">日期</div>
          <div class="table-col desc-col">描述</div>
          <div class="table-col type-col">类型</div>
          <div class="table-col amount-col">金额</div>
          <div class="table-col status-col">状态</div>
        </div>
        
        <div class="table-body">
          <div 
            v-for="bill in filteredBillingHistory" 
            :key="bill.id"
            class="table-row"
          >
            <div class="table-col date-col">
              <div class="date-display">
                <div class="date-main">{{ formatDateMain(bill.date) }}</div>
                <div class="date-time">{{ formatDateTime(bill.date) }}</div>
              </div>
            </div>
            
            <div class="table-col desc-col">
              <div class="transaction-info">
                <div class="transaction-title">{{ bill.description }}</div>
                <div class="transaction-id">交易ID: {{ bill.transactionId || generateTransactionId(bill.id) }}</div>
              </div>
            </div>
            
            <div class="table-col type-col">
              <div class="transaction-type" :class="bill.amount > 0 ? 'credit' : 'debit'">
                <i :class="bill.amount > 0 ? 'ri-arrow-down-line' : 'ri-arrow-up-line'"></i>
                {{ bill.amount > 0 ? '充值' : '消费' }}
              </div>
            </div>
            
            <div class="table-col amount-col">
              <div class="amount-display" :class="{ 'positive': bill.amount > 0, 'negative': bill.amount < 0 }">
                {{ bill.amount > 0 ? '+' : '' }}￥{{ Math.abs(bill.amount).toFixed(2) }}
              </div>
            </div>
            
            <div class="table-col status-col">
              <div class="status-badge success">
                <i class="ri-check-line"></i>
                已完成
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const selectedPeriod = ref(30)

// 模拟账单数据
const currentBilling = ref({
  thisMonth: 150.89,
  balance: 45.50,
  pending: 25.30
})

const billingHistory = ref([
  { 
    id: 1, 
    date: new Date('2024-01-26T14:30:00'), 
    description: 'API调用费用', 
    amount: -50.00,
    transactionId: 'TXN202401260001'
  },
  { 
    id: 2, 
    date: new Date('2024-01-25T10:15:00'), 
    description: '账户充值', 
    amount: 100.00,
    transactionId: 'TXN202401250001'
  },
  { 
    id: 3, 
    date: new Date('2024-01-24T16:45:00'), 
    description: 'API调用费用', 
    amount: -30.00,
    transactionId: 'TXN202401240001'
  },
  { 
    id: 4, 
    date: new Date('2024-01-22T09:00:00'), 
    description: '专业版套餐续费', 
    amount: -99.00,
    transactionId: 'TXN202401220001'
  },
  { 
    id: 5, 
    date: new Date('2024-01-20T13:20:00'), 
    description: 'API调用费用', 
    amount: -15.50,
    transactionId: 'TXN202401200001'
  },
  { 
    id: 6, 
    date: new Date('2024-01-18T11:10:00'), 
    description: '账户充值', 
    amount: 200.00,
    transactionId: 'TXN202401180001'
  }
])

const filteredBillingHistory = computed(() => {
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - selectedPeriod.value)
  
  return billingHistory.value.filter(bill => 
    new Date(bill.date) >= cutoffDate
  ).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
})

const formatDateMain = (date: Date): string => {
  return new Date(date).toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

const formatDateTime = (date: Date): string => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const generateTransactionId = (id: number): string => {
  const today = new Date()
  const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '')
  return `TXN${dateStr}${String(id).padStart(4, '0')}`
}

const getNextBillDate = (): string => {
  const nextMonth = new Date()
  nextMonth.setMonth(nextMonth.getMonth() + 1, 1)
  return nextMonth.toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric'
  })
}
</script>

<style scoped>
.billing-view {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 页面头部 */
.page-header {
  padding: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.page-description {
  color: var(--color-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  border: 1px solid transparent;
  border-radius: var(--radius);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  gap: 8px;
  font-size: 14px;
}

.btn-outline {
  background: white;
  border-color: var(--border-color-base);
  color: var(--color-text-primary);
}

.btn-outline:hover {
  border-color: var(--theme-color);
  color: var(--theme-color);
}

.btn-primary {
  background: var(--theme-color);
  color: white;
}

.btn-primary:hover {
  background: var(--theme-color-hover, var(--theme-color));
  transform: translateY(-1px);
}

/* 概览卡片 */
.billing-overview {
  margin-bottom: 8px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 24px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 28px;
}

.card-icon.primary {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.card-icon.success {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.card-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.card-change {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  gap: 4px;
}

.card-change.positive {
  color: #22c55e;
}

.card-change.negative {
  color: #ef4444;
}

.card-change.warning {
  color: #f59e0b;
}

/* 账单历史 */
.billing-history {
  padding: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.section-controls {
  display: flex;
  gap: 12px;
}

.filter-select select {
  padding: 8px 12px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  background: white;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--color-text-secondary);
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--color-text-primary);
}

.empty-description {
  font-size: 14px;
}

/* 表格样式 */
.billing-table {
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 120px 1fr 100px 120px 100px;
  gap: 16px;
  padding: 16px;
  background: var(--bg-muted-color);
  border-radius: var(--radius) var(--radius) 0 0;
  font-weight: 600;
  font-size: 14px;
  color: var(--color-text-primary);
}

.table-body {
  border: 1px solid var(--border-color-base);
  border-top: none;
  border-radius: 0 0 var(--radius) var(--radius);
}

.table-row {
  display: grid;
  grid-template-columns: 120px 1fr 100px 120px 100px;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid var(--border-color-base);
  transition: background-color 0.2s;
}

.table-row:hover {
  background: var(--bg-muted-color);
}

.table-row:last-child {
  border-bottom: none;
}

.table-col {
  display: flex;
  align-items: center;
}

/* 日期列 */
.date-display {
  display: flex;
  flex-direction: column;
}

.date-main {
  font-weight: 600;
  color: var(--color-text-primary);
  font-size: 14px;
}

.date-time {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: 2px;
}

/* 描述列 */
.transaction-info {
  display: flex;
  flex-direction: column;
}

.transaction-title {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 2px;
}

.transaction-id {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-family: 'Courier New', monospace;
}

/* 类型列 */
.transaction-type {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.transaction-type.credit {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.transaction-type.debit {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* 金额列 */
.amount-display {
  font-weight: 700;
  font-size: 16px;
}

.amount-display.positive {
  color: #22c55e;
}

.amount-display.negative {
  color: #ef4444;
}

/* 状态列 */
.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.success {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    flex-direction: column;
  }
  
  .overview-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .overview-card {
    padding: 20px;
  }
  
  .card-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
    margin-right: 16px;
  }
  
  .card-value {
    font-size: 24px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .table-col {
    justify-content: space-between;
    padding: 8px 0;
  }
  
  .table-col::before {
    content: attr(data-label);
    font-weight: 600;
    color: var(--color-text-secondary);
    font-size: 12px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .billing-view {
    gap: 16px;
  }
  
  .page-header,
  .billing-history {
    padding: 16px;
  }
  
  .overview-card {
    flex-direction: column;
    text-align: center;
  }
  
  .card-icon {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>