<template>
  <div class="stats-card" :class="`stats-card--${color}`">
    <div class="stats-card__icon">
      <i :class="icon"></i>
    </div>
    <div class="stats-card__content">
      <div class="stats-card__title">{{ title }}</div>
      <div class="stats-card__value">{{ value }}</div>
      <div v-if="subtitle" class="stats-card__subtitle">{{ subtitle }}</div>
      <div v-if="change !== undefined" class="stats-card__change" :class="changeClass">
        <i :class="changeIcon"></i>
        {{ Math.abs(change).toFixed(1) }}%
        <span class="change-text">{{ changeText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title: string
  value: string | number
  subtitle?: string
  change?: number
  icon: string
  color?: 'primary' | 'success' | 'warning' | 'info' | 'danger'
}

const props = withDefaults(defineProps<Props>(), {
  color: 'primary'
})

const changeClass = computed(() => {
  if (props.change === undefined) return ''
  return props.change >= 0 ? 'stats-card__change--positive' : 'stats-card__change--negative'
})

const changeIcon = computed(() => {
  if (props.change === undefined) return ''
  return props.change >= 0 ? 'ri-arrow-up-line' : 'ri-arrow-down-line'
})

const changeText = computed(() => {
  if (props.change === undefined) return ''
  return props.change >= 0 ? '较上月' : '较上月'
})
</script>

<style scoped>
.stats-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: var(--radius);
  border: 1px solid var(--border-color-base);
  transition: all 0.2s;
}

.stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stats-card__icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.stats-card--primary .stats-card__icon {
  background: var(--bg-text-color);
  color: var(--theme-color);
}

.stats-card--success .stats-card__icon {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.stats-card--warning .stats-card__icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.stats-card--info .stats-card__icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.stats-card--danger .stats-card__icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.stats-card__content {
  flex: 1;
}

.stats-card__title {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 4px;
}

.stats-card__value {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.stats-card__subtitle {
  font-size: 14px;
  color: var(--color-text-regular);
  margin-bottom: 8px;
}

.stats-card__change {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
}

.stats-card__change--positive {
  color: #22c55e;
}

.stats-card__change--negative {
  color: #ef4444;
}

.stats-card__change i {
  margin-right: 4px;
}

.change-text {
  font-weight: 400;
  color: var(--color-text-secondary);
  margin-left: 4px;
}
</style>