{"name": "xiangluai-vue", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3002", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "clean": "rm -rf node_modules/.vite && rm -rf dist"}, "dependencies": {"@heroicons/vue": "^2.2.0", "chart.js": "^4.5.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "~5.3.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}