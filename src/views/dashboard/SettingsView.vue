<template>
  <div class="settings-view">
    <div class="section-title">个人设置</div>
    
    <div class="settings-content box">
      <div class="user-avatar-section">
        <div class="user-avatar">
          <img :src="userProfile.avatar || '/src/assets/dashboard/picture/a6e.png'" alt="用户头像" />
        </div>
        <div class="avatar-actions">
          <button class="btn-secondary">更换头像</button>
        </div>
      </div>

      <form @submit.prevent="updateProfile" class="profile-form">
        <div class="form-group">
          <label>邮箱地址</label>
          <input 
            type="email" 
            v-model="userProfile.email" 
            readonly
            class="form-input"
          />
        </div>
        
        <div class="form-group">
          <label>昵称</label>
          <input 
            type="text" 
            v-model="userProfile.nickname" 
            class="form-input"
            placeholder="请输入昵称"
          />
        </div>
        
        <div class="form-group">
          <label>手机号</label>
          <input 
            type="tel" 
            v-model="userProfile.phone" 
            class="form-input"
            placeholder="请输入手机号"
          />
        </div>
        
        <div class="form-group">
          <label>个人简介</label>
          <textarea 
            v-model="userProfile.bio" 
            class="form-textarea"
            rows="4"
            placeholder="介绍一下自己..."
          ></textarea>
        </div>
        
        <div class="form-actions">
          <button type="submit" class="btn-primary">保存设置</button>
        </div>
      </form>
    </div>

    <div class="security-settings box">
      <div class="subsection-title">安全设置</div>
      
      <div class="security-item">
        <div class="security-info">
          <div class="security-title">修改密码</div>
          <div class="security-desc">定期修改密码有助于保护账户安全</div>
        </div>
        <button class="btn-secondary">修改</button>
      </div>
      
      <div class="security-item">
        <div class="security-info">
          <div class="security-title">两步验证</div>
          <div class="security-desc">为账户添加额外的安全保护</div>
        </div>
        <button class="btn-secondary">设置</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const userProfile = ref({
  email: authStore.user?.email || '',
  nickname: authStore.user?.nickname || '',
  phone: authStore.user?.phone || '',
  bio: authStore.user?.bio || '',
  avatar: authStore.user?.avatar || ''
})

const updateProfile = async () => {
  try {
    // 这里会调用实际的更新API
    console.log('Updating profile:', userProfile.value)
    // await authStore.updateProfile(userProfile.value)
    alert('设置已保存')
  } catch (error) {
    console.error('更新个人信息失败:', error)
    alert('保存失败，请重试')
  }
}
</script>

<style scoped>
.settings-view {
  display: flex;
  flex-direction: column;
  gap: var(--gap);
  padding: 16px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 16px;
}

.subsection-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 16px;
}

.settings-content {
  padding: 24px;
}

.user-avatar-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color-base);
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 500px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  color: var(--color-text-primary);
  font-weight: 500;
}

.form-input,
.form-textarea {
  padding: 12px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--radius);
  font-size: 14px;
  color: var(--color-text-primary);
  background-color: var(--bg-main-color);
}

.form-input:readonly {
  background-color: var(--bg-muted-color);
  color: var(--color-text-secondary);
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-actions {
  margin-top: 12px;
}

.security-settings {
  padding: 24px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color-base);
}

.security-item:last-child {
  border-bottom: none;
}

.security-title {
  font-size: 16px;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.security-desc {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: var(--radius);
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: color-mix(in srgb, var(--color-primary) 90%, black);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  padding: 8px 16px;
  border-radius: var(--radius);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background-color: var(--color-primary);
  color: white;
}
</style>