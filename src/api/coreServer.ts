// 前端与核心服务器的API接口
import { apiClient } from './client'

// 核心服务器基础URL
const CORE_SERVER_URL = import.meta.env.VITE_CORE_SERVER_URL || 'https://api-core.yourdomain.com'

// 服务间认证token（从环境变量获取）
const SERVICE_TOKEN = import.meta.env.VITE_SERVICE_TOKEN

// 创建核心服务器客户端
const coreApiClient = apiClient.create({
  baseURL: CORE_SERVER_URL,
  headers: {
    'X-Service-Token': SERVICE_TOKEN, // 服务间认证
    'Content-Type': 'application/json'
  }
})

// API Key相关接口
export interface CreateApiKeyRequest {
  userId: number
  planId: number
  subscriptionId: number
  keyName: string
  permissions?: string[]
}

export interface CreateApiKeyResponse {
  success: boolean
  data: {
    apiKey: string        // 完整的API Key（只返回一次）
    keyId: number         // API Key ID
    keyPrefix: string     // 显示用的前缀
    permissions: string[] // 权限列表
    rateLimit: {
      perMinute: number
      perDay: number
      perMonth: number
    }
    expiresAt?: string    // 过期时间
  }
  message?: string
}

export interface ApiKeyInfo {
  id: number
  keyPrefix: string
  name: string
  permissions: string[]
  rateLimit: {
    perMinute: number
    perDay: number
    perMonth: number
  }
  status: 'active' | 'suspended' | 'expired'
  usage: {
    today: number
    thisMonth: number
    totalCalls: number
  }
  createdAt: string
  lastUsedAt?: string
  expiresAt?: string
}

// 核心服务器API接口类
export class CoreServerApi {
  
  /**
   * 创建API Key（用户购买套餐后调用）
   */
  static async createApiKey(request: CreateApiKeyRequest): Promise<CreateApiKeyResponse> {
    try {
      const response = await coreApiClient.post('/api/v1/keys/create', {
        user_id: request.userId,
        plan_id: request.planId,
        subscription_id: request.subscriptionId,
        key_name: request.keyName,
        permissions: request.permissions || []
      })
      
      return response.data
    } catch (error: any) {
      console.error('创建API Key失败:', error)
      throw new Error(error.response?.data?.message || '创建API Key失败')
    }
  }

  /**
   * 获取用户的API Key列表
   */
  static async getUserApiKeys(userId: number): Promise<ApiKeyInfo[]> {
    try {
      const response = await coreApiClient.get(`/api/v1/keys/user/${userId}`)
      return response.data.data || []
    } catch (error: any) {
      console.error('获取API Key列表失败:', error)
      throw new Error(error.response?.data?.message || '获取API Key列表失败')
    }
  }

  /**
   * 更新API Key状态
   */
  static async updateApiKeyStatus(keyId: number, status: 'active' | 'suspended'): Promise<boolean> {
    try {
      const response = await coreApiClient.patch(`/api/v1/keys/${keyId}/status`, {
        status
      })
      return response.data.success
    } catch (error: any) {
      console.error('更新API Key状态失败:', error)
      throw new Error(error.response?.data?.message || '更新API Key状态失败')
    }
  }

  /**
   * 删除API Key
   */
  static async deleteApiKey(keyId: number): Promise<boolean> {
    try {
      const response = await coreApiClient.delete(`/api/v1/keys/${keyId}`)
      return response.data.success
    } catch (error: any) {
      console.error('删除API Key失败:', error)
      throw new Error(error.response?.data?.message || '删除API Key失败')
    }
  }

  /**
   * 重新生成API Key
   */
  static async regenerateApiKey(keyId: number): Promise<CreateApiKeyResponse> {
    try {
      const response = await coreApiClient.post(`/api/v1/keys/${keyId}/regenerate`)
      return response.data
    } catch (error: any) {
      console.error('重新生成API Key失败:', error)
      throw new Error(error.response?.data?.message || '重新生成API Key失败')
    }
  }

  /**
   * 获取API Key使用统计
   */
  static async getApiKeyUsage(keyId: number, days: number = 30): Promise<any> {
    try {
      const response = await coreApiClient.get(`/api/v1/keys/${keyId}/usage`, {
        params: { days }
      })
      return response.data.data
    } catch (error: any) {
      console.error('获取使用统计失败:', error)
      throw new Error(error.response?.data?.message || '获取使用统计失败')
    }
  }

  /**
   * 验证核心服务器连接状态
   */
  static async healthCheck(): Promise<boolean> {
    try {
      const response = await coreApiClient.get('/api/v1/health')
      return response.data.status === 'ok'
    } catch (error) {
      console.error('核心服务器连接失败:', error)
      return false
    }
  }

  /**
   * 获取支持的AI模型列表
   */
  static async getSupportedModels(): Promise<string[]> {
    try {
      const response = await coreApiClient.get('/api/v1/models')
      return response.data.data || []
    } catch (error: any) {
      console.error('获取模型列表失败:', error)
      return []
    }
  }
}

// 导出默认实例
export default CoreServerApi
