<template>
  <div class="space-y-6">
    <!-- 页面标题和时间范围选择 -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">使用统计</h2>
        <p class="mt-1 text-sm text-gray-600">查看您的API调用统计和性能分析</p>
      </div>
      <div class="flex items-center space-x-3">
        <select 
          v-model="selectedPeriod"
          @change="loadUsageData"
          class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="7d">最近7天</option>
          <option value="30d">最近30天</option>
          <option value="90d">最近90天</option>
        </select>
        <button 
          @click="exportData"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8a2 2 0 002-2V5a2 2 0 00-2-2H9a2 2 0 00-2 2v1m4 4l4-4m0 0l-4-4m4 4V8a2 2 0 00-2-2H7a2 2 0 00-2 2v3m4 4H8m4 0V8" />
          </svg>
          导出数据
        </button>
      </div>
    </div>

    <!-- 概览统计卡片 -->
    <div class="stats-grid stats-grid-2 stats-grid-4">
      <StatsCard
        title="总调用次数"
        :value="usageStats.totalRequests"
        icon="lightning"
        icon-color="blue"
        :trend="{ type: 'increase', value: 18, unit: '%' }"
        :progress="{ 
          percentage: (usageStats.totalRequests / 200000) * 100,
          current: usageStats.totalRequests,
          total: 200000
        }"
        format="number"
        description="API请求总数"
      />

      <StatsCard
        title="成功率"
        :value="usageStats.successRate"
        icon="chart"
        icon-color="emerald"
        :trend="{ type: 'increase', value: 0.3, unit: '%' }"
        format="percentage"
        description="请求成功率"
      />

      <StatsCard
        title="平均响应时间"
        :value="usageStats.avgResponseTime"
        icon="users"
        icon-color="amber"
        :trend="{ type: 'decrease', value: 5, unit: 'ms' }"
        :custom-format="(value) => `${value}ms`"
        description="API响应延迟"
      />

      <StatsCard
        title="错误次数"
        :value="usageStats.errorCount"
        icon="api"
        icon-color="red"
        :trend="{ type: 'decrease', value: 12, unit: '%' }"
        format="number"
        description="请求失败总数"
      />
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- 调用量趋势图 -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">调用量趋势</h3>
          <div class="flex space-x-2">
            <button 
              :class="[
                'px-3 py-1 text-xs font-medium rounded-md',
                chartType === 'requests' 
                  ? 'text-blue-600 bg-blue-50' 
                  : 'text-gray-500 hover:text-gray-700'
              ]"
              @click="chartType = 'requests'"
            >
              请求数
            </button>
            <button 
              :class="[
                'px-3 py-1 text-xs font-medium rounded-md',
                chartType === 'response_time' 
                  ? 'text-blue-600 bg-blue-50' 
                  : 'text-gray-500 hover:text-gray-700'
              ]"
              @click="chartType = 'response_time'"
            >
              响应时间
            </button>
          </div>
        </div>
        
        <!-- 简化的图表区域 -->
        <div class="h-64">
          <div class="h-full bg-gray-50 rounded-lg flex flex-col justify-end p-4">
            <div class="flex items-end justify-between h-full space-x-2">
              <div 
                v-for="(point, index) in chartData" 
                :key="index"
                class="bg-blue-500 rounded-t-sm flex-1 transition-all duration-300 hover:bg-blue-600"
                :style="{ height: getBarHeight(point) + '%' }"
                :title="`${point.date}: ${chartType === 'requests' ? point.requests : point.responseTime + 'ms'}`"
              ></div>
            </div>
            <div class="flex justify-between text-xs text-gray-500 mt-2">
              <span>{{ chartData[0]?.date }}</span>
              <span>{{ chartData[Math.floor(chartData.length / 2)]?.date }}</span>
              <span>{{ chartData[chartData.length - 1]?.date }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 状态码分布 -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">状态码分布</h3>
        <div class="space-y-4">
          <div v-for="status in statusCodes" :key="status.code" class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div 
                :class="[
                  'w-3 h-3 rounded-full',
                  status.code.startsWith('2') ? 'bg-green-500' :
                  status.code.startsWith('4') ? 'bg-yellow-500' :
                  'bg-red-500'
                ]"
              ></div>
              <span class="text-sm font-medium text-gray-900">{{ status.code }}</span>
              <span class="text-sm text-gray-500">{{ status.description }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-900">{{ status.count.toLocaleString() }}</span>
              <span class="text-xs text-gray-500">({{ status.percentage }}%)</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- API端点使用排行 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">热门API端点</h3>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">端点</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">调用次数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成功率</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平均响应时间</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后调用</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="endpoint in topEndpoints" :key="endpoint.path" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <span 
                    :class="[
                      'inline-flex px-2 py-1 text-xs font-mono rounded mr-3',
                      endpoint.method === 'GET' ? 'text-blue-800 bg-blue-100' :
                      endpoint.method === 'POST' ? 'text-green-800 bg-green-100' :
                      endpoint.method === 'PUT' ? 'text-yellow-800 bg-yellow-100' :
                      'text-red-800 bg-red-100'
                    ]"
                  >
                    {{ endpoint.method }}
                  </span>
                  <span class="text-sm font-medium text-gray-900">{{ endpoint.path }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ endpoint.calls.toLocaleString() }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                    <div 
                      class="bg-green-500 h-2 rounded-full" 
                      :style="{ width: endpoint.successRate + '%' }"
                    ></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">{{ endpoint.successRate }}%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ endpoint.avgResponseTime }}ms
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(endpoint.lastCall) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 错误日志 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">最近错误</h3>
        <button class="text-sm text-blue-600 hover:text-blue-800">查看全部</button>
      </div>
      <div class="divide-y divide-gray-200">
        <div v-for="error in recentErrors" :key="error.id" class="px-6 py-4">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2">
                <span 
                  :class="[
                    'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                    error.level === 'error' ? 'text-red-800 bg-red-100' :
                    error.level === 'warning' ? 'text-yellow-800 bg-yellow-100' :
                    'text-blue-800 bg-blue-100'
                  ]"
                >
                  {{ error.level.toUpperCase() }}
                </span>
                <span class="text-sm font-medium text-gray-900">{{ error.endpoint }}</span>
              </div>
              <p class="mt-1 text-sm text-gray-600">{{ error.message }}</p>
              <div class="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>状态码: {{ error.statusCode }}</span>
                <span>用户ID: {{ error.userId }}</span>
                <span>{{ formatDate(error.timestamp) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useUsageStore } from '@/stores/usage'
import type { UsageChartPoint } from '@/types/dashboard'
import StatsCard from '@/components/dashboard/cards/StatsCard.vue'

const usageStore = useUsageStore()

// 响应式数据
const selectedPeriod = ref('7d')
const chartType = ref<'requests' | 'response_time'>('requests')

// 统计数据
const usageStats = ref({
  totalRequests: 145682,
  successRate: 99.2,
  avgResponseTime: 157,
  errorCount: 1164
})

// 图表数据
const chartData = ref<UsageChartPoint[]>([])

// 状态码分布
const statusCodes = ref([
  { code: '200', description: '成功', count: 144518, percentage: 99.2 },
  { code: '400', description: '客户端错误', count: 856, percentage: 0.6 },
  { code: '401', description: '未授权', count: 203, percentage: 0.1 },
  { code: '500', description: '服务器错误', count: 105, percentage: 0.1 }
])

// 热门端点
const topEndpoints = ref([
  {
    method: 'GET',
    path: '/api/v1/chat/completions',
    calls: 89234,
    successRate: 99.5,
    avgResponseTime: 142,
    lastCall: new Date('2024-07-30T14:30:00')
  },
  {
    method: 'POST',
    path: '/api/v1/images/generations',
    calls: 34567,
    successRate: 98.8,
    avgResponseTime: 2341,
    lastCall: new Date('2024-07-30T14:25:00')
  },
  {
    method: 'GET',
    path: '/api/v1/models',
    calls: 12456,
    successRate: 100,
    avgResponseTime: 89,
    lastCall: new Date('2024-07-30T14:28:00')
  },
  {
    method: 'POST',
    path: '/api/v1/embeddings',
    calls: 9425,
    successRate: 99.1,
    avgResponseTime: 156,
    lastCall: new Date('2024-07-30T14:32:00')
  }
])

// 最近错误
const recentErrors = ref([
  {
    id: '1',
    level: 'error',
    endpoint: '/api/v1/chat/completions',
    message: 'API密钥已达到速率限制',
    statusCode: 429,
    userId: 'user_12345',
    timestamp: new Date('2024-07-30T14:25:00')
  },
  {
    id: '2',
    level: 'warning',
    endpoint: '/api/v1/images/generations',
    message: '请求超时，已自动重试',
    statusCode: 408,
    userId: 'user_67890',
    timestamp: new Date('2024-07-30T14:20:00')
  },
  {
    id: '3',
    level: 'error',
    endpoint: '/api/v1/completions',
    message: '无效的API密钥',
    statusCode: 401,
    userId: 'user_54321',
    timestamp: new Date('2024-07-30T14:15:00')
  }
])

// 计算属性
const getBarHeight = (point: UsageChartPoint) => {
  const values = chartData.value.map(p => 
    chartType.value === 'requests' ? p.requests : p.responseTime
  )
  const max = Math.max(...values)
  const value = chartType.value === 'requests' ? point.requests : point.responseTime
  return (value / max) * 100
}

// 工具函数
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// API 操作
const loadUsageData = () => {
  // 模拟加载数据
  const days = selectedPeriod.value === '7d' ? 7 : selectedPeriod.value === '30d' ? 30 : 90
  chartData.value = Array.from({ length: days }, (_, i) => {
    const date = new Date(Date.now() - (days - i - 1) * 24 * 60 * 60 * 1000)
    const requests = Math.floor(Math.random() * 5000) + 1000
    const errors = Math.floor(Math.random() * 50) + 5
    return {
      date: date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }),
      timestamp: date.getTime(),
      requests,
      errors,
      responseTime: Math.floor(Math.random() * 100) + 100,
      errorRate: (errors / requests) * 100
    }
  })
}

const exportData = () => {
  // 实现数据导出功能
  const data = {
    period: selectedPeriod.value,
    stats: usageStats.value,
    chartData: chartData.value,
    endpoints: topEndpoints.value,
    errors: recentErrors.value
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `usage-stats-${selectedPeriod.value}-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 生命周期管理
onMounted(() => {
  loadUsageData()
  // 启动实时数据更新
  usageStore.startRealTimeUpdates()
})

onUnmounted(() => {
  // 清理定时器和资源
  usageStore.stopRealTimeUpdates()
})
</script>